<template>
  <!-- 半圆分类导航 -->
  <div>
    <div class="half-circle-tabs">
      <div v-for="(item, index) in categoryItems" :key="index" class="half-circle-tab">
        <div class="tab-container">
          <div class="color-line" :class="item.color"></div>
          <div class="half-circle">
            <v-icon class="half-circle-icon" :class="item.color">{{ item.icon }}</v-icon>
          </div>
          <div class="tab-name">{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

// 类别项数据
const categoryItems = ref([
  { name: '控制器数', icon: 'mdi-calculator', color: 'blue' },
  { name: '摄像头数', icon: 'mdi-image', color: 'red' },
  { name: '交换机数', icon: 'mdi-swap-horizontal', color: 'green' },
  { name: '会议室数', icon: 'mdi-message-text', color: 'purple' },
  { name: '探测器1.2', icon: 'mdi-database', color: 'orange' }
]);

// 第二行类别项
const secondRowItems = ref([
  { name: '申请单数', icon: 'mdi-file-document', color: 'blue' },
  { name: '控制器数', icon: 'mdi-cog', color: 'red' },
  { name: '摄像头数', icon: 'mdi-camera', color: 'green' },
  { name: '交换机数', icon: 'mdi-swap-horizontal', color: 'purple' },
  { name: '会议室数', icon: 'mdi-forum', color: 'orange' }
]);

// 暴露给父组件使用
defineExpose({
  categoryItems,
  secondRowItems
});
</script>

<style scoped>
/* 最类似图片中的半圆侧边导航样式 */
.half-circle-tabs {
  display: flex;
  justify-content: space-around;
  margin: 20px auto;
  width: 100%;
  max-width: 800px;
  overflow-x: auto;
  padding: 0 20px;
  scrollbar-width: none; /* Firefox */
}

.half-circle-tabs::-webkit-scrollbar {
  display: none; /* Chrome, Safari */
}

.half-circle-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 10px;
  flex: 0 0 auto;
  position: relative;
  cursor: pointer;
  width: 70px;
  transition: transform 0.3s ease;
}

.half-circle-tab:hover {
  transform: translateY(-3px);
}

.color-line {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  border-radius: 3px;
}

.color-line.blue {
  background-color: #4285f4;
}

.color-line.red {
  background-color: #ea4335;
}

.color-line.green {
  background-color: #34a853;
}

.color-line.purple {
  background-color: #9c27b0;
}

.color-line.orange {
  background-color: #ff9800;
}

.tab-container {
  position: relative;
  width: 100%;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 15px 10px 15px 15px;
  overflow: hidden;
}

.half-circle {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.half-circle-icon {
  margin-left: -15px;
  opacity: 0.3;
  font-size: 20px;
}

.half-circle-icon.blue {
  color: #4285f4;
}

.half-circle-icon.red {
  color: #ea4335;
}

.half-circle-icon.green {
  color: #34a853;
}

.half-circle-icon.purple {
  color: #9c27b0;
}

.half-circle-icon.orange {
  color: #ff9800;
}

.tab-name {
  text-align: center;
  font-size: 12px;
  color: #333;
  margin-top: 8px;
  font-weight: 500;
}

/* 响应式调整 - 半圆侧边导航 */
@media (max-width: 768px) {
  .half-circle-tabs {
    justify-content: flex-start;
    padding: 0 10px;
  }
  
  .half-circle-tab {
    width: 60px;
    margin: 0 5px;
  }
  
  .tab-container {
    padding: 12px 8px 12px 12px;
  }
  
  .half-circle {
    width: 30px;
  }
  
  .half-circle-icon {
    margin-left: -12px;
    font-size: 18px;
  }
  
  .tab-name {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .half-circle-tab {
    width: 55px;
  }
  
  .tab-container {
    padding: 10px 6px 10px 10px;
  }
  
  .half-circle {
    width: 26px;
  }
  
  .half-circle-icon {
    margin-left: -10px;
    font-size: 16px;
  }
  
  .tab-name {
    font-size: 10px;
  }
}
</style> 