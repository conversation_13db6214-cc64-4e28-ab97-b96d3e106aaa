import request from '@/utils/request';

// 登录接口
export function login(data) {
  return request({
    url: '/user/login',
    method: 'post',
    data
  });
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/user/info',
    method: 'get'
  });
}

// 登出接口
export function logout() {
  return request({
    url: '/user/logout',
    method: 'post'
  });
}

// 修改密码
export function changePassword(data) {
  return request({
    url: '/user/password',
    method: 'put',
    data
  });
}

export function userListApi(data) {
  return request({
    url: 'app/api/plus/sysUserApi/list',
    method: 'post',
    data
  });
}

// 用户退出接口
export function logoutApi(data) {
  return request({
    url: 'app/api/plus/sysUserApi/logout',
    method: 'post',
    data
  });
}


export function userAdd(data) {
  return request({
    url: 'app/api/plus/sysUserApi/add',
    method: 'post',
    data
  });
}


export function userUpdate(data) {
  return request({
    url: 'app/api/plus/sysUserApi/changeUserInfo',
    method: 'post',
    data
  });
}


export function deptTree() {
  return request({
    url: '/system/user/deptTree',
    method: 'get'
  });
}

// 知识库成员添加
export function MembersAdd(data) {
  return request({
    url: 'app/api/system/repositoryMembersApi/add',
    method: 'post',
    data
  });
}

// 知识库成员批量添加
export function MembersAddBatch(data) {
  return request({
    url: 'app/api/system/repositoryMembersApi/addBatch',
    method: 'post',
    data
  });
}

// 知识库成员列表
export function MembersList(data) {
  return request({
    url: 'app/api/system/repositoryMembersApi/list',
    method: 'post',
    data
  });
}


export function MembersEdit(data) {
  return request({
    url: 'app/api/system/repositoryMembersApi/edit',
    method: 'post',
    data
  });
}

export function MembersRemove(data) {
  return request({
    url: 'app/api/system/repositoryMembersApi/remove',
    method: 'post',
    data
  });
}

// 知识库成员删除
export function MembersDelete(data) {
  return request({
    url: 'app/api/system/repositoryMembersApi/delete',
    method: 'post',
    data
  });
}

// 人员删除
export function UserDelete(userId) {
  return request({
    url: `system/user/${userId}`,
    method: 'delete',
  });
}



// 获取微信登录二维码
export function getWechatQrCode() {
  return request({
    url: '/auth/wechat/qrcode',
    method: 'get'
  });
}

// 检查微信扫码登录状态
export function checkWechatLoginStatus(ticket) {
  return request({
    url: '/auth/wechat/check',
    method: 'get',
    params: { ticket }
  });
}

// 使用微信授权码登录
export function wechatLogin(code) {
  return request({
    url: '/auth/wechat/login',
    method: 'post',
    data: { code }
  });
} 