<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Markdown和代码高亮测试</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/styles/atom-one-dark.min.css">
  <style>
    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    
    h1, h2, h3 {
      margin-top: 1.5em;
    }
    
    pre {
      background-color: #282c34;
      border-radius: 6px;
      padding: 16px;
      overflow: auto;
      margin: 16px 0;
    }
    
    code {
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 14px;
    }
    
    .code-block {
      margin: 16px 0;
      border-radius: 6px;
      overflow: hidden;
      background-color: #282c34;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
    
    .code-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 16px;
      background-color: #21252b;
      border-bottom: 1px solid #181a1f;
    }
    
    .code-language {
      color: #abb2bf;
      font-size: 12px;
      font-weight: 500;
    }
    
    .code-actions {
      display: flex;
      gap: 8px;
    }
    
    .copy-button,
    .download-button {
      background: #4d78cc;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 4px 8px;
      font-size: 12px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 4px;
      transition: background 0.2s ease;
    }
    
    .copy-button:hover,
    .download-button:hover {
      background: #5a8ae8;
    }
    
    .download-button {
      background: #56b6c2;
    }
    
    .download-button:hover {
      background: #66c6d2;
    }
    
    .output {
      border: 1px solid #ddd;
      border-radius: 6px;
      padding: 20px;
      margin-top: 20px;
    }
    
    textarea {
      width: 100%;
      height: 200px;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 14px;
      resize: vertical;
    }
    
    button.parse {
      background: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 16px;
      font-size: 14px;
      cursor: pointer;
      margin-top: 10px;
      transition: background 0.2s ease;
    }
    
    button.parse:hover {
      background: #45a049;
    }
  </style>
</head>
<body>
  <h1>Markdown和代码高亮测试</h1>
  
  <div>
    <h2>输入Markdown内容</h2>
    <textarea id="markdown-input">## 欢迎使用Markdown解析器

这是一个简单的**Markdown**测试。

### 代码示例

```javascript
// JavaScript代码示例
function hello() {
  console.log("Hello World!");
  return 42;
}

const answer = hello();
```

```html
<!-- HTML代码示例 -->
<!DOCTYPE html>
<html>
<head>
  <title>测试页面</title>
</head>
<body>
  <h1>Hello World!</h1>
  <p>这是一个测试页面</p>
</body>
</html>
```

```python
# Python代码示例
def factorial(n):
    if n == 0:
        return 1
    else:
        return n * factorial(n-1)
        
print(factorial(5))  # 输出: 120
```

```css
/* CSS代码示例 */
body {
  font-family: 'Arial', sans-serif;
  color: #333;
  margin: 0;
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}
```

### 列表示例

* 项目1
* 项目2
  * 子项目2.1
  * 子项目2.2
* 项目3

1. 第一步
2. 第二步
3. 第三步

### 表格示例

| 姓名 | 年龄 | 职业 |
|------|------|------|
| 张三 | 28 | 工程师 |
| 李四 | 32 | 设计师 |
| 王五 | 45 | 经理 |
</textarea>
    <button class="parse" onclick="parseMarkdown()">解析Markdown</button>
  </div>
  
  <div class="output" id="output">
    <!-- 解析后的内容将显示在这里 -->
  </div>
  
  <script src="https://cdn.jsdelivr.net/npm/marked@9.1.0/marked.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/lib/highlight.min.js"></script>
  <script>
    // 配置marked解析器
    function configureMarked() {
      // 设置marked选项
      marked.setOptions({
        renderer: new marked.Renderer(),
        highlight: function(code, lang) {
          // 使用highlight.js进行代码高亮
          const language = hljs.getLanguage(lang) ? lang : 'plaintext';
          return hljs.highlight(code, { language }).value;
        },
        langPrefix: 'hljs language-', // 添加到<code>标签的类名前缀
        pedantic: false,
        gfm: true,
        breaks: true,
        sanitize: false,
        smartypants: false,
        xhtml: false
      });
      
      // 自定义渲染器
      const renderer = new marked.Renderer();
      
      // 自定义代码块渲染
      renderer.code = function(code, language) {
        const validLanguage = hljs.getLanguage(language) ? language : 'plaintext';
        const highlightedCode = hljs.highlight(code, { language: validLanguage }).value;
        const langClass = language ? ` class="language-${language}"` : '';
        const codeBlockId = `code-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
        const fileExtension = getFileExtension(language);
        const downloadFilename = `code-snippet${fileExtension}`;
        
        return `
          <div class="code-block">
            <div class="code-header">
              <span class="code-language">${language || 'Code'}</span>
              <div class="code-actions">
                <button class="copy-button" onclick="copyCodeToClipboard('${codeBlockId}')">
                  <span class="copy-icon">📋</span> 复制
                </button>
                <button class="download-button" onclick="downloadCode('${codeBlockId}', '${downloadFilename}')">
                  <span class="download-icon">💾</span> 下载
                </button>
              </div>
            </div>
            <pre><code id="${codeBlockId}" data-language="${language || 'plaintext'}"${langClass}>${highlightedCode}</code></pre>
          </div>
        `;
      };
      
      // 使用自定义渲染器
      marked.use({ renderer });
    }
    
    // 获取文件扩展名
    function getFileExtension(language) {
      const extensions = {
        'javascript': '.js',
        'js': '.js',
        'html': '.html',
        'css': '.css',
        'python': '.py',
        'py': '.py',
        'java': '.java',
        'c': '.c',
        'cpp': '.cpp',
        'csharp': '.cs',
        'php': '.php',
        'ruby': '.rb',
        'go': '.go',
        'rust': '.rs',
        'swift': '.swift',
        'kotlin': '.kt',
        'typescript': '.ts',
        'ts': '.ts',
        'json': '.json',
        'xml': '.xml',
        'yaml': '.yml',
        'markdown': '.md',
        'md': '.md',
        'sql': '.sql',
        'bash': '.sh',
        'powershell': '.ps1'
      };
      
      return extensions[language] || '.txt';
    }
    
    // 复制代码功能
    function copyCodeToClipboard(codeId) {
      const codeElement = document.getElementById(codeId);
      if (codeElement) {
        // 提取原始代码文本（不包含HTML标签）
        const codeText = codeElement.textContent;
        
        // 创建一个临时文本区域用于复制
        const textarea = document.createElement('textarea');
        textarea.value = codeText;
        textarea.style.position = 'fixed';
        textarea.style.opacity = '0';
        document.body.appendChild(textarea);
        textarea.select();
        
        try {
          // 尝试使用复制命令
          const successful = document.execCommand('copy');
          if (successful) {
            // 显示复制成功的视觉反馈
            const button = codeElement.closest('.code-block').querySelector('.copy-button');
            const originalText = button.innerHTML;
            button.innerHTML = '<span class="copy-icon">✓</span> 已复制';
            setTimeout(() => {
              button.innerHTML = originalText;
            }, 2000);
          } else {
            // 如果execCommand失败，尝试使用clipboard API
            navigator.clipboard.writeText(codeText)
              .then(() => {
                const button = codeElement.closest('.code-block').querySelector('.copy-button');
                const originalText = button.innerHTML;
                button.innerHTML = '<span class="copy-icon">✓</span> 已复制';
                setTimeout(() => {
                  button.innerHTML = originalText;
                }, 2000);
              })
              .catch(err => console.error('复制失败:', err));
          }
        } catch (err) {
          console.error('复制过程中出错:', err);
        } finally {
          // 清理临时元素
          document.body.removeChild(textarea);
        }
      }
    }
    
    // 下载代码功能
    function downloadCode(codeId, filename) {
      const codeElement = document.getElementById(codeId);
      if (codeElement) {
        // 提取原始代码文本（不包含HTML标签）
        const codeText = codeElement.textContent;
        const language = codeElement.getAttribute('data-language');
        
        // 创建Blob对象
        const blob = new Blob([codeText], { type: 'text/plain' });
        
        // 创建下载链接
        const downloadLink = document.createElement('a');
        downloadLink.href = URL.createObjectURL(blob);
        downloadLink.download = filename;
        
        // 添加到DOM并触发点击
        document.body.appendChild(downloadLink);
        downloadLink.click();
        
        // 清理
        document.body.removeChild(downloadLink);
        URL.revokeObjectURL(downloadLink.href);
        
        // 显示下载成功的视觉反馈
        const button = codeElement.closest('.code-block').querySelector('.download-button');
        const originalText = button.innerHTML;
        button.innerHTML = '<span class="download-icon">✓</span> 已下载';
        setTimeout(() => {
          button.innerHTML = originalText;
        }, 2000);
      }
    }
    
    // 解析Markdown
    function parseMarkdown() {
      const input = document.getElementById('markdown-input').value;
      const output = document.getElementById('output');
      
      // 配置marked
      configureMarked();
      
      // 解析Markdown
      output.innerHTML = marked.parse(input);
    }
    
    // 页面加载时解析默认内容
    window.onload = function() {
      parseMarkdown();
    };
  </script>
</body>
</html> 