<template>
  <div class="chart-container" :style="{ height: height, width: width }"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  options: {
    type: Object,
    required: true
  },
  height: {
    type: String,
    default: '350px'
  },
  width: {
    type: String,
    default: '100%'
  }
});

const chartInstance = ref(null);
const chartContainer = ref(null);

// 初始化图表
const initChart = () => {
  const chart = echarts.init(chartContainer.value);
  chart.setOption(props.options);
  chartInstance.value = chart;
  
  // 窗口resize时，重新绘制图表
  window.addEventListener('resize', handleResize);
};

// 销毁图表
const destroyChart = () => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
    window.removeEventListener('resize', handleResize);
  }
};

// 窗口大小改变时的处理
const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
};

// 监听options变化
watch(
  () => props.options,
  (newVal) => {
    if (chartInstance.value) {
      chartInstance.value.setOption(newVal);
    }
  },
  { deep: true }
);

onMounted(() => {
  chartContainer.value = document.querySelector('.chart-container');
  initChart();
});

onBeforeUnmount(() => {
  destroyChart();
});
</script>

<style scoped>
.chart-container {
  position: relative;
}
</style> 