<template>
  <div class="menu-management">
    <!-- 搜索和工具栏 -->
    <div class="search-bar">
      <v-row>
        <v-col cols="12" sm="6" md="3" lg="2">
          <v-text-field
            v-model="queryParams.menuName"
            label="菜单名称"
            placeholder="请输入菜单名称"
            clearable
            hide-details
            density="compact"
            variant="outlined"
            @keyup.enter="handleQuery"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="3" lg="2">
          <v-select
            v-model="queryParams.status"
            :items="statusOptions"
            item-title="label"
            item-value="value"
            label="状态"
            clearable
            hide-details
            density="compact"
            variant="outlined"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="3" lg="2">
          <v-btn color="primary" @click="handleQuery" class="mr-2">
            <v-icon start>mdi-magnify</v-icon>搜索
          </v-btn>
          <v-btn @click="resetQuery" variant="outlined">
            <v-icon start>mdi-refresh</v-icon>重置
          </v-btn>
        </v-col>
      </v-row>
    </div>

    <!-- 操作按钮区 -->
    <div class="table-toolbar">
      <v-btn color="primary" @click="handleAdd" class="mr-2">
        <v-icon start>mdi-plus</v-icon>新增
      </v-btn>
      <v-btn @click="handleExpandAll" class="mr-2">
        <v-icon start>mdi-arrow-expand-all</v-icon>展开/折叠
      </v-btn>
      <v-btn @click="handleRefresh" variant="outlined">
        <v-icon start>mdi-refresh</v-icon>刷新
      </v-btn>
    </div>

    <!-- 数据表格 -->
    <v-data-table
      :headers="headers"
      :items="menuList"
      :loading="loading"
      class="elevation-1 mt-4"
      :items-per-page="-1"
    >
      <!-- 菜单名称列 -->
      <template #[`item.menuName`]="{ item }">
        <span :style="{ marginLeft: (item.level || 0) * 20 + 'px' }">
          <v-icon v-if="item.menuType === 'M'" color="primary">mdi-folder</v-icon>
          <v-icon v-else-if="item.menuType === 'C'" color="success">mdi-file-document</v-icon>
          <v-icon v-else color="info">mdi-link</v-icon>
          {{ item.menuName }}
        </span>
      </template>

      <!-- 图标列 -->
      <template #[`item.icon`]="{ item }">
        <v-icon v-if="item.icon && item.icon !== '#'">{{ item.icon }}</v-icon>
        <span v-else>-</span>
      </template>

      <!-- 排序列 -->
      <template #[`item.orderNum`]="{ item }">
        {{ item.orderNum }}
      </template>

      <!-- 权限标识列 -->
      <template #[`item.perms`]="{ item }">
        {{ item.perms || '-' }}
      </template>

      <!-- 组件路径列 -->
      <template #[`item.component`]="{ item }">
        {{ item.component || '-' }}
      </template>

      <!-- 状态列 -->
      <template #[`item.status`]="{ item }">
        <v-chip
          :color="item.status === '0' ? 'success' : 'error'"
          :text="item.status === '0' ? '正常' : '停用'"
          size="small"
        ></v-chip>
      </template>

      <!-- 操作列 -->
      <template #[`item.actions`]="{ item }">
        <v-btn icon size="small" color="primary" @click="handleAdd(item)" class="mr-1" v-if="item.menuType !== 'F'">
          <v-icon>mdi-plus</v-icon>
        </v-btn>
        <v-btn icon size="small" color="info" @click="handleEdit(item)" class="mr-1">
          <v-icon>mdi-pencil</v-icon>
        </v-btn>
        <v-btn icon size="small" color="error" @click="handleDelete(item)">
          <v-icon>mdi-delete</v-icon>
        </v-btn>
      </template>
    </v-data-table>

    <!-- 菜单表单对话框 -->
    <v-dialog v-model="dialog.visible" max-width="700px">
      <v-card>
        <v-card-title>
          <span class="text-h6">{{ dialog.title }}</span>
        </v-card-title>
        <v-card-text>
          <v-form ref="formRef" v-model="formValid">
            <v-container>
              <v-row>
                <v-col cols="12" sm="6">
                  <v-select
                    v-model="form.menuType"
                    :items="menuTypeOptions"
                    item-title="label"
                    item-value="value"
                    label="菜单类型"
                    required
                    :rules="[v => !!v || '菜单类型不能为空']"
                    @update:model-value="handleMenuTypeChange"
                  ></v-select>
                </v-col>
                <v-col cols="12" sm="6" v-if="form.menuType !== 'F'">
                  <v-text-field
                    v-model="form.icon"
                    label="菜单图标"
                    placeholder="请输入图标名称"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-text-field
                    v-model="form.menuName"
                    label="菜单名称"
                    required
                    :rules="[v => !!v || '菜单名称不能为空']"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-text-field
                    v-model="form.orderNum"
                    label="显示排序"
                    type="number"
                    required
                    :rules="[v => !!v || '显示排序不能为空']"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6" v-if="form.menuType !== 'F'">
                  <v-text-field
                    v-model="form.path"
                    label="路由地址"
                    required
                    :rules="[v => form.menuType === 'F' || !!v || '路由地址不能为空']"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6" v-if="form.menuType === 'C'">
                  <v-text-field
                    v-model="form.component"
                    label="组件路径"
                    required
                    :rules="[v => form.menuType !== 'C' || !!v || '组件路径不能为空']"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6" v-if="form.menuType !== 'M'">
                  <v-text-field
                    v-model="form.perms"
                    label="权限标识"
                    placeholder="请输入权限标识"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6" v-if="form.menuType !== 'F'">
                  <v-radio-group v-model="form.isCache" row label="是否缓存">
                    <v-radio label="缓存" :value="1"></v-radio>
                    <v-radio label="不缓存" :value="0"></v-radio>
                  </v-radio-group>
                </v-col>
                <v-col cols="12" sm="6" v-if="form.menuType !== 'F'">
                  <v-radio-group v-model="form.visible" row label="显示状态">
                    <v-radio label="显示" value="0"></v-radio>
                    <v-radio label="隐藏" value="1"></v-radio>
                  </v-radio-group>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-radio-group v-model="form.status" row label="菜单状态">
                    <v-radio label="正常" value="0"></v-radio>
                    <v-radio label="停用" value="1"></v-radio>
                  </v-radio-group>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" @click="submitForm">确定</v-btn>
          <v-btn @click="dialog.visible = false">取消</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { menuApi } from '../../utils/mock';

// 表格列定义
const headers = [
  { title: '菜单名称', key: 'menuName' },
  { title: '图标', key: 'icon' },
  { title: '排序', key: 'orderNum', width: '80px' },
  { title: '权限标识', key: 'perms' },
  { title: '组件路径', key: 'component' },
  { title: '状态', key: 'status', width: '100px' },
  { title: '操作', key: 'actions', sortable: false, width: '150px' }
];

// 状态选项
const statusOptions = [
  { label: '正常', value: '0' },
  { label: '停用', value: '1' }
];

// 菜单类型选项
const menuTypeOptions = [
  { label: '目录', value: 'M' },
  { label: '菜单', value: 'C' },
  { label: '按钮', value: 'F' }
];

// 查询参数
const queryParams = reactive({
  menuName: '',
  status: ''
});

// 表单参数
const form = reactive({
  menuId: undefined,
  parentId: 0,
  menuName: '',
  orderNum: 0,
  path: '',
  component: '',
  isFrame: 1,
  isCache: 0,
  menuType: 'M',
  visible: '0',
  status: '0',
  perms: '',
  icon: '',
  remark: ''
});

// 对话框控制
const dialog = reactive({
  visible: false,
  title: '',
  type: '' // add 或 edit
});

// 数据相关
const loading = ref(false);
const menuList = ref([]);
const formRef = ref(null);
const formValid = ref(false);
const isExpandAll = ref(false);

// 获取菜单列表
const getList = async () => {
  loading.value = true;
  try {
    const res = await menuApi.list(queryParams);
    
    // 处理菜单数据，将树形结构扁平化并添加层级信息
    const flattenMenus = (menus, level = 0) => {
      let result = [];
      menus.forEach(menu => {
        const newMenu = { ...menu, level };
        result.push(newMenu);
        if (menu.children && menu.children.length > 0) {
          result = result.concat(flattenMenus(menu.children, level + 1));
        }
      });
      return result;
    };
    
    menuList.value = flattenMenus(res.data);
  } catch (error) {
    console.error('获取菜单列表失败', error);
    ElMessage.error('获取菜单列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理菜单类型变化
const handleMenuTypeChange = (val) => {
  if (val === 'F') {
    form.isCache = 0;
    form.isFrame = 1;
    form.path = '';
    form.component = '';
    form.icon = '#';
  }
};

// 处理查询
const handleQuery = () => {
  getList();
};

// 重置查询
const resetQuery = () => {
  queryParams.menuName = '';
  queryParams.status = '';
  handleQuery();
};

// 处理刷新
const handleRefresh = () => {
  getList();
};

// 处理展开/折叠
const handleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value;
  // 实际应用中，应该根据isExpandAll的值来展开或折叠表格
  ElMessage.info(isExpandAll.value ? '展开所有菜单' : '折叠所有菜单');
};

// 处理新增
const handleAdd = (row) => {
  resetForm();
  dialog.visible = true;
  dialog.title = '添加菜单';
  dialog.type = 'add';
  
  if (row && row.menuId) {
    form.parentId = row.menuId;
  } else {
    form.parentId = 0;
  }
};

// 处理编辑
const handleEdit = async (row) => {
  resetForm();
  dialog.visible = true;
  dialog.title = '编辑菜单';
  dialog.type = 'edit';
  
  try {
    const res = await menuApi.get(row.menuId);
    if (res.data) {
      Object.assign(form, res.data);
    }
  } catch (error) {
    console.error('获取菜单详情失败', error);
    ElMessage.error('获取菜单详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除菜单"${row.menuName}"吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await menuApi.delete(row.menuId);
      ElMessage.success('删除成功');
      getList();
    } catch (error) {
      console.error('删除菜单失败', error);
      ElMessage.error('删除菜单失败');
    }
  }).catch(() => {});
};

// 提交表单
const submitForm = async () => {
  if (!formValid.value) {
    return;
  }
  
  try {
    if (dialog.type === 'add') {
      await menuApi.add(form);
      ElMessage.success('添加成功');
    } else {
      await menuApi.update(form);
      ElMessage.success('修改成功');
    }
    dialog.visible = false;
    getList();
  } catch (error) {
    console.error('提交表单失败', error);
    ElMessage.error('提交表单失败');
  }
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.reset();
  }
  Object.assign(form, {
    menuId: undefined,
    parentId: 0,
    menuName: '',
    orderNum: 0,
    path: '',
    component: '',
    isFrame: 1,
    isCache: 0,
    menuType: 'M',
    visible: '0',
    status: '0',
    perms: '',
    icon: '',
    remark: ''
  });
};

// 页面初始化
onMounted(() => {
  getList();
});
</script>

<style scoped>
.menu-management {
  width: 100%;
}

.search-bar {
  background-color: var(--card-background);
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px var(--shadow-color);
}

.table-toolbar {
  margin-bottom: 16px;
  display: flex;
  flex-wrap: wrap;
}

.table-toolbar .v-btn {
  margin-bottom: 8px;
}

:deep(.v-data-table) {
  background-color: var(--card-background) !important;
  border-radius: 4px;
  box-shadow: 0 1px 2px var(--shadow-color) !important;
}

:deep(.v-data-table-header) {
  background-color: var(--background-color);
}
</style> 