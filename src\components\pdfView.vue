<template>
    <div class="pdf-header">
        <p class="title">{{ title }}</p>
        <div class="page-tool">
            <div class="page-tool-item" @click="setPage(-1)">上一页</div>
            <div class="page-tool-item">{{ state.pageNum }}/{{ state.numPages }}</div>
            <div class="page-tool-item" @click="setPage(1)">下一页</div>
            <div class="page-tool-item" @click="setZoom(0.1)">放大</div>
            <div class="page-tool-item" @click="setZoom(-0.1)">缩小</div>
        </div>
    </div>
    <div class="pdf-container" :style="containerStyle">
        <!-- 目录 -->
        <div class="container-left">
            <div class="catalogue-item" v-for="i in state.numPages" :key="i">
                <div :class="i == state.pageNum ? 'active' : ''">
                    <vue-pdf-embed class="item-pdf" :source="state.source" @click="state.pageNum = i" :page="i" />
                </div>
                <p>{{ i }}</p>
            </div>
        </div>
        <div class="container-right">
            <div class="paper-pdf-container">
                <vue-pdf-embed class="paper-pdf" :source="state.source" :style="scale" :page="state.pageNum" />
            </div>
        </div>

    </div>
</template>
<script setup>
import { reactive, onMounted, computed } from "vue";
import VuePdfEmbed from "vue-pdf-embed";
import { createLoadingTask } from "vue3-pdfjs";

const props = defineProps({
    pdfSource: {
        type: String,
        default: () => new URL('@/assets/test.pdf', import.meta.url).href
    },
    title: {
        type: String,
        default: "标题"
    },
    height: {
        type: String,
        default: "calc(100vh - 60px)"
    }
});

const state = reactive({
    source: props.pdfSource,
    pageNum: 1,
    scale: 1,
    numPages: 0,
});
// source：{
// url: url,
// cMapUrl: "unpkg.com",
// cMapPacked: true,
// }
const scale = computed(() => `transform:scale(${state.scale})`)
const containerStyle = computed(() => ({
    height: props.height
}))
const setPage = (page) => {
    if (page > 0 && state.pageNum < state.numPages) {
        state.pageNum++;
    } else {
        if (state.pageNum > 1) {
            state.pageNum--;
        }
    }
}
const setZoom = (val) => {
    if (val > 0 && state.scale < 2) {
        state.scale += 0.1;
    } else {
        if (state.scale > 0.6) {
            state.scale -= 0.1;
        }
    }
}


onMounted(() => {
    // 在组件挂载后创建加载任务并设置source
    const loadingTask = createLoadingTask(props.pdfSource);
    state.source = props.pdfSource;
    
    loadingTask.promise.then((pdf) => {
        state.numPages = pdf.numPages;
    });
});

</script>
<style lang="css" scoped>
body {
    width: 100%;
    height: 100vh;
    margin: 0;
    padding: 0;
}

.pdf-header {
    width: 100%;
    height: 60px;
    position: fixed;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 999;
    padding: 0 20px;
    min-width: 1200px;
    background-color: #323639;
    -webkit-box-shadow: 0px 0px 3px #c8c8c8;
    -moz-box-shadow: 0px 0px 3px #c8c8c8;
    box-shadow: 0px 0px 3px #c8c8c8;
}


.title {
    font-size: 18px;
    color: #fff;
}

.page-tool {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #fff;
    user-select: none;
}

.page-tool-item {
    padding: 8px 15px;
    padding-left: 10px;
    cursor: pointer;
}

.pdf-container {
    display: flex;
    margin: 0 auto;
    padding-top: 60px;
    min-height: 500px;
}

.container-left {
    width: 300px;
    box-sizing: border-box;
    height: 100%;
    overflow-y: auto;
    background-color: #323639;
}

.catalogue-item {
    width: 200px;
    margin: 10px auto;
    text-align: center;
    cursor: pointer;
}

p {
    margin: 0;
    padding: 10px 0;
    color: #fff;
}

.item-pdf {
    box-sizing: border-box;
    width: 190px;
}

.active {
    border: 5px solid #7d9dfe;
}

.container-right {
    flex: 1;
    height: 100%;
    background-color: #505050;
    overflow: hidden;
}

.paper-pdf-container {
    width: 100%;
    height: 100%;
    overflow: auto;
}

.paper-pdf {
    padding: 50px 0;
    width: 800px;
    margin: 0 auto;
}
</style>
