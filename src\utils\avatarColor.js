// 头像颜色管理工具

// 颜色列表
const colorGradients = [
  'linear-gradient(135deg, #FF9B9B, #FF6B6B)', 
  'linear-gradient(135deg, #A6C1EE, #5D8BF4)',
  'linear-gradient(135deg, #FFD26F, #FFAA5B)',
  'linear-gradient(135deg, #90F7EC, #32CCBC)',
  'linear-gradient(135deg, #D279EE, #A742EA)',
  'linear-gradient(135deg, #81FBB8, #28C76F)'
];

// 存储用户ID和颜色的映射
const userColorMap = new Map();

/**
 * 根据用户ID获取一致的头像背景色
 * @param {string} userId - 用户ID
 * @returns {string} - 背景色CSS
 */
export function getAvatarColor(userId) {
  // 如果没有用户ID，返回默认颜色
  if (!userId) {
    return colorGradients[0];
  }

  // 如果已经为该用户分配了颜色，则返回该颜色
  if (userColorMap.has(userId)) {
    return userColorMap.get(userId);
  }

  // 为用户分配一个新颜色
  const colorIndex = Math.abs(hashCode(userId)) % colorGradients.length;
  const color = colorGradients[colorIndex];
  
  // 存储用户颜色映射
  userColorMap.set(userId, color);
  
  return color;
}

/**
 * 简单的字符串哈希函数
 * @param {string} str - 输入字符串
 * @returns {number} - 哈希值
 */
function hashCode(str) {
  let hash = 0;
  if (str.length === 0) return hash;
  
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  
  return hash;
}

export default {
  getAvatarColor
}; 