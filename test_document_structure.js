// 测试文档结构解析功能

// 测试数据
const testStructureData = "一、综合科: [日常工作协调机制, 档案管理规范要求, 群众投诉处理流程],    二、创建科: [文明城市测评核心指标, 文明单位创建要素, 负面清单应对机制],    三、宣传科: [群众参与提升策略, 公益广告管理标准, 媒体传播机制建设],    四、未成年人工作科: [思想道德建设重点任务, 文明校园创建标准体系, 特殊群体帮扶机制],    五、志愿服务科: [志愿者注册流程规范, 志愿服务项目管理模型, 权益保障体系构建],    六、农村精神文明建设科: [移风易俗推进策略, 环境整治量化标准, 文化惠民工程实施路径],    七、全国文明城市创建专题问答库: [核心概念阐释, 创建理念体系, 公民参与规范]";

// 解析函数（从 PdfPreview.vue 中提取）
function parseDocumentStructure(structureText) {
  console.log('开始解析文档结构:', structureText);
  
  const structureArray = [];
  
  // 使用正则表达式匹配 "一、xxx: [内容]" 格式
  const sectionRegex = /([一二三四五六七八九十]+)、([^:：]+)[:：]\s*\[([^\]]+)\]/g;
  let match;
  
  while ((match = sectionRegex.exec(structureText)) !== null) {
    const [, number, title, content] = match;
    console.log('匹配到章节:', { number, title, content });
    
    // 处理内容，按逗号分割
    const contentItems = content.split(',').map(item => item.trim()).filter(item => item);
    
    const sectionItem = {
      label: `${number}、${title}`,
      children: contentItems.map(item => ({ label: item }))
    };
    
    structureArray.push(sectionItem);
  }
  
  return structureArray;
}

// 转换回字符串格式的函数
function convertToStructureString(structureArray) {
  let structureString = '';
  structureArray.forEach((item, index) => {
    if (index > 0) structureString += ',    ';
    
    const childrenText = item.children.map(child => child.label).join(', ');
    structureString += `${item.label}: [${childrenText}]`;
  });
  
  return structureString;
}

// 测试解析
console.log('=== 测试文档结构解析 ===');
const parsedStructure = parseDocumentStructure(testStructureData);
console.log('解析结果:', JSON.stringify(parsedStructure, null, 2));

// 测试转换回字符串
console.log('\n=== 测试转换回字符串 ===');
const convertedString = convertToStructureString(parsedStructure);
console.log('转换结果:', convertedString);

// 验证是否一致
console.log('\n=== 验证一致性 ===');
console.log('原始数据:', testStructureData);
console.log('转换结果:', convertedString);
console.log('是否一致:', testStructureData === convertedString);

// 预期结果展示
console.log('\n=== 预期的树形结构 ===');
parsedStructure.forEach(section => {
  console.log(`${section.label}:`);
  section.children.forEach(child => {
    console.log(`  - ${child.label}`);
  });
});
