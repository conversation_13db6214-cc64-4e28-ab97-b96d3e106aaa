<template>
  <el-dialog
    v-model="visibleSG"
    :title="titleSG == 'sort' ? '设置排序' : '移动分组'"
    width="500px"
    align-center
    @close="getClose"
  >
    <div class="content_box">
      <el-input-number
        v-model="nurseGroupSort"
        :min="1"
        @change="handleNumberChange"
        v-if="titleSG == 'sort'"
      ></el-input-number>
      <el-select v-model="groupId" placeholder="分组" style="width: 240px" @change="handleChangeGroup" v-else>
        <el-option
          v-for="item in groupList"
          :key="item.nurseGroupId"
          :label="item.nurseGroupName"
          :value="item.nurseGroupId"
        />
      </el-select>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <!-- <el-button @click="colseExpected">取消</el-button> -->
        <el-button @click="getClose">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
const { proxy } = getCurrentInstance();
const props = defineProps({
  visibleSG: {
    type: Boolean,
  },
  nurseGroupSort: {
    type: Number,
  },
  titleSG: {
    type: String,
  },
  groupList: {
    type: Array,
  },
  groupId:{
    type: Number
  }
});
const emit = defineEmits(["closeSG", "confirmChange"]);

// 监听期望排版列表弹窗打开关闭
const visibleSG = ref(false);
const nurseGroupSort = ref(1);
const titleSG = ref("");
watch(
  () => [props.visibleSG, props.titleSG,props.nurseGroupSort,props.groupList,props.groupId],
  (newValue, oldValue) => {
    console.log(newValue);
    if (newValue) {
      visibleSG.value = newValue[0];
      titleSG.value = newValue[1];
      nurseGroupSort.value = newValue[2];
      groupList.value = newValue[3];
      groupId.value = newValue[4];
    }
  }
);
// 关闭规则弹窗组件
function getClose() {
  emit("closeSG");
}
function colseExpected() {
  visibleSG.value = false;
}

// 监听人员排序input框的数值变化
function handleNumberChange(e) {
  console.log(e);
  nurseGroupSort.value = e;
}
// 监听分组选择器的变化
const groupId = ref();
const groupList = ref([]);
const groupName = ref("");
function handleChangeGroup(e){
  groupId.value = e
  props.groupList.forEach(item=>{
    console.log(item);
    if(item.nurseGroupId == e){
      groupName.value = item.nurseGroupName
    }
  })
};

// 确认修改
function confirm() {
  visibleSG.value = false;
  console.log(groupName.value);
  emit("confirmChange", titleSG.value, nurseGroupSort.value,groupId.value,groupName.value);
}
</script>

<style lang="scss" scoped>
.content_box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 110px;
  border-top: 1px solid #dcdcdc;
  border-bottom: 1px solid #dcdcdc;
}
</style>
