import Cookies from 'js-cookie'

const To<PERSON><PERSON>ey = 'Admin-Token'

export function getToken() {
  return Cookies.get(Token<PERSON>ey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function getCaptchaType() {
  return Cookies.get("captchaType")
}

export function setCaptchaType(captchaType) {
  return Cookies.set("captchaType", captchaType)
}

export function removeCaptchaType() {
  return Cookies.remove("captchaType")
}

export function getIsCode() {
  return Cookies.get("isCode")
}

export function setIsCode(boolFlag) {
  return Cookies.set("isCode", boolFlag)
}

export function removeIsCode() {
  return Cookies.remove("isCode")
}
