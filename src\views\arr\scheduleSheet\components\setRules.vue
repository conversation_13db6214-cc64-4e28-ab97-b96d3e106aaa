<template>
    <el-dialog v-model="visible" width="1231px" top="5vh" append-to-body @close="getClose">
        <template #header="{ titleId,titleClass  }">
            <div class="my-header" :id="titleId">
                <div :class="titleClass">
                    <el-icon :size="20" color="#0052D9">
                        <InfoFilled />
                    </el-icon>设置排班校验规则
                </div>
            </div>
        </template>
        <el-row :gutter="20">
            <el-col :span="24" :xs="24">
                <el-table ref="rulesTable" :data="tableData" style="width: 100%;height: 500px;padding-bottom: 4px;"
                    :max-height="tableHeight" border class="table_item" :header-row-style="{ height: '63px' }"
                    :cell-style="cellStyle" :row-style="{ height: '45px' }" @cell-click="cellClick"
                    @cell-contextmenu.native="rightClick" @cell-dblclick.native="cellContextmenu">
                    <el-table-column class="table_item_XXXXXXXXXX" :prop="item.prop" :label="item.label"
                        v-for="(item, index) in tableHeader" :key="index" :width="85" align="center">
                        <template #default="scope">
                            <div v-show="!item.editable || !scope.row.editable" class="editable-row">
                                <template v-if="item.type == 'text'">
                                    <div style="color: #0052D9;">{{scope.row[item.prop]}}</div>
                                </template>
                            </div>
                            <div v-show="!item.editable || !scope.row.editable" class="editable-row">
                                <template v-if="item.type === 'input'">
                                    <el-input :min="1" v-if="scope.row[item.prop]" type="number"
                                        v-model="scope.row[item.prop]" />
                                </template>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </el-col>
        </el-row>
        <!-- 班种列表组件 -->
        <context-menu style="position: absolute" :style="{ top: menuTop, left: menuLeft }" :classesList="workShiftsList"
            :postList="postList" :postSelect="postSelect" :combinationsList="combinationsList"
            @selButtonItem="selButtonItem" v-show="showMenu">
        </context-menu>
        <template #footer>
            <span class="dialog-footer">
                <el-button style="float: left;" type="warning" @click="delRules">清空所选</el-button>
                <el-button style="float: left;" type="danger" @click="delRulesAll">清空全部</el-button>
                <el-button style="float: left;" type="success" @click="addTableRules">行添加</el-button>
                <el-button @click="colseRules">取消</el-button>
                <el-button type="primary" @click="subMitRules">提交并保存</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
    import contextMenu from "./contextMenu.vue";
    import largeMenu from "./largeMenu.vue";
    import {
        listWorkPost
    } from "@/api/arr/workPost";
    import {
        listPost
    } from "@/api/system/post";
    import {
        listWorkShifts
    } from "@/api/arr/workShifts";
 
    import {
        listWorkShiftsPost
    } from "@/api/arr/workShiftsPost";
    import {
        saveWorkCheck,
        workChecklist
    } from "@/api/arr/scheduleSheet";

    const {
        proxy
    } = getCurrentInstance();
    const props = defineProps({
        visibleRules: {
            type: Boolean,
        },
    });
    const deptId = localStorage.getItem("deptId")
    const deptName = localStorage.getItem("deptName")

    const visible = ref(false)
    watch(
        () => props.visibleRules,
        (newValue, oldValue) => {
            if (newValue) {
                visible.value = newValue;
            }
        }
    );
    const emit = defineEmits(["closeSetRules"]);


    /* 默认数据 */
    const tableHeight = ref(window.innerHeight - 140)
    const tableHeader = ref([{
            prop: "week1_name",
            label: "周一",
            editable: false,
            flag: true,
            type: "text",
        }, {
            prop: "week1_num",
            label: "数量",
            editable: false,
            flag: true,
            type: "input",
        },
        {
            prop: "week2_name",
            label: "周二",
            editable: false,
            flag: true,
            type: "text",
        }, {
            prop: "week2_num",
            label: "数量",
            editable: false,
            flag: true,
            type: "input",
        },
        {
            prop: "week3_name",
            label: "周三",
            editable: false,
            flag: true,
            type: "text",
        }, {
            prop: "week3_num",
            label: "数量",
            editable: false,
            flag: true,
            type: "input",
        },
        {
            prop: "week4_name",
            label: "周四",
            editable: false,
            flag: true,
            type: "text",
        }, {
            prop: "week4_num",
            label: "数量",
            editable: false,
            flag: true,
            type: "input",
        },
        {
            prop: "week5_name",
            label: "周五",
            editable: false,
            flag: true,
            type: "text",
        }, {
            prop: "week5_num",
            label: "数量",
            editable: false,
            flag: true,
            type: "input",
        },
        {
            prop: "week6_name",
            label: "周六",
            editable: false,
            flag: true,
            type: "text",
        }, {
            prop: "week6_num",
            label: "数量",
            editable: false,
            flag: true,
            type: "input",
        },
        {
            prop: "week7_name",
            label: "周日",
            editable: false,
            flag: true,
            type: "text",
        }, {
            prop: "week7_num",
            label: "数量",
            editable: false,
            flag: true,
            type: "input",
        }
    ]);



    const currentValue = ref("");
    const columnIndex = ref(1);
    const showMenu = ref(false);
    // 单元格点击事件
    const tableColumnVal = ref();
    const cellClick = (row, column, rowIndex, columnIndex) => {
        rowIndex.value = row.id;
        columnIndex.value = column.rawColumnKey;
        currentValue.value = `${row.id}_${column.property}`;
        tableColumnVal.value = currentValue.value.split("_")
        showMenu.value = false
        // tableData.value[Number(valArr[0] - 1)][valArr[1] + '_name'] = ""
        // tableData.value[Number(valArr[0] - 1)][valArr[1] + '_num'] = ""
    };

    //单元格的 style 的回调方法
    // const currentBgc = reactive({})
    const cellStyle = ({
        row,
        column,
        rowIndex,
        columnIndex
    }) => {
        if (columnIndex < 0) {
            return {
                backgroundColor: "#f8f8f9"
            };
        } else if (`${row.id}_${column.property}` === currentValue.value) {
            return {
                // border: "1px solid #597EF7",
                borderWidth: "1px",
                /* 上下 2px，左右 1px */
                borderStyle: "solid",
                /* 上下实线，左右虚线 */
                borderColor: "#597EF7",
                /* 上下蓝色，左右红色 */
            };
            // 只给班种加选中样式
            // const nameType = currentValue.value.split("_")[2]
            // if (nameType == 'name') {
            //     return {
            //         // border: "1px solid #597EF7",
            //         borderWidth: "1px",
            //         /* 上下 2px，左右 1px */
            //         borderStyle: "solid",
            //         /* 上下实线，左右虚线 */
            //         borderColor: "#597EF7",
            //         /* 上下蓝色，左右红色 */
            //     };
            // }
        }
    };
    /* 点击单元格相关事件 end*/

    const menuTop = ref("0px");
    const menuLeft = ref("0px");
    const selRowIndex = ref("");
    const selColIndex = ref("");
    const postSelect = ref([]);

    /* 右击单元格事件 */
    const rightClick = (row, column, cell, event) => {
        event.preventDefault();
        selRowIndex.value = row.id;
        selColIndex.value = column.rawColumnKey;
        currentValue.value = `${row.id}_${column.property}`;
        tableColumnVal.value = currentValue.value.split("_")
        // 已选择的岗位在列表上默认显示填充button样式
        let pName = tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_postName']
        postSelect.value = pName.split(',')
        if (tableHeader.value.some((item) => item["label"] == column.label)) {
            menuTop.value = event.clientY - 5 + "px";
            menuLeft.value = event.clientX - 740 + "px";
            showMenu.value = true;
        }
    };

    /* 双击击单元格事件 */
    const cellContextmenu = (row, column, cell, event) => {
        event.preventDefault();
        selRowIndex.value = row.id;
        selColIndex.value = column.rawColumnKey;
        currentValue.value = `${row.id}_${column.property}`;
        tableColumnVal.value = currentValue.value.split("_")
        // 已选择的岗位在列表上默认显示填充button样式
        let pName = tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_postName']
        postSelect.value = pName.split(',')

        if (tableHeader.value.some((item) => item["label"] == column.label)) {
            menuTop.value = event.clientY - 5 + "px";
            menuLeft.value = event.clientX - 740 + "px";
            showMenu.value = true;
        }
    };
    const queryParams = {
        pageNum: 1,
        pageSize: 100,
    }
    /** 查询班次管理列表 */
    const workShiftsList = ref(undefined);

    function getListWorkShifts() {
        listWorkShifts(queryParams.value).then((response) => {
            workShiftsList.value = response.rows;
        });
    }

    /** 查询岗位列表 */
    const postList = ref(undefined);

    function getListPost() {
        listWorkPost(queryParams.value).then(response => {
            postList.value = response.rows;
        });
    }
    /** 查询排班班次岗位组合列表 */
    const combinationsList = ref(undefined);

    function getList() {
        listWorkShiftsPost(queryParams.value).then(response => {
            combinationsList.value = response.rows;
        });
    }
    /* 子组件想父组件暴露事件或传值 start */
    const selButtonItem = (show, shifts_name, shifts_id, post_name, post_id, type) => {
        showMenu.value = show;
        getInfo(shifts_name, shifts_id, post_name, post_id, type)
    };

    // 选中班种岗位值回传当前表格位置
    // var pIdArr = []
    // var pNameArr = []
    function getInfo(shifts_name, shifts_id, post_name, post_id, type) {
        // 选择班种
        if (type == 1) {
            // 选择班种时已有岗位 需要班种+岗位
            if (tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_postName']) {
                tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_name'] =
                    `${shifts_name}(${tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_postName']}) `
            } else { // 选择班种时没有岗位
                tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_name'] = shifts_name
            }
            tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_num'] = 1
            tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_type'] = 1
            tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_shiftsId'] = shifts_id
            tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_shiftsName'] = shifts_name
            // 目的选择新的班种置空岗位

        }
        // 有班种 有岗位
        if (type == 2) {
            var pIdArr = []
            var pNameArr = []
            let pId = tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_postId']
            let pName = tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_postName']
            // 已有岗位列表条件
            if (pName) {
                pIdArr = pId.split(',')
                pNameArr = pName.split(',')

                // 检索当前选择岗位在已有岗位列表中是否存在
                if (pIdArr.includes(post_id.toString())) {
                    // 删除已选岗位id
                    for (let i in pIdArr) {
                        if (pIdArr[i] == post_id) {
                            pIdArr.splice(i, 1);
                        }
                    }
                    // 删除已选岗位名称
                    for (let i in pNameArr) {
                        console.log(pNameArr[i])
                        console.log(post_name)
                        if (pNameArr[i] == post_name) {
                            pNameArr.splice(i, 1);
                        }
                    }
                } else {
                    pIdArr.push(post_id)
                    pNameArr.push(post_name)
                }
            } else { // 没有岗位列表条件
                pIdArr.push(post_id)
                pNameArr.push(post_name)
            }
            // 选择岗位名称列表 回传给岗位列表选择组件 用于匹配当前岗位按钮组样式是否填充
            postSelect.value = pNameArr;
            // 判断当前选择是班种还是岗位  如果没有班种 必须先选择班种
            if (!tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_shiftsName']) {
                return proxy.$modal.msgWarning("请先选择班种");
            } else {
                // 岗位不为空时加括号显示岗位
                if (pNameArr.length > 0) {
                    tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_name'] =
                        `${tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_shiftsName']}(${pNameArr.join(",")}) `
                } else { //否则去掉括号
                    tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_name'] =
                        `${tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_shiftsName']} `
                }
                tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_postName'] = pNameArr
                    .join(",")
                tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_postId'] = pIdArr.join(
                    ",")
                tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_num'] = 1
                tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_type'] = 3
            }

        }

    }


    // 保存校验规则
    const rulesIds = ref([])

    function subMitRules() {
        var week1 = []
        var week2 = []
        var week3 = []
        var week4 = []
        var week5 = []
        var week6 = []
        var week7 = []
        tableData.value.forEach(item => {
            // 星期一
            if (item.week1_type == 1) { //获取普通/班种
                let obj = {
                    weekNum: "星期一",
                    id: item.week1_id,
                    shiftsName: item.week1_shiftsName,
                    shiftsId: item.week1_shiftsId,
                    postName: item.week1_postName,
                    postId: item.week1_postId,
                    reqNum: item.week1_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week1.push(obj)
            }
            if (item.week1_type == 2) { //获取岗位
                let obj = {
                    weekNum: "星期一",
                    id: item.week1_id,
                    shiftsName: item.week1_shiftsName,
                    shiftsId: item.week1_shiftsId,
                    postName: item.week1_postName,
                    postId: item.week1_postId,
                    reqNum: item.week1_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week1.push(obj)
            }
            if (item.week1_type == 3) { //获取组合
                let obj = {
                    weekNum: "星期一",
                    id: item.week1_id,
                    shiftsName: item.week1_shiftsName,
                    shiftsId: item.week1_shiftsId,
                    postName: item.week1_postName,
                    postId: item.week1_postId,
                    reqNum: item.week1_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week1.push(obj)
            }

            // 星期二
            if (item.week2_type == 1) { //获取普通/班种
                let obj = {
                    weekNum: "星期二",
                    id: item.week2_id,
                    shiftsName: item.week2_shiftsName,
                    shiftsId: item.week2_shiftsId,
                    postName: item.week2_postName,
                    postId: item.week2_postId,
                    reqNum: item.week2_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week2.push(obj)
            }
            if (item.week2_type == 2) { //获取岗位
                let obj = {
                    weekNum: "星期二",
                    id: item.week2_id,
                    shiftsName: item.week2_shiftsName,
                    shiftsId: item.week2_shiftsId,
                    postName: item.week2_postName,
                    postId: item.week2_postId,
                    reqNum: item.week2_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week2.push(obj)
            }
            if (item.week2_type == 3) { //获取组合
                let obj = {
                    weekNum: "星期二",
                    id: item.week2_id,
                    shiftsName: item.week2_shiftsName,
                    shiftsId: item.week2_shiftsId,
                    postName: item.week2_postName,
                    postId: item.week2_postId,
                    reqNum: item.week2_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week2.push(obj)
            }
            // 星期三
            if (item.week3_type == 1) { //获取普通/班种1
                let obj = {
                    weekNum: "星期三",
                    id: item.week3_id,
                    shiftsName: item.week3_shiftsName,
                    shiftsId: item.week3_shiftsId,
                    postName: item.week3_postName,
                    postId: item.week3_postId,
                    reqNum: item.week3_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week3.push(obj)
            }
            if (item.week3_type == 2) { //获取岗位
                let obj = {
                    weekNum: "星期三",
                    id: item.week3_id,
                    shiftsName: item.week3_shiftsName,
                    shiftsId: item.week3_shiftsId,
                    postName: item.week3_postName,
                    postId: item.week3_postId,
                    reqNum: item.week3_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week3.push(obj)
            }
            if (item.week3_type == 3) { //获取组合
                let obj = {
                    weekNum: "星期三",
                    id: item.week3_id,
                    shiftsName: item.week3_shiftsName,
                    shiftsId: item.week3_shiftsId,
                    postName: item.week3_postName,
                    postId: item.week3_postId,
                    reqNum: item.week3_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week3.push(obj)
            }
            // 星期四
            if (item.week4_type == 1) { //获取普通/班种1
                let obj = {
                    weekNum: "星期四",
                    id: item.week4_id,
                    shiftsName: item.week4_shiftsName,
                    shiftsId: item.week4_shiftsId,
                    postName: item.week4_postName,
                    postId: item.week4_postId,
                    reqNum: item.week4_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week4.push(obj)
            }
            if (item.week4_type == 2) { //获取岗位
                let obj = {
                    weekNum: "星期四",
                    id: item.week4_id,
                    shiftsName: item.week4_shiftsName,
                    shiftsId: item.week4_shiftsId,
                    postName: item.week4_postName,
                    postId: item.week4_postId,
                    reqNum: item.week4_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week4.push(obj)
            }
            if (item.week4_type == 3) { //获取组合
                let obj = {
                    weekNum: "星期四",
                    id: item.week4_id,
                    shiftsName: item.week4_shiftsName,
                    shiftsId: item.week4_shiftsId,
                    postName: item.week4_postName,
                    postId: item.week4_postId,
                    reqNum: item.week4_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week4.push(obj)
            }
            // 星期五
            if (item.week5_type == 1) { //获取普通/班种1
                let obj = {
                    weekNum: "星期五",
                    id: item.week5_id,
                    shiftsName: item.week5_shiftsName,
                    shiftsId: item.week5_shiftsId,
                    postName: item.week5_postName,
                    postId: item.week5_postId,
                    reqNum: item.week5_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week5.push(obj)
            }
            if (item.week5_type == 2) { //获取岗位
                let obj = {
                    weekNum: "星期五",
                    id: item.week5_id,
                    shiftsName: item.week5_shiftsName,
                    shiftsId: item.week5_shiftsId,
                    postName: item.week5_postName,
                    postId: item.week5_postId,
                    reqNum: item.week5_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week5.push(obj)
            }
            if (item.week5_type == 3) { //获取组合
                let obj = {
                    weekNum: "星期五",
                    id: item.week5_id,
                    shiftsName: item.week5_shiftsName,
                    shiftsId: item.week5_shiftsId,
                    postName: item.week5_postName,
                    postId: item.week5_postId,
                    reqNum: item.week5_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week5.push(obj)
            }
            // 星期六
            if (item.week6_type == 1) { //获取普通/班种1
                let obj = {
                    weekNum: "星期六",
                    id: item.week6_id,
                    shiftsName: item.week6_shiftsName,
                    shiftsId: item.week6_shiftsId,
                    postName: item.week6_postName,
                    postId: item.week6_postId,
                    reqNum: item.week6_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week6.push(obj)
            }
            if (item.week6_type == 2) { //获取岗位
                let obj = {
                    weekNum: "星期六",
                    id: item.week6_id,
                    shiftsName: item.week6_shiftsName,
                    shiftsId: item.week6_shiftsId,
                    postName: item.week6_postName,
                    postId: item.week6_postId,
                    reqNum: item.week6_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week6.push(obj)
            }
            if (item.week6_type == 3) { //获取组合
                let obj = {
                    weekNum: "星期六",
                    id: item.week6_id,
                    shiftsName: item.week6_shiftsName,
                    shiftsId: item.week6_shiftsId,
                    postName: item.week6_postName,
                    postId: item.week6_postId,
                    reqNum: item.week6_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week6.push(obj)
            }
            // 星期日
            if (item.week7_type == 1) { //获取普通/班种1
                let obj = {
                    weekNum: "星期日",
                    id: item.week7_id,
                    shiftsName: item.week7_shiftsName,
                    shiftsId: item.week7_shiftsId,
                    postName: item.week7_postName,
                    postId: item.week7_postId,
                    reqNum: item.week7_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week7.push(obj)
            }
            if (item.week7_type == 2) { //获取岗位
                let obj = {
                    weekNum: "星期日",
                    id: item.week7_id,
                    shiftsName: item.week7_shiftsName,
                    shiftsId: item.week7_shiftsId,
                    postName: item.week7_postName,
                    postId: item.week7_postId,
                    reqNum: item.week7_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week7.push(obj)
            }
            if (item.week7_type == 3) { //获取组合
                let obj = {
                    weekNum: "星期日",
                    id: item.week7_id,
                    shiftsName: item.week7_shiftsName,
                    shiftsId: item.week7_shiftsId,
                    postName: item.week7_postName,
                    postId: item.week7_postId,
                    reqNum: item.week7_num,
                    deptId: deptId,
                    deptName: deptName,
                }
                week7.push(obj)
            }
        });
        let data = {
            workCheckMap: {
                week1: week1,
                week2: week2,
                week3: week3,
                week4: week4,
                week5: week5,
                week6: week6,
                week7: week7,
            },
            ids: rulesIds.value
        }
        // 新增规则
        saveWorkCheck(data).then(response => {
            if (response.code == 200) {
                proxy.$modal.msgSuccess("保存成功");
                visible.value = false
                showMenu.value = false
                getWorkChecklist()
                resetTableData()
                rulesIds.value = []
            } else {
                proxy.$modal.msgError("保存失败");
            }
        });
    }

    // 取消未提交
    function colseRules() {
        visible.value = false
        showMenu.value = false
        getWorkChecklist()
        resetTableData()
    }
    // 关闭规则弹窗组件
    function getClose() {
        showMenu.value = false
        getWorkChecklist()
        resetTableData()
        emit("closeSetRules");
    }
    // 清空所选
    function delRules() {
        if (tableColumnVal.value) {
            let index = Number(tableColumnVal.value[0]) - 1
            let name = tableColumnVal.value[1] + "_" + tableColumnVal.value[2]
            if (tableData.value[index][name]) {
                showMenu.value = false
                tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_name'] = ""
                tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_num'] = null
                tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_type'] = ""
                tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_shiftsName'] = ""
                tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_shiftsId'] = ""
                tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_postName'] = ""
                tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_postId'] = ""
                let id = tableData.value[Number(tableColumnVal.value[0] - 1)][tableColumnVal.value[1] + '_id']
                rulesIds.value.push(id)
            } else {
                proxy.$modal.msgWarning("清除选项不能为空");
            }

        } else {
            proxy.$modal.msgWarning("请选择选项");
        }

    }
    // 清空全部
    function delRulesAll() {
        resetTableData()
    }
    // 初始表格数据
    const tableData = ref([]);
    // 重置表格数据
    function resetTableData() {
        let arr = []
        tableData.value.forEach(item => {
            if (item.week1_id) {
                arr.push(item.week1_id)
            }
            if (item.week2_id) {
                arr.push(item.week2_id)
            }
            if (item.week3_id) {
                arr.push(item.week3_id)
            }
            if (item.week4_id) {
                arr.push(item.week4_id)
            }
            if (item.week5_id) {
                arr.push(item.week5_id)
            }
            if (item.week6_id) {
                arr.push(item.week6_id)
            }
            if (item.week7_id) {
                arr.push(item.week7_id)
            }
        });
        rulesIds.value = arr
        tableData.value = [{
                id: 1,
                week1_id: "",
                week1_name: "",
                week1_shiftsName: "",
                week1_shiftsId: "",
                week1_postName: "",
                week1_postId: "",
                week1_type: "",
                week1_num: null,

                week2_id: "",
                week2_name: "",
                week2_shiftsName: "",
                week2_shiftsId: "",
                week2_postName: "",
                week2_postId: "",
                week2_type: "",
                week2_num: null,

                week3_id: "",
                week3_name: "",
                week3_shiftsName: "",
                week3_shiftsId: "",
                week3_postName: "",
                week3_postId: "",
                week3_type: "",
                week3_num: null,

                week4_id: "",
                week4_name: "",
                week4_shiftsName: "",
                week4_shiftsId: "",
                week4_postName: "",
                week4_postId: "",
                week4_type: "",
                week4_num: null,

                week5_id: "",
                week5_name: "",
                week5_shiftsName: "",
                week5_shiftsId: "",
                week5_postName: "",
                week5_postId: "",
                week5_type: "",
                week5_num: null,

                week6_id: "",
                week6_name: "",
                week6_shiftsName: "",
                week6_shiftsId: "",
                week6_postName: "",
                week6_postId: "",
                week6_type: "",
                week6_num: null,

                week7_id: "",
                week7_name: "",
                week7_shiftsName: "",
                week7_shiftsId: "",
                week7_postName: "",
                week7_postId: "",
                week7_type: "",
                week7_num: null,
            },
            {
                id: 2,
                week1_id: "",
                week1_name: "",
                week1_shiftsName: "",
                week1_shiftsId: "",
                week1_postName: "",
                week1_postId: "",
                week1_type: "",
                week1_num: null,

                week2_id: "",
                week2_name: "",
                week2_shiftsName: "",
                week2_shiftsId: "",
                week2_postName: "",
                week2_postId: "",
                week2_type: "",
                week2_num: null,

                week3_id: "",
                week3_name: "",
                week3_shiftsName: "",
                week3_shiftsId: "",
                week3_postName: "",
                week3_postId: "",
                week3_type: "",
                week3_num: null,

                week4_id: "",
                week4_name: "",
                week4_shiftsName: "",
                week4_shiftsId: "",
                week4_postName: "",
                week4_postId: "",
                week4_type: "",
                week4_num: null,

                week5_id: "",
                week5_name: "",
                week5_shiftsName: "",
                week5_shiftsId: "",
                week5_postName: "",
                week5_postId: "",
                week5_type: "",
                week5_num: null,

                week6_id: "",
                week6_name: "",
                week6_shiftsName: "",
                week6_shiftsId: "",
                week6_postName: "",
                week6_postId: "",
                week6_type: "",
                week6_num: null,

                week7_id: "",
                week7_name: "",
                week7_shiftsName: "",
                week7_shiftsId: "",
                week7_postName: "",
                week7_postId: "",
                week7_type: "",
                week7_num: null,
            },
            {
                id: 3,
                week1_id: "",
                week1_name: "",
                week1_shiftsName: "",
                week1_shiftsId: "",
                week1_postName: "",
                week1_postId: "",
                week1_type: "",
                week1_num: null,

                week2_id: "",
                week2_name: "",
                week2_shiftsName: "",
                week2_shiftsId: "",
                week2_postName: "",
                week2_postId: "",
                week2_type: "",
                week2_num: null,

                week3_id: "",
                week3_name: "",
                week3_shiftsName: "",
                week3_shiftsId: "",
                week3_postName: "",
                week3_postId: "",
                week3_type: "",
                week3_num: null,

                week4_id: "",
                week4_name: "",
                week4_shiftsName: "",
                week4_shiftsId: "",
                week4_postName: "",
                week4_postId: "",
                week4_type: "",
                week4_num: null,

                week5_id: "",
                week5_name: "",
                week5_shiftsName: "",
                week5_shiftsId: "",
                week5_postName: "",
                week5_postId: "",
                week5_type: "",
                week5_num: null,

                week6_id: "",
                week6_name: "",
                week6_shiftsName: "",
                week6_shiftsId: "",
                week6_postName: "",
                week6_postId: "",
                week6_type: "",
                week6_num: null,

                week7_id: "",
                week7_name: "",
                week7_shiftsName: "",
                week7_shiftsId: "",
                week7_postName: "",
                week7_postId: "",
                week7_type: "",
                week7_num: null,
            },
            {
                id: 4,
                week1_id: "",
                week1_name: "",
                week1_shiftsName: "",
                week1_shiftsId: "",
                week1_postName: "",
                week1_postId: "",
                week1_type: "",
                week1_num: null,

                week2_id: "",
                week2_name: "",
                week2_shiftsName: "",
                week2_shiftsId: "",
                week2_postName: "",
                week2_postId: "",
                week2_type: "",
                week2_num: null,

                week3_id: "",
                week3_name: "",
                week3_shiftsName: "",
                week3_shiftsId: "",
                week3_postName: "",
                week3_postId: "",
                week3_type: "",
                week3_num: null,

                week4_id: "",
                week4_name: "",
                week4_shiftsName: "",
                week4_shiftsId: "",
                week4_postName: "",
                week4_postId: "",
                week4_type: "",
                week4_num: null,

                week5_id: "",
                week5_name: "",
                week5_shiftsName: "",
                week5_shiftsId: "",
                week5_postName: "",
                week5_postId: "",
                week5_type: "",
                week5_num: null,

                week6_id: "",
                week6_name: "",
                week6_shiftsName: "",
                week6_shiftsId: "",
                week6_postName: "",
                week6_postId: "",
                week6_type: "",
                week6_num: null,

                week7_id: "",
                week7_name: "",
                week7_shiftsName: "",
                week7_shiftsId: "",
                week7_postName: "",
                week7_postId: "",
                week7_type: "",
                week7_num: null,
            },
            {
                id: 5,
                week1_id: "",
                week1_name: "",
                week1_shiftsName: "",
                week1_shiftsId: "",
                week1_postName: "",
                week1_postId: "",
                week1_type: "",
                week1_num: null,

                week2_id: "",
                week2_name: "",
                week2_shiftsName: "",
                week2_shiftsId: "",
                week2_postName: "",
                week2_postId: "",
                week2_type: "",
                week2_num: null,

                week3_id: "",
                week3_name: "",
                week3_shiftsName: "",
                week3_shiftsId: "",
                week3_postName: "",
                week3_postId: "",
                week3_type: "",
                week3_num: null,

                week4_id: "",
                week4_name: "",
                week4_shiftsName: "",
                week4_shiftsId: "",
                week4_postName: "",
                week4_postId: "",
                week4_type: "",
                week4_num: null,

                week5_id: "",
                week5_name: "",
                week5_shiftsName: "",
                week5_shiftsId: "",
                week5_postName: "",
                week5_postId: "",
                week5_type: "",
                week5_num: null,

                week6_id: "",
                week6_name: "",
                week6_shiftsName: "",
                week6_shiftsId: "",
                week6_postName: "",
                week6_postId: "",
                week6_type: "",
                week6_num: null,

                week7_id: "",
                week7_name: "",
                week7_shiftsName: "",
                week7_shiftsId: "",
                week7_postName: "",
                week7_postId: "",
                week7_type: "",
                week7_num: null,
            },
            {
                id: 6,
                week1_id: "",
                week1_name: "",
                week1_shiftsName: "",
                week1_shiftsId: "",
                week1_postName: "",
                week1_postId: "",
                week1_type: "",
                week1_num: null,

                week2_id: "",
                week2_name: "",
                week2_shiftsName: "",
                week2_shiftsId: "",
                week2_postName: "",
                week2_postId: "",
                week2_type: "",
                week2_num: null,

                week3_id: "",
                week3_name: "",
                week3_shiftsName: "",
                week3_shiftsId: "",
                week3_postName: "",
                week3_postId: "",
                week3_type: "",
                week3_num: null,

                week4_id: "",
                week4_name: "",
                week4_shiftsName: "",
                week4_shiftsId: "",
                week4_postName: "",
                week4_postId: "",
                week4_type: "",
                week4_num: null,

                week5_id: "",
                week5_name: "",
                week5_shiftsName: "",
                week5_shiftsId: "",
                week5_postName: "",
                week5_postId: "",
                week5_type: "",
                week5_num: null,

                week6_id: "",
                week6_name: "",
                week6_shiftsName: "",
                week6_shiftsId: "",
                week6_postName: "",
                week6_postId: "",
                week6_type: "",
                week6_num: null,

                week7_id: "",
                week7_name: "",
                week7_shiftsName: "",
                week7_shiftsId: "",
                week7_postName: "",
                week7_postId: "",
                week7_type: "",
                week7_num: null,
            },
            {
                id: 7,
                week1_id: "",
                week1_name: "",
                week1_shiftsName: "",
                week1_shiftsId: "",
                week1_postName: "",
                week1_postId: "",
                week1_type: "",
                week1_num: null,

                week2_id: "",
                week2_name: "",
                week2_shiftsName: "",
                week2_shiftsId: "",
                week2_postName: "",
                week2_postId: "",
                week2_type: "",
                week2_num: null,

                week3_id: "",
                week3_name: "",
                week3_shiftsName: "",
                week3_shiftsId: "",
                week3_postName: "",
                week3_postId: "",
                week3_type: "",
                week3_num: null,

                week4_id: "",
                week4_name: "",
                week4_shiftsName: "",
                week4_shiftsId: "",
                week4_postName: "",
                week4_postId: "",
                week4_type: "",
                week4_num: null,

                week5_id: "",
                week5_name: "",
                week5_shiftsName: "",
                week5_shiftsId: "",
                week5_postName: "",
                week5_postId: "",
                week5_type: "",
                week5_num: null,

                week6_id: "",
                week6_name: "",
                week6_shiftsName: "",
                week6_shiftsId: "",
                week6_postName: "",
                week6_postId: "",
                week6_type: "",
                week6_num: null,

                week7_id: "",
                week7_name: "",
                week7_shiftsName: "",
                week7_shiftsId: "",
                week7_postName: "",
                week7_postId: "",
                week7_type: "",
                week7_num: null,
            },
            {
                id: 8,
                week1_id: "",
                week1_name: "",
                week1_shiftsName: "",
                week1_shiftsId: "",
                week1_postName: "",
                week1_postId: "",
                week1_type: "",
                week1_num: null,

                week2_id: "",
                week2_name: "",
                week2_shiftsName: "",
                week2_shiftsId: "",
                week2_postName: "",
                week2_postId: "",
                week2_type: "",
                week2_num: null,

                week3_id: "",
                week3_name: "",
                week3_shiftsName: "",
                week3_shiftsId: "",
                week3_postName: "",
                week3_postId: "",
                week3_type: "",
                week3_num: null,

                week4_id: "",
                week4_name: "",
                week4_shiftsName: "",
                week4_shiftsId: "",
                week4_postName: "",
                week4_postId: "",
                week4_type: "",
                week4_num: null,

                week5_id: "",
                week5_name: "",
                week5_shiftsName: "",
                week5_shiftsId: "",
                week5_postName: "",
                week5_postId: "",
                week5_type: "",
                week5_num: null,

                week6_id: "",
                week6_name: "",
                week6_shiftsName: "",
                week6_shiftsId: "",
                week6_postName: "",
                week6_postId: "",
                week6_type: "",
                week6_num: null,

                week7_id: "",
                week7_name: "",
                week7_shiftsName: "",
                week7_shiftsId: "",
                week7_postName: "",
                week7_postId: "",
                week7_type: "",
                week7_num: null,
            },
            {
                id: 9,
                week1_id: "",
                week1_name: "",
                week1_shiftsName: "",
                week1_shiftsId: "",
                week1_postName: "",
                week1_postId: "",
                week1_type: "",
                week1_num: null,

                week2_id: "",
                week2_name: "",
                week2_shiftsName: "",
                week2_shiftsId: "",
                week2_postName: "",
                week2_postId: "",
                week2_type: "",
                week2_num: null,

                week3_id: "",
                week3_name: "",
                week3_shiftsName: "",
                week3_shiftsId: "",
                week3_postName: "",
                week3_postId: "",
                week3_type: "",
                week3_num: null,

                week4_id: "",
                week4_name: "",
                week4_shiftsName: "",
                week4_shiftsId: "",
                week4_postName: "",
                week4_postId: "",
                week4_type: "",
                week4_num: null,

                week5_id: "",
                week5_name: "",
                week5_shiftsName: "",
                week5_shiftsId: "",
                week5_postName: "",
                week5_postId: "",
                week5_type: "",
                week5_num: null,

                week6_id: "",
                week6_name: "",
                week6_shiftsName: "",
                week6_shiftsId: "",
                week6_postName: "",
                week6_postId: "",
                week6_type: "",
                week6_num: null,

                week7_id: "",
                week7_name: "",
                week7_shiftsName: "",
                week7_shiftsId: "",
                week7_postName: "",
                week7_postId: "",
                week7_type: "",
                week7_num: null,
            }
        ]
    }
    // 表格行添加
    function addTableRules() {
        let obj = {
            id: tableData.value.length + 1,
            week1_id: "",
            week1_name: "",
            week1_shiftsName: "",
            week1_shiftsId: "",
            week1_postName: "",
            week1_postId: "",
            week1_type: "",
            week1_num: null,

            week2_id: "",
            week2_name: "",
            week2_shiftsName: "",
            week2_shiftsId: "",
            week2_postName: "",
            week2_postId: "",
            week2_type: "",
            week2_num: null,

            week3_id: "",
            week3_name: "",
            week3_shiftsName: "",
            week3_shiftsId: "",
            week3_postName: "",
            week3_postId: "",
            week3_type: "",
            week3_num: null,

            week4_id: "",
            week4_name: "",
            week4_shiftsName: "",
            week4_shiftsId: "",
            week4_postName: "",
            week4_postId: "",
            week4_type: "",
            week4_num: null,

            week5_id: "",
            week5_name: "",
            week5_shiftsName: "",
            week5_shiftsId: "",
            week5_postName: "",
            week5_postId: "",
            week5_type: "",
            week5_num: null,

            week6_id: "",
            week6_name: "",
            week6_shiftsName: "",
            week6_shiftsId: "",
            week6_postName: "",
            week6_postId: "",
            week6_type: "",
            week6_num: null,

            week7_id: "",
            week7_name: "",
            week7_shiftsName: "",
            week7_shiftsId: "",
            week7_postName: "",
            week7_postId: "",
            week7_type: "",
            week7_num: null,
        }
        tableData.value.push(obj)
    }
    // 渲染表格 进行中...
    function getWorkChecklist() {
        workChecklist().then(response => {
            console.log(response)
            let data = response.data
            let arrLength = []
            let maxValue = null
            for (const key in data) {
                arrLength.push(data[key].length)
                maxValue = Math.max(...arrLength);
            }
            if (maxValue - tableData.value.length > 0) {
                let count = maxValue - tableData.value.length
                for (let i = 0; i < count; i++) {
                    let obj = {
                        id: tableData.value.length + i + 1,
                        week1_id: "",
                        week1_name: "",
                        week1_shiftsName: "",
                        week1_shiftsId: "",
                        week1_postName: "",
                        week1_postId: "",
                        week1_type: "",
                        week1_num: null,

                        week2_id: "",
                        week2_name: "",
                        week2_shiftsName: "",
                        week2_shiftsId: "",
                        week2_postName: "",
                        week2_postId: "",
                        week2_type: "",
                        week2_num: null,

                        week3_id: "",
                        week3_name: "",
                        week3_shiftsName: "",
                        week3_shiftsId: "",
                        week3_postName: "",
                        week3_postId: "",
                        week3_type: "",
                        week3_num: null,

                        week4_id: "",
                        week4_name: "",
                        week4_shiftsName: "",
                        week4_shiftsId: "",
                        week4_postName: "",
                        week4_postId: "",
                        week4_type: "",
                        week4_num: null,

                        week5_id: "",
                        week5_name: "",
                        week5_shiftsName: "",
                        week5_shiftsId: "",
                        week5_postName: "",
                        week5_postId: "",
                        week5_type: "",
                        week5_num: null,

                        week6_id: "",
                        week6_name: "",
                        week6_shiftsName: "",
                        week6_shiftsId: "",
                        week6_postName: "",
                        week6_postId: "",
                        week6_type: "",
                        week6_num: null,

                        week7_id: "",
                        week7_name: "",
                        week7_shiftsName: "",
                        week7_shiftsId: "",
                        week7_postName: "",
                        week7_postId: "",
                        week7_type: "",
                        week7_num: null,
                    }
                    tableData.value.push(obj)
                }
            };
            for (const key in data) {
                if (key == "星期一") {
                    for (let i in data[key]) {
                        tableData.value[i].week1_id = data[key][i].id
                        tableData.value[i].week1_num = data[key][i].reqNum
                        if (data[key][i].postName != "" && data[key][i].shiftsName == "") {
                            tableData.value[i].week1_name = data[key][i].postName
                            tableData.value[i].week1_postName = data[key][i].postName
                            tableData.value[i].week1_postId = data[key][i].postId
                            tableData.value[i].week1_type = 1
                        }
                        if (data[key][i].postName == "" && data[key][i].shiftsName != "") {
                            tableData.value[i].week1_name = data[key][i].shiftsName
                            tableData.value[i].week1_shiftsName = data[key][i].shiftsName
                            tableData.value[i].week1_shiftsId = data[key][i].shiftsId
                            tableData.value[i].week1_type = 2
                        }
                        if (data[key][i].postName != "" && data[key][i].shiftsName != "") {
                            tableData.value[i].week1_name =
                                `${data[key][i].shiftsName}(${ data[key][i].postName})`
                            tableData.value[i].week1_shiftsName = data[key][i].shiftsName
                            tableData.value[i].week1_shiftsId = data[key][i].shiftsId
                            tableData.value[i].week1_postName = data[key][i].postName
                            tableData.value[i].week1_postId = data[key][i].postId
                            tableData.value[i].week1_type = 3
                        }
                    }

                }
                if (key == "星期二") {
                    for (let i in data[key]) {
                        tableData.value[i].week2_id = data[key][i].id
                        tableData.value[i].week2_num = data[key][i].reqNum
                        if (data[key][i].postName != "" && data[key][i].shiftsName == "") {
                            tableData.value[i].week2_name = data[key][i].postName
                            tableData.value[i].week2_postName = data[key][i].postName
                            tableData.value[i].week2_postId = data[key][i].postId
                            tableData.value[i].week2_type = 1
                        }
                        if (data[key][i].postName == "" && data[key][i].shiftsName != "") {
                            tableData.value[i].week2_name = data[key][i].shiftsName
                            tableData.value[i].week2_shiftsName = data[key][i].shiftsName
                            tableData.value[i].week2_shiftsId = data[key][i].shiftsId
                            tableData.value[i].week2_type = 2
                        }
                        if (data[key][i].postName != "" && data[key][i].shiftsName != "") {
                            tableData.value[i].week2_name =
                                `${data[key][i].shiftsName}(${ data[key][i].postName})`
                            tableData.value[i].week2_shiftsName = data[key][i].shiftsName
                            tableData.value[i].week2_shiftsId = data[key][i].shiftsId
                            tableData.value[i].week2_postName = data[key][i].postName
                            tableData.value[i].week2_postId = data[key][i].postId
                            tableData.value[i].week2_type = 3
                        }
                    }

                }
                if (key == "星期三") {
                    for (let i in data[key]) {
                        tableData.value[i].week3_id = data[key][i].id
                        tableData.value[i].week3_num = data[key][i].reqNum
                        if (data[key][i].postName != "" && data[key][i].shiftsName == "") {
                            tableData.value[i].week3_name = data[key][i].postName
                            tableData.value[i].week3_postName = data[key][i].postName
                            tableData.value[i].week3_postId = data[key][i].postId
                            tableData.value[i].week3_type = 1
                        }
                        if (data[key][i].postName == "" && data[key][i].shiftsName != "") {
                            tableData.value[i].week3_name = data[key][i].shiftsName
                            tableData.value[i].week3_shiftsName = data[key][i].shiftsName
                            tableData.value[i].week3_shiftsId = data[key][i].shiftsId
                            tableData.value[i].week3_type = 2
                        }
                        if (data[key][i].postName != "" && data[key][i].shiftsName != "") {
                            tableData.value[i].week3_name =
                                `${data[key][i].shiftsName}(${ data[key][i].postName})`
                            tableData.value[i].week3_shiftsName = data[key][i].shiftsName
                            tableData.value[i].week3_shiftsId = data[key][i].shiftsId
                            tableData.value[i].week3_postName = data[key][i].postName
                            tableData.value[i].week3_postId = data[key][i].postId
                            tableData.value[i].week3_type = 3
                        }
                    }

                }
                if (key == "星期四") {
                    for (let i in data[key]) {
                        tableData.value[i].week4_id = data[key][i].id
                        tableData.value[i].week4_num = data[key][i].reqNum
                        if (data[key][i].postName != "" && data[key][i].shiftsName == "") {
                            tableData.value[i].week4_name = data[key][i].postName
                            tableData.value[i].week4_postName = data[key][i].postName
                            tableData.value[i].week4_postId = data[key][i].postId
                            tableData.value[i].week4_type = 1
                        }
                        if (data[key][i].postName == "" && data[key][i].shiftsName != "") {
                            tableData.value[i].week4_name = data[key][i].shiftsName
                            tableData.value[i].week4_shiftsName = data[key][i].shiftsName
                            tableData.value[i].week4_shiftsId = data[key][i].shiftsId
                            tableData.value[i].week4_type = 2
                        }
                        if (data[key][i].postName != "" && data[key][i].shiftsName != "") {
                            tableData.value[i].week4_name =
                                `${data[key][i].shiftsName}(${ data[key][i].postName})`
                            tableData.value[i].week4_shiftsName = data[key][i].shiftsName
                            tableData.value[i].week4_shiftsId = data[key][i].shiftsId
                            tableData.value[i].week4_postName = data[key][i].postName
                            tableData.value[i].week4_postId = data[key][i].postId
                            tableData.value[i].week4_type = 3
                        }
                    }

                }
                if (key == "星期五") {
                    for (let i in data[key]) {
                        tableData.value[i].week5_id = data[key][i].id
                        tableData.value[i].week5_num = data[key][i].reqNum
                        if (data[key][i].postName != "" && data[key][i].shiftsName == "") {
                            tableData.value[i].week5_name = data[key][i].postName
                            tableData.value[i].week5_postName = data[key][i].postName
                            tableData.value[i].week5_postId = data[key][i].postId
                            tableData.value[i].week5_type = 1
                        }
                        if (data[key][i].postName == "" && data[key][i].shiftsName != "") {
                            tableData.value[i].week5_name = data[key][i].shiftsName
                            tableData.value[i].week5_shiftsName = data[key][i].shiftsName
                            tableData.value[i].week5_shiftsId = data[key][i].shiftsId
                            tableData.value[i].week5_type = 2
                        }
                        if (data[key][i].postName != "" && data[key][i].shiftsName != "") {
                            tableData.value[i].week5_name =
                                `${data[key][i].shiftsName}(${ data[key][i].postName})`
                            tableData.value[i].week5_shiftsName = data[key][i].shiftsName
                            tableData.value[i].week5_shiftsId = data[key][i].shiftsId
                            tableData.value[i].week5_postName = data[key][i].postName
                            tableData.value[i].week5_postId = data[key][i].postId
                            tableData.value[i].week5_type = 3
                        }
                    }

                }
                if (key == "星期六") {
                    for (let i in data[key]) {
                        tableData.value[i].week6_id = data[key][i].id
                        tableData.value[i].week6_num = data[key][i].reqNum
                        if (data[key][i].postName != "" && data[key][i].shiftsName == "") {
                            tableData.value[i].week6_name = data[key][i].postName
                            tableData.value[i].week6_postName = data[key][i].postName
                            tableData.value[i].week6_postId = data[key][i].postId
                            tableData.value[i].week6_type = 1
                        }
                        if (data[key][i].postName == "" && data[key][i].shiftsName != "") {
                            tableData.value[i].week6_name = data[key][i].shiftsName
                            tableData.value[i].week6_shiftsName = data[key][i].shiftsName
                            tableData.value[i].week6_shiftsId = data[key][i].shiftsId
                            tableData.value[i].week6_type = 2
                        }
                        if (data[key][i].postName != "" && data[key][i].shiftsName != "") {
                            tableData.value[i].week6_name =
                                `${data[key][i].shiftsName}(${ data[key][i].postName})`
                            tableData.value[i].week6_shiftsName = data[key][i].shiftsName
                            tableData.value[i].week6_shiftsId = data[key][i].shiftsId
                            tableData.value[i].week6_postName = data[key][i].postName
                            tableData.value[i].week6_postId = data[key][i].postId
                            tableData.value[i].week6_type = 3
                        }
                    }

                }
                if (key == "星期日") {
                    for (let i in data[key]) {
                        tableData.value[i].week7_id = data[key][i].id
                        tableData.value[i].week7_num = data[key][i].reqNum
                        if (data[key][i].postName != "" && data[key][i].shiftsName == "") {
                            tableData.value[i].week7_name = data[key][i].postName
                            tableData.value[i].week7_postName = data[key][i].postName
                            tableData.value[i].week7_postId = data[key][i].postId
                            tableData.value[i].week7_type = 1
                        }
                        if (data[key][i].postName == "" && data[key][i].shiftsName != "") {
                            tableData.value[i].week7_name = data[key][i].shiftsName
                            tableData.value[i].week7_shiftsName = data[key][i].shiftsName
                            tableData.value[i].week7_shiftsId = data[key][i].shiftsId
                            tableData.value[i].week7_type = 2
                        }
                        if (data[key][i].postName != "" && data[key][i].shiftsName != "") {
                            tableData.value[i].week7_name =
                                `${data[key][i].shiftsName}(${ data[key][i].postName})`
                            tableData.value[i].week7_shiftsName = data[key][i].shiftsName
                            tableData.value[i].week7_shiftsId = data[key][i].shiftsId
                            tableData.value[i].week7_postName = data[key][i].postName
                            tableData.value[i].week7_postId = data[key][i].postId
                            tableData.value[i].week7_type = 3
                        }
                    }

                }
            }
        });
    }

    resetTableData()
    getListWorkShifts()
    getListPost()
    getList()
    getWorkChecklist()
</script>

<style lang="scss" scoped>
    .bzTitle {
        height: 30px;
        font-size: 15px;
        margin-left: 10px;
    }

    .ml-2 {
        margin: 0 0 0 10px;
    }

    .tag_sty {
        margin-bottom: 15px;
        background: #F3F3F3;
    }

    .el-dialog {
        background: #F3F3F3;
    }

    .el-dialog__title {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center
    }
</style>