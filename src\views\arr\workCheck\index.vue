<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-护理单元-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="请输入护理单元名称"
            clearable
            prefix-icon="Search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deptOptions"
            :props="{ label: 'label', children: 'children' }"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="deptTreeRef"
            node-key="id"
            highlight-current
            default-expand-all
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px" @submit.native.prevent>
          <el-form-item label="岗位名称" prop="postName">
            <el-input
              v-model="queryParams.postName"
              placeholder="请输入岗位名称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="Plus"
              @click="handleAdd"
              v-hasPermi="['arr:workCheck:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="Edit"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['arr:workCheck:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['arr:workCheck:remove']"
            >删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="Download"
              @click="handleExport"
              v-hasPermi="['arr:workCheck:export']"
            >导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="workCheckList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <!-- <el-table-column label="id" align="center" prop="id" /> -->
          <el-table-column label="护理单元" align="center" prop="deptName" />
          <el-table-column label="星期" align="center" prop="weekNum" />
          <el-table-column label="班次名称" align="center" prop="shiftsName" />
          <el-table-column label="岗位名称" align="center" prop="postName" />
          <!-- <el-table-column label="状态(是否有效)" align="center" prop="status" /> -->
          <el-table-column label="需求数量" align="center" prop="reqNum" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['arr:workCheck:edit']">修改</el-button>
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['arr:workCheck:remove']">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <pagination
          v-show="total>0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>


    <!-- 添加或修改排班规则设置对话框 -->
    <el-dialog v-dialogDrag :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="workCheckRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item  label="护理单元" prop="deptId">
          <el-tree-select
            style="width: 100%;"
            v-model="form.deptId"
            :data="deptOptions"
            :props="{ value: 'id', label: 'label', children: 'children' }"
            value-key="id"
            placeholder="请选择护理单元"
            check-strictly
            @node-click="handleClick"
          />
        </el-form-item>
        <el-form-item label="星期" prop="weekNum">
          <el-select style="width: 100%;" v-model="form.weekNum" placeholder="请选择星期">
            <el-option
              v-for="dict in weekOptins"
              :key="dict.label"
              :label="dict.label"
              :value="dict.label"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item  label="班次" prop="shiftsId">
          <el-select style="width: 100%;" v-model="form.shiftsId" placeholder="请选择班次" @change="handleClickShifts">
            <el-option
              v-for="dict in workShiftsList"
              :key="dict.id"
              :label="dict.shiftsName"
              :value="dict.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="岗位" prop="postId">
          <el-select style="width: 100%;" v-model="form.postId" placeholder="请选择岗位" @change="handleClickWorkPost">
            <el-option
              v-for="dict in postList"
              :key="dict.id"
              :label="dict.postName"
              :value="dict.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="需求数量" prop="reqNum">
          <el-input v-model="form.reqNum" oninput="value=value.replace(/[^\d]/g,'')" placeholder="请输入需求数量" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="WorkCheck">
import { listWorkCheck, getWorkCheck, delWorkCheck, addWorkCheck, updateWorkCheck } from "@/api/arr/workCheck";
import { deptTreeSelect } from "@/api/system/user";
import { listWorkShifts } from "@/api/arr/workShifts";
import { listPost } from "@/api/system/post";
import {
        listWorkPost
    } from "@/api/arr/workPost";
const { proxy } = getCurrentInstance();

const workCheckList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    deptId: null,
    deptName: null,
    weekNum: null,
    shiftsId: null,
    shiftsName: null,
    postId: null,
    postName: null,
    status: null,
    reqNum: null,
  },
  rules: {
    deptId: [{ required: true, message: "护理单元不能为空", trigger: "blur" }],
    weekNum: [{ required: true, message: "星期不能为空", trigger: "blur" }],
    shiftsId: [{ required: true, message: "班次不能为空", trigger: "blur" }],
    postId: [{ required: true, message: "岗位不能为空", trigger: "blur" }],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询排班规则设置列表 */
function getList() {
  loading.value = true;
  listWorkCheck(queryParams.value).then(response => {
    workCheckList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}
/** 根据名称筛选部门树 */
const deptName = ref("");
watch(deptName, (val) => {
  proxy.$refs["deptTreeRef"].filter(val);
});
/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};
// 表单重置
function reset() {
  form.value = {
    id: null,
    deptId: null,
    deptName: null,
    weekNum: null,
    shiftsId: null,
    shiftsName: null,
    postId: null,
    postName: null,
    status: null,
    reqNum: null,
    createBy: null,
    createId: null,
    createTime: null,
    updateBy: null,
    updateId: null,
    updateTime: null,
    tenantId: null,
    delFlag: null,
    remark: null
  };
  proxy.resetForm("workCheckRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加排班规则设置";
  form.value.deptId = queryParams.value.deptId;
  form.value.deptName = queryParams.value.deptName;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getWorkCheck(_id).then(response => {
    form.value = response.data;
    form.value.postId = Number(form.value.postId)
    open.value = true;
    title.value = "修改排班规则设置";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["workCheckRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateWorkCheck(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addWorkCheck(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除排班规则设置编号为"' + _ids + '"的数据项？').then(function() {
    return delWorkCheck(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('arr/workCheck/export', {
    ...queryParams.value
  }, `workCheck_${new Date().getTime()}.xlsx`)
}

const weekOptins = [{
  label:"星期一"
},{
  label:"星期二"
},{
  label:"星期三"
},{
  label:"星期四"
},{
  label:"星期五"
},{
  label:"星期六"
},{
  label:"星期日"
}]

/** 查询部门下拉树结构 */
const deptOptions = ref(undefined);
function getDeptTree() {
  deptTreeSelect().then((response) => {
    console.log(response);
    deptOptions.value = response.data;
  });
}

/** 获取护理单元选中节点信息 */
function handleClick(node) {
  if(node.id != queryParams.value.deptId){
    form.value = {
      shiftsId: null,
      shiftsName: null,
      postId: null,
      postName: null,
    };
  }
  form.value.deptName = node.label;
  queryParams.value.deptId = node.id;
  queryParams.value.deptName = node.label;
  getListPost()
  getListWorkShifts()

}
/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.value.deptId = data.id;
  queryParams.value.deptName = data.label;
  handleQuery();
}

/** 查询班次管理列表 */
const workShiftsList = ref(undefined);
function getListWorkShifts() {
  let data = {
    deptId:queryParams.value.deptId,
    pageNum: 1,
    pageSize: 10,
  }
  listWorkShifts(data).then((response) => {
    workShiftsList.value = response.rows;
  });
}

/** 获取班次节点信息 */
function handleClickShifts(node) {
  workShiftsList.value.forEach(e => {
    if(e.id == node){
      form.value.shiftsName = e.shiftsName;
    }
  });
}

/** 查询岗位列表 */
const postList = ref(undefined);
function getListPost() {
  listWorkPost(queryParams.value).then(response => {
    console.log(response.rows);
    postList.value = response.rows;
  });
}

/** 获取岗位节点信息 */
function handleClickWorkPost(node) {
  postList.value.forEach(e => {
    if(e.id == node){
      form.value.postName = e.postName;
    }
  });
}
getListPost()
getListWorkShifts()
getDeptTree();
getList();
</script>
