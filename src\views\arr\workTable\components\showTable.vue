<template>
  <div>
    <!-- <h2>Vue3 + Element plus 动态表格</h2> -->
    <!-- 单元格样式（暂时不用） :cell-style="cellStyle" -->
    <el-table
      :data="tableData"
      class="table_item"
      style="width: 100%"
      border
      :header-row-style="{ height: '60px' }"
      :row-style="{ height: '50' }"
      :span-method="spanMethod"
    >
      <!-- 排班table 的表头内容 -->
      <el-table-column
        prop="nurseGroupName"
        label="护理分组"
        width="100"
        fixed
        align="center"
      >
      </el-table-column>
      <el-table-column prop="nickName" label="姓名" width="80" fixed align="center">
      </el-table-column>
      <!-- 除护理分组、姓名外的其他表头，通过控制tableColumShow的值来控制显示还是隐藏 -->
      <template v-if="tableColumShow">
        <el-table-column
          :prop="item.prop"
          :label="item.label"
          :width="index == 0 ? 100 : 80"
          v-for="(item, index) in tableHeader"
          :key="index"
          fixed
          align="center"
        >
        </el-table-column>
      </template>
      <!-- 控制 显示/隐藏 的具体内容显示情况 -->
      <el-table-column
        :prop="tableSH.prop"
        :label="tableSH.label"
        :width="70"
        fixed
        align="center"
      >
        <template #header="{ column }">
          <span
            class="header-icon"
            style="display: flex; align-items: center; justify-content: center"
            @click="showOrHide"
          >
            <span class="header-text">{{ column.label }}</span>
            <el-icon style="margin-left: 4px">
              <CaretLeft v-if="column.label == '隐藏'" />
              <CaretRight v-else />
            </el-icon>
          </span>
        </template>
      </el-table-column>
      <!-- 时间表头，通过父组件出来的dates，来显示具体的日期 -->
      <el-table-column
        :prop="item.prop"
        :label="item.label"
        v-for="(item, index) in dateHeader"
        :key="index"
        width="130"
        align="center"
      >
        <template #default="scope">
          <div
            class="editable-row-span"
            v-for="(citem, cindex) in scope.row[item.prop]"
            :key="cindex"
            v-show="citem.flag"
            :style="{ color: citem.shiftsColor }"
          >
            <span class="cell_content">
              {{ citem.shiftsName }}
              <span v-show="citem.postId && citem.postId.length != 0">(</span>
              <span
                class="post_item"
                v-show="citem.postId && citem.postId.length != 0"
                v-for="(sitem, sindex) in citem.postName"
                :key="sindex"
                >{{ sitem
                }}<span
                  class="post_line"
                  v-show="citem.postId && sindex != citem.postId.length - 1"
                ></span
              ></span>
              <span v-show="citem.postId && citem.postId.length != 0">)</span>
            </span>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { arrWorkList } from "@/api/arr/scheduleSheet";

const { proxy } = getCurrentInstance();
/* 组件接收父组件字段定义 */
const props = defineProps({
  dates: {
    type: Array,
  },
  deptId: {
    type: String,
    default: localStorage.getItem("deptId"),
  },
});
// const { deptId } = toRefs(props);
// const queryParams = ref({
//   deptId: deptId,
//   status: "", // 1-临时保存；2-发布；""-全部；
// });

/* 默认表头数据 */
const tableHeader = ref([
  // {
  //   prop: "nurseGroupName",
  //   label: "护理分组",
  //   flag: true,
  // },
  // {
  //   prop: "nickName",
  //   label: "姓名",
  //   flag: true,
  // },
  {
    prop: "userCode",
    label: "工号",
    flag: true,
  },
  {
    prop: "levelName",
    label: "能级",
    flag: true,
  },
  // {
  //   prop: "rest",
  //   label: "积休",
  //   flag: true,
  // },
  // {
  //   prop: "weekRest",
  //   label: "本周积休",
  //   flag: true,
  // },
  {
    prop: "allAmassRest",
    label: "累计积休",
    flag: true,
  },
  {
    prop: "weekDuration",
    label: "本周时长",
    flag: true,
  },
  {
    prop: "monthDuration",
    label: "本月时长",
    flag: true,
  },
  // {
  //   prop: "monthRest",
  //   label: "本月积休",
  //   flag: true,
  // },
]);
/* 展开和隐藏cell */
const tableSH = ref({
  prop: "hide",
  label: "隐藏",
  flag: true,
});
const tableColumShow = ref(true);
// 点击表头 隐藏/展开 时间
function showOrHide() {
  if (tableSH.value.prop == "hide") {
    tableSH.value.prop = "show";
    tableSH.value.label = "展开";
    tableColumShow.value = false;
    tableHeader.value.map((item) => {
      if (item.prop != "nurseGroupName" && item.prop != "nickName") {
        item.flag = false;
      }
    });
  } else {
    tableSH.value.prop = "hide";
    tableSH.value.label = "隐藏";
    tableColumShow.value = true;
    tableHeader.value.map((item) => {
      if (item.prop != "nurseGroupName" && item.prop != "nickName") {
        item.flag = true;
      }
    });
  }
}
/* 默认表单数据 */
const tableData = ref([]);
const dateHeader = ref([]);
const dateRang = ref([]);

// 监听时间范围(dates)的变化，有变化则计算遍历始末日期间的所有日期
watch(
  () => [props.dates, props.deptId],
  (newValue, oldValue) => {
    if (newValue) {
      if (newValue[0].length != 0) {
        let newDates = JSON.parse(JSON.stringify(newValue[0]));
        dateHeader.value = [];
        dateRang.value = [];
        newDates.map((item,index)=>{
          let obj = {
            prop: item,
            // prop: "recordMap",
            label: getWeekday(item),
            flag: true,
          };
          dateHeader.value.push(obj);
          if (index == 0) {
            dateRang.value.push(item);
          } else if (index == newDates.length - 1) {
            dateRang.value.push(item);
          }
        })
      }
      getList();
    }
  }
);

/* 获取排班table的人员数据  start*/
// 解构父组件向子组件传值（响应式，toRefs之后不需要再重新给queryParams.deptId赋值）
const { deptId } = toRefs(props);

const queryParams = ref({
  deptId: deptId,
  status: "2", // 1-临时保存；2-发布；""-全部；
});
function getList() {
  // loading.value = true;
  arrWorkList(proxy.addDateRange(queryParams.value, dateRang.value)).then((response) => {
    response.data.map((item, index) => {
      item["id"] = index;
      if (item.recordMap) {
        Object.keys(item.recordMap).map((child) => {
          item.recordMap[child].forEach((son, sindex) => {
            son.flag = true;
            son.name = JSON.stringify(sindex);
            if (typeof son.postId == "string") {
              son.postId = son.postId.split(",");
            }
            if (typeof son.postName == "string") {
              son.postName = son.postName.split(",");
            }
          });
          item[child] = item.recordMap[child];
        });
      } else {
        item.recordMap = {};
      }
    });
    tableData.value = JSON.parse(JSON.stringify(response.data));
    getSpanArr(tableData.value, "nurseGroupId");
  });
}
/* 获取排班table的人员数据  end*/

/* 默认一周日期范围计算 start */
/* 计算当前日期的星期数 */
const getWeekday = (dateStr) => {
  const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
  const dateObj = new Date(dateStr);
  return dateStr + " " + weekdays[dateObj.getDay()];
};

// 计算距离今天七天后的日期
const nextday = (e) => {
  let date2 = new Date(new Date());
  date2.setDate(new Date().getDate() + 6);
  return date2;
};

/* 计算默认一周的日期 */
const ComputeInterval = () => {
  let list = [];
  let currentDate = new Date();
  while (currentDate <= nextday()) {
    list.push(
      `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(
        2,
        "0"
      )}-${String(currentDate.getDate()).padStart(2, "0")}`
    );
    currentDate.setDate(currentDate.getDate() + 1);
  }
  dateHeader.value = [];
  list.map((item, index) => {
    let obj = {
      prop: item,
      // prop: "recordMap",
      label: getWeekday(item),
      flag: true,
    };
    dateHeader.value.push(obj);
    if (index == 0) {
      dateRang.value.push(item);
    } else if (index == list.length - 1) {
      dateRang.value.push(item);
    }
  });
  getList();
};

ComputeInterval();
/* 默认一周日期范围计算 end */

/* 合并单元格事件 start */
// 将获取到的数据根据需要合并的字段进行排序分组
// 函数groupBy有两个形参，data 为 表格数据 ， params 为需要合并的字段
const groupBy = (data, params) => {
  const groups = {};
  data.forEach((item) => {
    // 根据模拟数据 是通过group字段来分组，获取data中的传入的params属性对应的属性值 ，放入数组中：["2222"]，再将属性值转换为json字符串：'["2222"]'
    const group = JSON.stringify(item[params]);
    //  把group作为groups的key,初始化value,循环时找到相同的item[params]时不变
    groups[group] = groups[group] || [];
    // 将对应找到的值作为value放入数组中
    groups[group].push(item);
  });
  // 返回处理好的二维数组 （如果想返回groupBy形式的数据只返回groups即可）
  return Object.values(groups);
};

// 构造控制合并数组spanArr(),构造一个SpanArr数组赋予rowspan，即控制行合并
const spanArr = ref([]);
const getSpanArr = (data, params) => {
  // 接收重构数组
  let arr = [];
  // 设置索引
  let pos = 0;

  // 控制合并的数组
  spanArr.value = [];
  // arr 处理,就是进行深拷贝，改变存储地址，避免影响原数据(tableData)
  groupBy(data, params).map((v) => (arr = arr.concat(v)));
  arr.map((res) => {
    data.shift();
    data.push(res);
  });
  // spanArr 处理
  const redata = arr.map((v) => v[params]);
  redata.reduce((prev, cur, index, arr) => {
    if (index === 0) {
      spanArr.value.push(1);
    } else {
      if (cur === prev) {
        spanArr.value[pos] += 1;
        spanArr.value.push(0);
      } else {
        spanArr.value.push(1);
        pos = index;
      }
    }
    return cur;
  }, {});
  tableData.value.map((item, index) => {
    item["id"] = index;
  });
};

// 合并单元格
const spanMethod = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex === 0) {
    const _row = spanArr.value[rowIndex];
    const _col = _row > 0 ? 1 : 0;
    return {
      rowspan: _row,
      colspan: _col,
    };
  }
};
/* 合并单元格事件 end */

// const showIndex = ref(4);
// const currentValue = ref("");
// const rowIndex = ref(0);
// const columnIndex = ref(0);

// //单元格的 style 的回调方法
// // const currentBgc = reactive({})
// const flag = false;
// const cellStyle = ({ row, column, rowIndex, columnIndex }) => {
//   // if (tableHeader.value[1].label == "展开" ? columnIndex < 2 : columnIndex <= 4) {
//   //   return { backgroundColor: "#f8f8f9" };
//   // } else if (`${row.id}_${column.property}` === currentValue.value) {
//   //   return { border: "1px solid #597EF7" };
//   // }
// };
// // ({
// //   border:
// //     `${row.id}_${column.property}` === currentValue.value ? "1px solid #597EF7" : "",
// //   // 'backgroundColor':currentBgc[`${row.id}_${column.property}`]||'',
// // });
</script>

<style lang="scss">
.table_item {
  .editable-row-span {
    width: 100%;
    border: 1px solid #366ef4;
    border-radius: 4px;
    background-color: #f2f3ff;
  }
  .tab-header {
    display: flex;
  }
}
</style>
