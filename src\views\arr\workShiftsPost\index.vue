<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px" @submit.native.prevent>
      <el-form-item label="组合名称" prop="shiftsPostName">
        <el-input
          v-model="queryParams.shiftsPostName"
          placeholder="请输入组合名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['arr:workShiftsPost:add']"
        >新增组合</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['arr:workShiftsPost:edit']"
        >修改组合</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['arr:workShiftsPost:remove']"
        >删除组合</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['arr:workShiftsPost:export']"
        >导出组合</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="workShiftsPostList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="组合名称" align="center" prop="shiftsPostName" />
      <el-table-column label="组合状态" width="100" align="center" prop="status">
        <template #default="scope">
          <el-tag v-if="scope.row.status== '1'" type="success">正常</el-tag>
          <el-tag v-if="scope.row.status== '2'" type="danger">停用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="组合内容" align="center">
        <template #default="scope">
          <el-tag type="success">{{scope.row.shiftsName}}</el-tag>
          <el-tag type="warning" style="margin-left: 3px;">{{scope.row.postName}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="orderNum" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['arr:workShiftsPost:edit']">修改</el-button>
          <el-button link type="primary" icon="SwitchButton" @click="handleOnOff(scope.row)">{{scope.row.status == '1'?'停用':'开启'}}</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['arr:workShiftsPost:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改排班班次岗位组合对话框 -->
    <el-dialog v-dialogDrag :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="workShiftsPostRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="组合名称" prop="shiftsPostName">
          <el-input v-model="form.shiftsPostName" placeholder="请输入组合名称" />
        </el-form-item>
        <el-form-item  label="班次" prop="shiftsId">
          <el-select style="width: 100%;" v-model="form.shiftsId" placeholder="请选择班次" @change="handleClickShifts">
            <el-option
              v-for="dict in workShiftsList"
              :key="dict.id"
              :label="dict.shiftsName"
              :value="dict.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="岗位" prop="postId">
          <el-select style="width: 100%;" v-model="form.postId" placeholder="请选择岗位" @change="handleClickWorkPost">
            <el-option
              v-for="dict in postList"
              :key="dict.id"
              :label="dict.postName"
              :value="dict.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="组合状态" prop="status">
          <el-select style="width: 100%;" v-model="form.status" placeholder="请选择组合状态">
            <el-option
              v-for="dict in hr_staff_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="orderNum">
          <el-input v-model="form.orderNum" oninput="value=value.replace(/[^\d]/g,'')" placeholder="请输入排序" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="WorkShiftsPost">
import { listWorkShiftsPost, getWorkShiftsPost, delWorkShiftsPost, addWorkShiftsPost, updateWorkShiftsPost,changeStatus } from "@/api/arr/workShiftsPost";
import { listWorkShifts } from "@/api/arr/workShifts";
import { listPost } from "@/api/system/post";
import {
        listWorkPost
    } from "@/api/arr/workPost";
const { proxy } = getCurrentInstance();
const { hr_staff_status } = proxy.useDict('hr_staff_status');
const workShiftsPostList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {status:"1"},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    shiftsPostName: null,
    postId: null,
    postName: null,
    shiftsId: null,
    shiftsName: null,
    orderNum: null,
  },
  rules: {
    shiftsPostName: [{ required: true, message: "组合名称不能为空", trigger: "blur" }],
    shiftsId: [{ required: true, message: "班次不能为空", trigger: "blur" }],
    postId: [{ required: true, message: "岗位不能为空", trigger: "blur" }],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询排班班次岗位组合列表 */
function getList() {
  loading.value = true;
  listWorkShiftsPost(queryParams.value).then(response => {
    console.log(response)
    workShiftsPostList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    shiftsPostName: null,
    postId: null,
    postName: null,
    shiftsId: null,
    shiftsName: null,
    orderNum: null,
    createBy: null,
    createId: null,
    createTime: null,
    updateBy: null,
    updateId: null,
    updateTime: null,
    tenantId: null,
    delFlag: null,
    remark: null,
    status: "1"
  };
  proxy.resetForm("workShiftsPostRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加排班班次岗位组合";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getWorkShiftsPost(_id).then(response => {
    console.log(response)
    form.value = response.data;
    open.value = true;
    title.value = "修改排班班次岗位组合";
  });
}

/** 停用/开启按钮操作 */
function handleOnOff(row) {
  console.log(row)
  reset();
  const _id = row.id || ids.value
  const _status = row.status
  const statusText = _status == '1'?'停用':'启用'
  
  proxy.$modal.confirm(`是否确认${statusText}排班岗位管理编号为${_id}的数据项？`).then(function() {
    return changeStatus(_status == '1'?'2':'1',_id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess(`${statusText}成功`);
  }).catch(() => {});
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["workShiftsPostRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateWorkShiftsPost(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addWorkShiftsPost(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除排班班次岗位组合编号为"' + _ids + '"的数据项？').then(function() {
    return delWorkShiftsPost(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('arr/workShiftsPost/export', {
    ...queryParams.value
  }, `workShiftsPost_${new Date().getTime()}.xlsx`)
}
/** 查询班次管理列表 */
const workShiftsList = ref(undefined);
function getListWorkShifts() {
  listWorkShifts(queryParams.value).then((response) => {
    workShiftsList.value = response.rows;
  });
}

/** 获取班次节点信息 */
function handleClickShifts(node) {
  workShiftsList.value.forEach(e => {
    if(e.id == node){
      form.value.shiftsName = e.shiftsName;
    }
  });
}

/** 查询岗位列表 */
const postList = ref(undefined);
function getListPost() {
  listWorkPost(queryParams.value).then(response => {
    console.log(response.rows);
    postList.value = response.rows;
  });
}

/** 获取岗位节点信息 */
function handleClickWorkPost(node) {
  console.log(node)
  postList.value.forEach(e => {
    if(e.id == node){
      form.value.postName = e.postName;
    }
  });
}
getListPost()
getListWorkShifts()
getList();
</script>
