<template>
  <div class="graph-container">
    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    
    <!-- 浏览/管理切换按钮 -->
    <div class="mode-switch">
      <div class="mode-option" :class="{ active: !isManageMode }" @click="isManageMode = false">浏览模式</div>
      <div class="mode-option" :class="{ active: isManageMode }" @click="isManageMode = true">管理模式</div>
    </div>
    
    <!-- 浏览模式内容 -->
    <div class="main-content" v-if="!isManageMode">
      <div class="content-section header-section">
        <div class="header-title">
          <h1>智能建筑知识图谱</h1>
          <div class="stats-container">
            <span class="stat-item">节点数: 10</span>
            <span class="stat-item">关系数: 9</span>
            <span class="stat-item">系统数: 5</span>
            <span class="stat-item">设备数: 3</span>
          </div>
          <div class="header-actions">
            <button class="action-button primary" @click="exportImage">
              <i class="fa fa-download"></i>
              导出图谱
            </button>
            <button class="action-button secondary">
              <i class="fa fa-refresh"></i>
              更新数据
            </button>
          </div>
        </div>
      </div>

      <div class="content-section scene-section">
        <div class="scene-title">
          场景视图 <span class="scene-desc">选择不同的业务视角查看知识图谱</span>
        </div>
        
        <div class="graph-tabs">
          <div class="tab" 
               :class="{ active: activeTab === 'system' }"
               @click="switchTab('system')">
            <i class="fa fa-cubes"></i>
            系统架构
            <div class="tab-desc">展示系统间关系和系统架构树</div>
          </div>
          <div class="tab" 
               :class="{ active: activeTab === 'device' }"
               @click="switchTab('device')">
            <i class="fa fa-desktop"></i>
            设备管理
            <div class="tab-desc">展示设备与系统的归属关系</div>
          </div>
          <div class="tab" 
               :class="{ active: activeTab === 'fault' }"
               @click="switchTab('fault')">
            <i class="fa fa-exclamation-triangle"></i>
            故障分析
            <div class="tab-desc">分析故障传播链及故障原因范围</div>
          </div>
          <div class="tab" 
               :class="{ active: activeTab === 'space' }"
               @click="switchTab('space')">
            <i class="fa fa-map-marker"></i>
            空间分布
            <div class="tab-desc">基于空间位置查看设备分布</div>
          </div>
        </div>
      </div>

      <div class="content-section business-section">
        <div class="scenario-title">
          <i class="fa fa-lightbulb-o"></i>
          业务场景 - 在当前视图中找行业业务操作
        </div>

        <div class="scenario-cards">
          <div class="scenario-card" 
               :class="{ active: activeScenario === 'dependency' }"
               @click="switchScenario('dependency')">
            <i class="fa fa-link"></i>
            系统依赖分析
            <div class="card-desc">识别关键链路和系统脆弱环</div>
          </div>
          <div class="scenario-card" 
               :class="{ active: activeScenario === 'expansion' }"
               @click="switchScenario('expansion')">
            <i class="fa fa-expand"></i>
            系统扩展规划
            <div class="card-desc">评估系统扩展部署新节点</div>
          </div>
          <div class="scenario-card" 
               :class="{ active: activeScenario === 'security' }"
               @click="switchScenario('security')">
            <i class="fa fa-shield"></i>
            系统安全评估
            <div class="card-desc">识别系统安全关键节点</div>
          </div>
        </div>
      </div>

      <div class="content-section graph-section">
        <div class="graph-and-details">
          <div class="graph-controls">
            <div class="control-item" 
                 :class="{ active: graphControls.filtered }"
                 title="筛选节点" 
                 @click="toggleFilter">
              <i class="fa fa-filter"></i>
            </div>
            <div class="control-item" 
                 :class="{ active: graphControls.hierarchyView }"
                 title="层级视图" 
                 @click="toggleHierarchyView">
              <i class="fa fa-sitemap"></i>
            </div>
            <div class="control-item" 
                 :class="{ active: graphControls.searchActive }"
                 title="搜索节点" 
                 @click="toggleSearch">
              <i class="fa fa-search"></i>
            </div>
            <div class="control-item" 
                 title="放大/缩小" 
                 @click="zoomGraph">
              <i class="fa fa-search-plus"></i>
              <span class="zoom-level" v-if="graphControls.zoomLevel > 1">{{graphControls.zoomLevel}}x</span>
            </div>
            <div class="control-item" 
                 :class="{ active: graphControls.showOptionsMenu }"
                 title="更多选项" 
                 @click="toggleOptionsMenu">
              <i class="fa fa-ellipsis-h"></i>
            </div>
            <div class="control-item" 
                 :class="{ active: graphControls.showShareMenu }"
                 title="分享" 
                 @click="toggleShareMenu">
              <i class="fa fa-share-alt"></i>
            </div>
          </div>
          
          <!-- 搜索框 -->
          <div class="search-overlay" v-if="showSearchInput">
            <div class="search-container">
              <input 
                type="text" 
                v-model="searchKeyword" 
                placeholder="输入节点名称搜索..." 
                @keyup.enter="searchNodes"
                ref="searchInput"
                autofocus
              />
              <button @click="searchNodes">
                <i class="fa fa-search"></i>
              </button>
              <button class="close-btn" @click="toggleSearch">
                <i class="fa fa-times"></i>
              </button>
            </div>
          </div>
          
          <!-- 更多选项菜单 -->
          <div class="options-menu" v-if="graphControls.showOptionsMenu">
            <div class="menu-item">
              <i class="fa fa-eye"></i> 显示全部节点
            </div>
            <div class="menu-item">
              <i class="fa fa-eye-slash"></i> 隐藏孤立节点
            </div>
            <div class="menu-item">
              <i class="fa fa-arrows-alt"></i> 重置视图
            </div>
            <div class="menu-item">
              <i class="fa fa-picture-o"></i> 更换背景
            </div>
          </div>
          
          <!-- 分享菜单 -->
          <div class="share-menu" v-if="graphControls.showShareMenu">
            <div class="menu-item">
              <i class="fa fa-download"></i> 导出为PNG
            </div>
            <div class="menu-item">
              <i class="fa fa-file-pdf-o"></i> 导出为PDF
            </div>
            <div class="menu-item">
              <i class="fa fa-link"></i> 复制链接
            </div>
            <div class="menu-item">
              <i class="fa fa-qrcode"></i> 生成二维码
            </div>
          </div>
          
          <div class="graph-view">
            <div ref="graphChart" class="graph-chart"></div>
            <div class="graph-legend">
              <span class="legend-item"><i class="legend-dot system"></i> 系统</span>
              <span class="legend-item"><i class="legend-dot device"></i> 设备</span>
              <span class="legend-item"><i class="legend-dot relation"></i> 传感器</span>
              <span class="legend-item"><i class="legend-dot alert"></i> 故障节点</span>
              <span class="legend-item"><i class="legend-dot controller"></i> 控制器</span>
            </div>
          </div>
          
          <div class="node-details">
            <h3 class="details-title">节点详情</h3>
            
            <div class="system-info">
              <div class="system-title">{{ selectedSystem.name }}</div>
              <div class="system-status">
                <span class="status-indicator" :class="selectedSystem.status"></span>
                运行正常
              </div>
            </div>
            
            <div class="details-section">
              <h4 class="section-title">属性信息</h4>
              <div class="property-item">
                <div class="property-label">安装位置</div>
                <div class="property-value">消防控制室</div>
              </div>
              <div class="property-item">
                <div class="property-label">维护责任人</div>
                <div class="property-value">赵工</div>
              </div>
              <div class="property-item">
                <div class="property-label">设备数量</div>
                <div class="property-value">56</div>
              </div>
              <div class="property-item">
                <div class="property-label">上线时间</div>
                <div class="property-value">2022-12-25</div>
              </div>
            </div>
            
            <div class="details-section">
              <h4 class="section-title">关联信息</h4>
              <div class="relation-item">
                <div class="relation-label">控制器 (6)</div>
                <div class="relation-value">
                  <a href="#" class="link-btn">控制</a>
                </div>
              </div>
              <div class="relation-item">
                <div class="relation-label">设备 (5)</div>
                <div class="relation-value">
                  <a href="#" class="link-btn">管理</a>
                </div>
              </div>
            </div>
            
            <div class="action-buttons">
              <button class="action-btn primary">
                <i class="fa fa-sitemap"></i> 展开关联
              </button>
              <button class="action-btn secondary">
                <i class="fa fa-search"></i> 查找相关
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 管理模式内容 -->
    <div class="management-content" v-if="isManageMode">
      <div class="management-header">
        <h1 class="management-title">知识图谱治理中心</h1>
        <div class="management-actions">
          <button class="btn btn-primary">
            <i class="fa fa-upload"></i>
            导出数据
          </button>
          <button class="btn btn-success">
            <i class="fa fa-download"></i>
            导入数据
          </button>
        </div>
      </div>
      
      <div class="management-tabs">
        <div class="management-tab-item" :class="{ active: activeManagementTab === 'fusion' }" @click="activeManagementTab = 'fusion'">图谱融合</div>
        <div class="management-tab-item" :class="{ active: activeManagementTab === 'disambiguation' }" @click="activeManagementTab = 'disambiguation'">实体消歧</div>
        <div class="management-tab-item" :class="{ active: activeManagementTab === 'validation' }" @click="activeManagementTab = 'validation'">图谱验证</div>
        <div class="management-tab-item" :class="{ active: activeManagementTab === 'inference' }" @click="activeManagementTab = 'inference'">关系推理</div>
        <div class="management-tab-item" :class="{ active: activeManagementTab === 'version' }" @click="activeManagementTab = 'version'">版本管理</div>
      </div>
      
      <!-- 图谱融合工具 -->
      <div class="fusion-tool" v-if="activeManagementTab === 'fusion'">
        <div class="tool-header">
          <div class="tool-icon fusion-icon">
            <i class="fa fa-code-fork"></i>
          </div>
          <div class="tool-info">
            <h2 class="tool-title">图谱融合工具</h2>
            <p class="tool-desc">整合来自不同数据源的图谱信息，解决重复和冲突，提升图谱的全面性和准确性。</p>
          </div>
        </div>
        
        <div class="fusion-content">
          <div class="data-sources-section">
            <div class="section-header">
              <h3>数据源概览</h3>
              <span class="source-count">3个数据源</span>
            </div>
            
            <div class="sources-list">
              <div class="source-item active">
                <div class="source-marker"></div>
                <div class="source-name">主系统图谱</div>
                <div class="source-info">10节点 • 12关系</div>
                <div class="source-date">更新: 2023/5/6</div>
                <div class="source-quality">
                  <div class="quality-bar">
                    <div class="quality-fill" style="width: 95%"></div>
                  </div>
                  <span class="quality-value">95%</span>
                </div>
              </div>
              
              <div class="source-item">
                <div class="source-marker"></div>
                <div class="source-name">设备管理系统</div>
                <div class="source-info">65节点 • 58关系</div>
                <div class="source-date">更新: 2024/4/10</div>
                <div class="source-quality">
                  <div class="quality-bar">
                    <div class="quality-fill" style="width: 88%"></div>
                  </div>
                  <span class="quality-value">88%</span>
                </div>
              </div>
              
              <div class="source-item">
                <div class="source-marker"></div>
                <div class="source-name">BIM模型数据</div>
                <div class="source-info">102节点 • 86关系</div>
                <div class="source-date">更新: 2024/3/28</div>
                <div class="source-quality">
                  <div class="quality-bar">
                    <div class="quality-fill" style="width: 92%"></div>
                  </div>
                  <span class="quality-value">92%</span>
                </div>
              </div>
              
              <div class="source-item add-source">
                <div class="add-icon">
                  <i class="fa fa-plus"></i>
                </div>
                <div class="add-text">添加数据源</div>
              </div>
            </div>
          </div>
          
          <div class="fusion-workspace">
            <div class="workspace-header">
              <h3>融合工作台</h3>
              <div class="workspace-actions">
                <button class="btn btn-primary btn-sm" :disabled="btnLoading.fusion">
                  <i class="fa" :class="btnLoading.fusion ? 'fa-spinner fa-spin' : 'fa-refresh'"></i>
                  {{ btnLoading.fusion ? '融合中...' : '启动融合' }}
                </button>
                <button class="btn btn-default btn-sm">
                  <i class="fa fa-cog"></i> 融合设置
                </button>
              </div>
            </div>
            
            <div class="workspace-content">
              <div class="fusion-stats">
                <div class="stat-card">
                  <div class="stat-icon conflict-icon">
                    <i class="fa fa-exclamation-triangle"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">3</div>
                    <div class="stat-label">冲突待解决</div>
                  </div>
                </div>
                
                <div class="stat-card">
                  <div class="stat-icon entity-icon">
                    <i class="fa fa-cube"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">177</div>
                    <div class="stat-label">总实体数</div>
                  </div>
                </div>
                
                <div class="stat-card">
                  <div class="stat-icon relation-icon">
                    <i class="fa fa-link"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">156</div>
                    <div class="stat-label">总关系数</div>
                  </div>
                </div>
              </div>
              
              <div class="conflict-list">
                <div class="conflict-header">
                  <h4>冲突列表</h4>
                  <div class="filter-options">
                    <span class="filter-label active">属性冲突</span>
                    <span class="filter-label">关系冲突</span>
                    <span class="filter-label">命名冲突</span>
                  </div>
                </div>
                
                <table class="conflict-table">
                  <thead>
                    <tr>
                      <th>冲突类型</th>
                      <th>影响实体</th>
                      <th>来源</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr :style="{ display: showConflictResults ? '' : 'none' }">
                      <td><span class="conflict-type attr">属性冲突</span></td>
                      <td>DDC控制器A</td>
                      <td>主系统图谱 / 设备管理系统</td>
                      <td><button class="btn-resolve" @click="resolveConflict(0)">解决</button></td>
                    </tr>
                    <tr :style="{ display: showConflictResults ? '' : 'none' }">
                      <td><span class="conflict-type rel">命名冲突</span></td>
                      <td>温度传感器A1</td>
                      <td>主系统图谱 / BIM模型数据</td>
                      <td><button class="btn-resolve" @click="resolveConflict(1)">解决</button></td>
                    </tr>
                    <tr :style="{ display: showConflictResults ? '' : 'none' }">
                      <td><span class="conflict-type name">关系冲突</span></td>
                      <td>楼宇自控系统</td>
                      <td>设备管理系统 / BIM模型数据</td>
                      <td><button class="btn-resolve" @click="resolveConflict(2)">解决</button></td>
                    </tr>
                  </tbody>
                </table>
              </div>
              
              <div class="fusion-preview">
                <div class="preview-header">
                  <h4>融合预览</h4>
                  <div class="preview-actions">
                    <button class="btn-preview active">
                      <i class="fa fa-pie-chart"></i>
                    </button>
                    <button class="btn-preview">
                      <i class="fa fa-bar-chart"></i>
                    </button>
                  </div>
                </div>
                
                <div class="preview-chart">
                  <div class="chart-placeholder">
                    <div class="chart-info">
                      <div class="chart-icon">
                        <i class="fa fa-area-chart"></i>
                      </div>
                      <div class="chart-text">融合后图谱预览</div>
                      <div class="chart-subtext">解决所有冲突后可查看完整预览</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 实体消歧工具 -->
      <div class="disambiguation-tool" v-if="activeManagementTab === 'disambiguation'">
        <div class="tool-header">
          <div class="tool-icon">
            <i class="fa fa-random"></i>
          </div>
          <div class="tool-info">
            <h2 class="tool-title">实体消歧工具</h2>
            <p class="tool-desc">解决实体指代歧义和冲突，确保知识图谱中的每个实体都有明确、唯一的定义和引用。</p>
          </div>
        </div>
        
        <div class="disambiguation-content">
          <div class="entity-section">
            <div class="entity-header">
              <h3>待处理实体</h3>
              <div class="entity-count">4</div>
            </div>
            
            <div class="entity-list">
              <div class="entity-item active" @click="selectDisambiguationEntity">
                <div class="entity-marker"></div>
                <div class="entity-name">温度传感器</div>
                <div class="entity-count-small">3个潜在重复实体</div>
                <div class="entity-status">当前查中</div>
              </div>
              
              <div class="entity-item" @click="selectDisambiguationEntity">
                <div class="entity-marker"></div>
                <div class="entity-name">DDC控制器</div>
                <div class="entity-count-small">2个潜在重复实体</div>
              </div>
              
              <div class="entity-item" @click="selectDisambiguationEntity">
                <div class="entity-marker"></div>
                <div class="entity-name">风机盒管</div>
                <div class="entity-count-small">2个潜在重复实体</div>
              </div>
              
              <div class="entity-item" @click="selectDisambiguationEntity">
                <div class="entity-marker"></div>
                <div class="entity-name">新风机组</div>
                <div class="entity-count-small">1个潜在重复实体</div>
              </div>
            </div>
          </div>
          
          <div class="comparison-section">
            <div class="comparison-header">
              <h3>实体消歧分析</h3>
              <button class="btn btn-apply" @click="applyDecision">应用决策</button>
            </div>
            
            <div class="warning-message">
              <i class="fa fa-exclamation-circle"></i>
              系统检测到以下实体可能存在歧义
              <p>这些实体具有相似的命名和属性，可能指代同一设备。请查看并择优决定如何处理。</p>
            </div>
            
            <div class="comparison-table">
              <table>
                <thead>
                  <tr>
                    <th>属性</th>
                    <th>实体 1: 温度传感器A1</th>
                    <th>实体 2: 温度传感器_A1</th>
                    <th>实体 3: 温控器A1</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>ID</td>
                    <td>5</td>
                    <td>205</td>
                    <td>312</td>
                  </tr>
                  <tr>
                    <td>类型</td>
                    <td>传感器</td>
                    <td>传感器</td>
                    <td>控制器</td>
                  </tr>
                  <tr>
                    <td>位置</td>
                    <td>3F走廊</td>
                    <td>三层走廊</td>
                    <td>3楼走廊</td>
                  </tr>
                  <tr>
                    <td>设备型号</td>
                    <td>TS-100</td>
                    <td>TS-100</td>
                    <td>TS-100C</td>
                  </tr>
                  <tr>
                    <td>数据源</td>
                    <td>主系统</td>
                    <td>设备管理系统</td>
                    <td>BIM模型数据</td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <div class="decision-section">
              <h3>消歧决策</h3>
              <div class="decision-options">
                <label class="decision-option">
                  <input type="radio" name="decision" value="merge">
                  <span>合并实体 - 将多个实体合并为单一实体，保留最完整的信息</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 图谱验证工具 -->
      <div class="validation-tool" v-if="activeManagementTab === 'validation'">
        <div class="tool-header">
          <div class="tool-icon validation-icon">
            <i class="fa fa-check-circle"></i>
          </div>
          <div class="tool-info">
            <h2 class="tool-title">图谱验证工具</h2>
            <p class="tool-desc">检查图谱的完整性和一致性，识别和修复潜在问题，确保知识图谱的质量。</p>
          </div>
        </div>
        
        <div class="validation-content">
          <div class="validation-actions">
            <button class="btn validation-btn primary" :disabled="btnLoading.validation">
              <i class="fa" :class="btnLoading.validation ? 'fa-spinner fa-spin' : 'fa-play'"></i>
              {{ btnLoading.validation ? '验证中...' : '开始验证' }}
            </button>
            <button class="btn validation-btn secondary">
              <i class="fa fa-cog"></i> 验证设置
            </button>
            <div class="spacer"></div>
            <button class="btn validation-btn export">
              <i class="fa fa-file-text-o"></i> 导出报告
            </button>
          </div>
          
          <div class="validation-panels">
            <div class="validation-panel">
              <div class="panel-header">
                <h3>完整性检查</h3>
              </div>
              <div class="panel-content">
                <div class="check-item">
                  <div class="check-label">缺失属性</div>
                  <div class="check-value empty">--</div>
                </div>
                <div class="check-item">
                  <div class="check-label">孤立节点</div>
                  <div class="check-value empty">--</div>
                </div>
                <div class="check-item">
                  <div class="check-label">悬空关系</div>
                  <div class="check-value empty">--</div>
                </div>
              </div>
            </div>
            
            <div class="validation-panel">
              <div class="panel-header">
                <h3>一致性验证</h3>
              </div>
              <div class="panel-content">
                <div class="check-item">
                  <div class="check-label">属性类型错误</div>
                  <div class="check-value empty">--</div>
                </div>
                <div class="check-item">
                  <div class="check-label">关系约束冲突</div>
                  <div class="check-value empty">--</div>
                </div>
                <div class="check-item">
                  <div class="check-label">命名规范问题</div>
                  <div class="check-value empty">--</div>
                </div>
              </div>
            </div>
            
            <div class="validation-panel quality-panel">
              <div class="panel-header">
                <h3>质量评分</h3>
              </div>
              <div class="panel-content">
                <div class="score-display">
                  <div class="overall-score">-- <span class="score-unit">/100</span></div>
                </div>
                
                <div class="score-details">
                  <div class="score-item">
                    <div class="score-label">完整性</div>
                    <div class="score-bar-container">
                      <div class="score-bar" style="width: 0%"></div>
                    </div>
                    <div class="score-value empty">--</div>
                  </div>
                  
                  <div class="score-item">
                    <div class="score-label">一致性</div>
                    <div class="score-bar-container">
                      <div class="score-bar" style="width: 0%"></div>
                    </div>
                    <div class="score-value empty">--</div>
                  </div>
                  
                  <div class="score-item">
                    <div class="score-label">连通性</div>
                    <div class="score-bar-container">
                      <div class="score-bar" style="width: 0%"></div>
                    </div>
                    <div class="score-value empty">--</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="validation-results-panel" :style="{ display: showResults.validation ? 'block' : 'none' }">
            <div class="results-header">
              <h3>验证结果</h3>
              <div class="results-summary">
                发现 <span class="issue-count">12</span> 个问题，其中 <span class="critical-count">3</span> 个严重问题
              </div>
            </div>
            
            <div class="results-content">
              <div class="results-filters">
                <div class="filter-group">
                  <label>问题类型</label>
                  <select>
                    <option>全部问题</option>
                    <option>完整性问题</option>
                    <option>一致性问题</option>
                    <option>质量问题</option>
                  </select>
                </div>
                
                <div class="filter-group">
                  <label>严重程度</label>
                  <select>
                    <option>全部</option>
                    <option>严重</option>
                    <option>中等</option>
                    <option>轻微</option>
                  </select>
                </div>
                
                <div class="search-group">
                  <input type="text" placeholder="搜索问题..." />
                  <button><i class="fa fa-search"></i></button>
                </div>
              </div>
              
              <div class="results-table-container">
                <table class="results-table">
                  <thead>
                    <tr>
                      <th>问题类型</th>
                      <th>影响对象</th>
                      <th>详细信息</th>
                      <th>严重程度</th>
                      <th>修复建议</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>属性缺失</td>
                      <td>温度传感器A1</td>
                      <td>缺少必要属性: 位置</td>
                      <td><span class="severity high">严重</span></td>
                      <td>添加位置属性信息</td>
                      <td><button class="btn-fix" @click="fixIssue(0)">修复</button></td>
                    </tr>
                    <tr>
                      <td>属性类型错误</td>
                      <td>DDC控制器组</td>
                      <td>安装日期格式不正确</td>
                      <td><span class="severity medium">中等</span></td>
                      <td>将日期格式修改为标准时间格式</td>
                      <td><button class="btn-fix" @click="fixIssue(1)">修复</button></td>
                    </tr>
                    <tr>
                      <td>孤立节点</td>
                      <td>新风系统</td>
                      <td>节点没有任何关联关系</td>
                      <td><span class="severity high">严重</span></td>
                      <td>添加与相关系统的连接关系</td>
                      <td><button class="btn-fix" @click="fixIssue(2)">修复</button></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 关系推理工具 -->
      <div class="inference-tool" v-if="activeManagementTab === 'inference'">
        <div class="inference-layout">
          <div class="inference-left-panel">
            <div class="inference-card">
              <div class="inference-card-header">
                <h3 class="card-title">推理规则库</h3>
                <div class="rules-count">3/4 启用</div>
              </div>
              <div class="inference-card-content">
                <div class="rules-list">
                  <div class="rule-item">
                    <div class="rule-info">
                      <div class="rule-name">传递性规则</div>
                      <div class="rule-desc">如果A控制B且B控制C，则A间接控制C</div>
                    </div>
                    <div class="rule-toggle">
                      <label class="switch">
                        <input type="checkbox" checked>
                        <span class="slider round"></span>
                      </label>
                    </div>
                  </div>
                  
                  <div class="rule-item">
                    <div class="rule-info">
                      <div class="rule-name">对称性规则</div>
                      <div class="rule-desc">如果A连接B，则B连接A</div>
                    </div>
                    <div class="rule-toggle">
                      <label class="switch">
                        <input type="checkbox" checked>
                        <span class="slider round"></span>
                      </label>
                    </div>
                  </div>
                  
                  <div class="rule-item">
                    <div class="rule-info">
                      <div class="rule-name">层级规则</div>
                      <div class="rule-desc">如果A包含B且A属于子系统，则B属于子系统</div>
                    </div>
                    <div class="rule-toggle">
                      <label class="switch">
                        <input type="checkbox">
                        <span class="slider round"></span>
                      </label>
                    </div>
                  </div>
                  
                  <div class="rule-item">
                    <div class="rule-info">
                      <div class="rule-name">功能关联规则</div>
                      <div class="rule-desc">如果A和B都设置同一个，则A和B可能有功能关联</div>
                    </div>
                    <div class="rule-toggle">
                      <label class="switch">
                        <input type="checkbox" checked>
                        <span class="slider round"></span>
                      </label>
                    </div>
                  </div>
                </div>
                
                <div class="run-inference-btn">
                  <button class="btn primary-btn" :disabled="btnLoading.inference">
                    {{ btnLoading.inference ? '推理中...' : '运行推理引擎' }}
                  </button>
                </div>
              </div>
            </div>
            
            <div class="inference-card stats-card">
              <div class="inference-card-header">
                <h3 class="card-title">推理统计</h3>
              </div>
              <div class="inference-card-content">
                <div class="stats-container">
                  <div class="stat-box blue-box">
                    <div class="stat-number">0</div>
                    <div class="stat-label">推断关系</div>
                  </div>
                  <div class="stat-box green-box">
                    <div class="stat-number">0</div>
                    <div class="stat-label">已应用</div>
                  </div>
                </div>
                <div class="last-run-info">
                  上次推理: 未运行
                </div>
              </div>
            </div>
          </div>
          
          <div class="inference-right-panel">
            <div class="inference-results-header">
              <h3 class="results-title">推理结果</h3>
              <div class="results-actions">
                <button class="btn secondary-btn">导出结果</button>
                <button class="btn primary-btn">全部接受</button>
              </div>
            </div>
            
            <div class="inference-results-content">
              <div class="inference-empty-state" v-if="!showResults.inference">
                <div class="empty-icon">
                  <i class="fa fa-random"></i>
                </div>
                <div class="empty-text">运行推理引擎以发现潜在关系</div>
              </div>
              
              <div class="inference-loading" v-if="showResults.inference">
                <div class="loading-spinner">
                  <i class="fa fa-cog fa-spin"></i>
                </div>
                <div class="loading-text">正在运行推理引擎，分析潜在关系...</div>
              </div>
              
              <!-- 结果列表，当有结果时显示 -->
              <div class="inference-results-list" v-if="showResults.inference">
                <table class="inference-table">
                  <thead>
                    <tr>
                      <th>关系类型</th>
                      <th>源实体</th>
                      <th>目标实体</th>
                      <th>推理规则</th>
                      <th>置信度</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>控制</td>
                      <td>主控制器</td>
                      <td>风机组</td>
                      <td>传递性规则</td>
                      <td>
                        <div class="confidence-level high">
                          <div class="confidence-bar"></div>
                          <span>87%</span>
                        </div>
                      </td>
                      <td>
                        <button class="btn-accept" @click="handleInferenceResult(0, true)">接受</button>
                        <button class="btn-reject" @click="handleInferenceResult(0, false)">拒绝</button>
                      </td>
                    </tr>
                    <tr>
                      <td>连接</td>
                      <td>控制器B</td>
                      <td>控制器A</td>
                      <td>对称性规则</td>
                      <td>
                        <div class="confidence-level medium">
                          <div class="confidence-bar"></div>
                          <span>65%</span>
                        </div>
                      </td>
                      <td>
                        <button class="btn-accept" @click="handleInferenceResult(1, true)">接受</button>
                        <button class="btn-reject" @click="handleInferenceResult(1, false)">拒绝</button>
                      </td>
                    </tr>
                    <tr>
                      <td>属于</td>
                      <td>温度传感器C</td>
                      <td>空调系统</td>
                      <td>功能关联规则</td>
                      <td>
                        <div class="confidence-level low">
                          <div class="confidence-bar"></div>
                          <span>42%</span>
                        </div>
                      </td>
                      <td>
                        <button class="btn-accept" @click="handleInferenceResult(2, true)">接受</button>
                        <button class="btn-reject" @click="handleInferenceResult(2, false)">拒绝</button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 版本管理工具 -->
      <div class="version-tool" v-if="activeManagementTab === 'version'">
        <div class="tool-header">
          <div class="tool-icon version-icon">
            <i class="fa fa-code-fork"></i>
          </div>
          <div class="tool-info">
            <h2 class="tool-title">版本与权限管理</h2>
            <p class="tool-desc">管理图谱的历史版本、变更记录和访问权限，保障数据安全和质量。</p>
          </div>
        </div>
        
        <div class="version-content">
          <div class="version-layout">
            <div class="version-main-panel">
              <div class="version-card">
                <div class="version-card-header">
                  <h3 class="section-title">版本历史</h3>
                  <button class="btn-create-version" :disabled="btnLoading.version">
                    <i class="fa" :class="btnLoading.version ? 'fa-spinner fa-spin' : 'fa-plus-circle'"></i>
                    {{ btnLoading.version ? '创建中...' : '创建快照' }}
                  </button>
                </div>
                
                <div class="version-list">
                  <div class="version-item">
                    <div class="version-icon-container">
                      <div class="version-circle">
                        <i class="fa fa-check"></i>
                      </div>
                    </div>
                    
                    <div class="version-info">
                      <div class="version-name">
                        初始版本 v1.0.0
                        <span class="version-tag">当前</span>
                      </div>
                      <div class="version-desc">图谱的初始状态</div>
                      
                      <div class="version-actions">
                        <a href="#" class="version-action">
                          <i class="fa fa-reply"></i> 回溯
                        </a>
                        <a href="#" class="version-action">
                          <i class="fa fa-exchange"></i> 对比
                        </a>
                        <a href="#" class="version-action">
                          <i class="fa fa-history"></i> 变更历史
                        </a>
                      </div>
                    </div>
                    
                    <div class="version-meta">
                      <div class="version-time">2025/5/8 00:08:16</div>
                      <div class="version-author">系统</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="version-side-panel">
              <div class="version-card">
                <div class="version-card-header">
                  <h3 class="section-title">权限设置</h3>
                </div>
                
                <div class="permissions-content">
                  <div class="permission-section">
                    <div class="permission-label">访问控制</div>
                    <div class="permission-selector">
                      <select class="form-select">
                        <option>特定用户组</option>
                        <option>全部用户</option>
                        <option>仅管理员</option>
                      </select>
                      <i class="fa fa-chevron-down select-arrow"></i>
                    </div>
                  </div>
                  
                  <div class="permission-section">
                    <div class="permission-label">用户组权限</div>
                    
                    <div class="user-groups">
                      <div class="user-group-item">
                        <div class="user-group-name">管理员</div>
                        <div class="user-group-role">完全控制</div>
                      </div>
                      
                      <div class="user-group-item">
                        <div class="user-group-name">工程师</div>
                        <div class="user-group-role">读写</div>
                      </div>
                      
                      <div class="user-group-item">
                        <div class="user-group-name">运维人员</div>
                        <div class="user-group-role">只读</div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="permission-section">
                    <div class="permission-label">变更审核</div>
                    
                    <div class="approval-option">
                      <label class="checkbox-container">
                        <input type="checkbox" checked>
                        <span class="checkmark"></span>
                        重要变更需要审核
                      </label>
                    </div>
                  </div>
                  
                  <div class="permission-actions">
                    <button class="btn btn-save-permissions" :disabled="btnLoading.permissions">
                      {{ btnLoading.permissions ? '保存中...' : '保存权限设置' }}
                    </button>
                  </div>
                </div>
              </div>
              
              <div class="version-card">
                <div class="version-card-header">
                  <h3 class="section-title">版本统计</h3>
                </div>
                
                <div class="version-stats">
                  <div class="stat-box light-blue">
                    <div class="stat-number">1</div>
                    <div class="stat-date">2025/5/8</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import * as echarts from 'echarts';

const graphChart = ref(null);
const showGuide = ref(false);
let chart = null;

const selectedNode = ref(true);
const selectedSystem = reactive({
  name: '消防系统',
  status: 'normal'
});

// Active tab tracking
const activeTab = ref('system'); // system, device, fault, space
const activeScenario = ref(null); // dependency, expansion, security

// 图谱控制状态
const graphControls = reactive({
  filtered: false,      // 是否已筛选
  hierarchyView: false, // 是否层级视图
  searchActive: false,  // 是否激活搜索
  zoomLevel: 1,         // 缩放级别
  showOptionsMenu: false, // 是否显示更多选项
  showShareMenu: false,   // 是否显示分享选项
});

// 搜索关键词
const searchKeyword = ref('');
const showSearchInput = ref(false);

// Different graph data for different views
const systemGraphData = reactive({
  nodes: [
    { id: '1', name: '智能建筑系统', category: 0, symbolSize: 50 },
    { id: '2', name: '楼宇自控系统', category: 1, symbolSize: 40 },
    { id: '3', name: '安防监控系统', category: 1, symbolSize: 40 },
    { id: '4', name: '消防系统', category: 1, symbolSize: 40 },
    { id: '5', name: '照明系统', category: 1, symbolSize: 40 },
    { id: '6', name: 'DDC控制器组', category: 2, symbolSize: 35 },
    { id: '7', name: '视频监控子系统', category: 3, symbolSize: 35 },
    { id: '8', name: '门禁子系统', category: 3, symbolSize: 35 },
    { id: '9', name: '火灾自动报警子系统', category: 3, symbolSize: 35 },
    { id: '10', name: '智能照明控制器', category: 2, symbolSize: 35 }
  ],
  links: [
    { source: '1', target: '2' },
    { source: '1', target: '3' },
    { source: '1', target: '4' },
    { source: '1', target: '5' },
    { source: '2', target: '6' },
    { source: '3', target: '7' },
    { source: '3', target: '8' },
    { source: '4', target: '9' },
    { source: '5', target: '10' }
  ]
});

const deviceGraphData = reactive({
  nodes: [
    { id: '1', name: '消防系统', category: 0, symbolSize: 50 },
    { id: '2', name: '火灾自动报警子系统', category: 1, symbolSize: 40 },
    { id: '3', name: '消防水系统', category: 1, symbolSize: 40 },
    { id: '4', name: '烟感器', category: 2, symbolSize: 30 },
    { id: '5', name: '温感器', category: 2, symbolSize: 30 },
    { id: '6', name: '手动报警器', category: 2, symbolSize: 30 },
    { id: '7', name: '水泵', category: 3, symbolSize: 30 },
    { id: '8', name: '喷淋控制器', category: 3, symbolSize: 30 }
  ],
  links: [
    { source: '1', target: '2' },
    { source: '1', target: '3' },
    { source: '2', target: '4' },
    { source: '2', target: '5' },
    { source: '2', target: '6' },
    { source: '3', target: '7' },
    { source: '3', target: '8' }
  ]
});

const faultGraphData = reactive({
  nodes: [
    { id: '1', name: '消防系统', category: 0, symbolSize: 50 },
    { id: '2', name: '火灾自动报警子系统', category: 1, symbolSize: 40 },
    { id: '3', name: '7层烟感器', category: 4, symbolSize: 35, itemStyle: { color: '#ff4d4f' } },
    { id: '4', name: '7层温感器', category: 2, symbolSize: 30 },
    { id: '5', name: '7层手动报警器', category: 2, symbolSize: 30 },
    { id: '6', name: '报警主机', category: 1, symbolSize: 40 },
    { id: '7', name: '控制柜', category: 3, symbolSize: 35 }
  ],
  links: [
    { source: '1', target: '2' },
    { source: '2', target: '3' },
    { source: '2', target: '4' },
    { source: '2', target: '5' },
    { source: '1', target: '6' },
    { source: '6', target: '7' },
    { source: '3', target: '6', lineStyle: { color: '#ff4d4f', width: 2 } }
  ]
});

const spaceGraphData = reactive({
  nodes: [
    { id: '1', name: '建筑楼层', category: 0, symbolSize: 50 },
    { id: '2', name: '一层', category: 1, symbolSize: 40 },
    { id: '3', name: '消防控制室', category: 1, symbolSize: 40 },
    { id: '4', name: '配电室', category: 1, symbolSize: 40 },
    { id: '5', name: '消防主机', category: 2, symbolSize: 30 },
    { id: '6', name: '消防水泵', category: 2, symbolSize: 30 },
    { id: '7', name: '配电柜', category: 2, symbolSize: 30 }
  ],
  links: [
    { source: '1', target: '2' },
    { source: '2', target: '3' },
    { source: '2', target: '4' },
    { source: '3', target: '5' },
    { source: '3', target: '6' },
    { source: '4', target: '7' }
  ]
});

// Scenario graph data
const dependencyGraphData = reactive({
  nodes: [
    { id: '1', name: '消防系统', category: 0, symbolSize: 50 },
    { id: '2', name: '电力系统', category: 0, symbolSize: 50 },
    { id: '3', name: '通信系统', category: 0, symbolSize: 50 },
    { id: '4', name: '火灾自动报警', category: 1, symbolSize: 40 },
    { id: '5', name: '防排烟系统', category: 1, symbolSize: 40 },
    { id: '6', name: '应急照明', category: 1, symbolSize: 40 },
    { id: '7', name: '消防水泵', category: 1, symbolSize: 40 },
    { id: '8', name: '配电柜', category: 2, symbolSize: 35 },
    { id: '9', name: '网络设备', category: 2, symbolSize: 35 }
  ],
  links: [
    { source: '1', target: '4' },
    { source: '1', target: '5' },
    { source: '1', target: '6' },
    { source: '1', target: '7' },
    { source: '2', target: '8' },
    { source: '3', target: '9' },
    { source: '4', target: '2', lineStyle: { color: '#faad14', width: 2 } },
    { source: '4', target: '3', lineStyle: { color: '#faad14', width: 2 } },
    { source: '7', target: '2', lineStyle: { color: '#faad14', width: 2 } },
    { source: '6', target: '2', lineStyle: { color: '#faad14', width: 2 } }
  ]
});

const expansionGraphData = reactive({
  nodes: [
    { id: '1', name: '消防系统', category: 0, symbolSize: 50 },
    { id: '2', name: '火灾自动报警', category: 1, symbolSize: 40 },
    { id: '3', name: '消防水系统', category: 1, symbolSize: 40 },
    { id: '4', name: '防排烟系统', category: 1, symbolSize: 40 },
    { id: '5', name: '应急照明', category: 1, symbolSize: 40 },
    { id: '6', name: '消防广播', category: 1, symbolSize: 40, itemStyle: { color: '#52c41a' } },
    { id: '7', name: '新增区域', category: 0, symbolSize: 50, itemStyle: { color: '#52c41a' } }
  ],
  links: [
    { source: '1', target: '2' },
    { source: '1', target: '3' },
    { source: '1', target: '4' },
    { source: '1', target: '5' },
    { source: '1', target: '6', lineStyle: { color: '#52c41a', width: 2 } },
    { source: '7', target: '6', lineStyle: { color: '#52c41a', width: 2 } }
  ]
});

const securityGraphData = reactive({
  nodes: [
    { id: '1', name: '消防系统', category: 0, symbolSize: 50 },
    { id: '2', name: '火灾自动报警', category: 1, symbolSize: 40 },
    { id: '3', name: '防排烟系统', category: 1, symbolSize: 40 },
    { id: '4', name: '消防网关', category: 4, symbolSize: 45, itemStyle: { color: '#ff4d4f' } },
    { id: '5', name: '远程监控', category: 2, symbolSize: 35 },
    { id: '6', name: '控制终端', category: 2, symbolSize: 35 }
  ],
  links: [
    { source: '1', target: '2' },
    { source: '1', target: '3' },
    { source: '1', target: '4', lineStyle: { color: '#ff4d4f', width: 2 } },
    { source: '4', target: '5', lineStyle: { color: '#ff4d4f', width: 2 } },
    { source: '4', target: '6', lineStyle: { color: '#ff4d4f', width: 2 } }
  ]
});

// Default to system view
const currentGraphData = ref(systemGraphData);
// 保存原始数据，用于筛选后恢复
const originalGraphData = ref(systemGraphData);
// 筛选后的数据
const filteredGraphData = reactive({
  nodes: [],
  links: []
});

// 控制面板功能
// 1. 筛选功能
const toggleFilter = () => {
  graphControls.filtered = !graphControls.filtered;
  
  if (graphControls.filtered) {
    // 模拟筛选：只保留系统和子系统节点（category 0和1）
    filteredGraphData.nodes = currentGraphData.value.nodes.filter(node => 
      node.category === 0 || node.category === 1
    );
    
    // 只保留与这些节点有关的连接
    const nodeIds = filteredGraphData.nodes.map(node => node.id);
    filteredGraphData.links = currentGraphData.value.links.filter(link => 
      nodeIds.includes(link.source) && nodeIds.includes(link.target)
    );
    
    updateChart(filteredGraphData);
  } else {
    // 恢复原始数据
    updateChart(currentGraphData.value);
  }
};

// 2. 层级视图切换
const toggleHierarchyView = () => {
  graphControls.hierarchyView = !graphControls.hierarchyView;
  
  if (graphControls.hierarchyView) {
    // 切换为层级树形布局
    if (chart) {
      const option = chart.getOption();
      option.series[0].layout = 'tree';
      option.series[0].force = null;
      option.series[0].orient = 'LR';
      chart.setOption(option, true);
    }
  } else {
    // 恢复力导向布局
    if (chart) {
      const option = chart.getOption();
      option.series[0].layout = 'force';
      option.series[0].force = {
        repulsion: 200,
        edgeLength: 120
      };
      chart.setOption(option, true);
    }
  }
};

// 3. 搜索功能
const toggleSearch = () => {
  graphControls.searchActive = !graphControls.searchActive;
  showSearchInput.value = graphControls.searchActive;
  
  if (!graphControls.searchActive) {
    // 取消搜索，恢复原始视图
    searchKeyword.value = '';
    updateChart(currentGraphData.value);
  }
};

const searchNodes = () => {
  if (!searchKeyword.value.trim()) {
    updateChart(currentGraphData.value);
    return;
  }
  
  // 搜索匹配节点
  const keyword = searchKeyword.value.toLowerCase();
  filteredGraphData.nodes = currentGraphData.value.nodes.filter(node => 
    node.name.toLowerCase().includes(keyword)
  );
  
  if (filteredGraphData.nodes.length === 0) {
    // 无匹配结果，显示所有节点
    filteredGraphData.nodes = currentGraphData.value.nodes;
    filteredGraphData.links = currentGraphData.value.links;
  } else {
    // 找出这些节点之间的连接
    const nodeIds = filteredGraphData.nodes.map(node => node.id);
    filteredGraphData.links = currentGraphData.value.links.filter(link => 
      nodeIds.includes(link.source) && nodeIds.includes(link.target)
    );
    
    // 高亮搜索结果
    filteredGraphData.nodes.forEach(node => {
      if (node.name.toLowerCase().includes(keyword)) {
        node._highlight = true;
        node._originSize = node.symbolSize;
        node.symbolSize = node.symbolSize * 1.3;
      }
    });
  }
  
  updateChart(filteredGraphData);
};

// 4. 缩放控制
const zoomGraph = () => {
  if (!chart) return;
  
  graphControls.zoomLevel += 0.5;
  if (graphControls.zoomLevel > 3) graphControls.zoomLevel = 1;
  
  chart.dispatchAction({
    type: 'dataZoom',
    start: 0,
    end: 100 / graphControls.zoomLevel
  });
};

// 5. 更多选项菜单
const toggleOptionsMenu = () => {
  graphControls.showOptionsMenu = !graphControls.showOptionsMenu;
  graphControls.showShareMenu = false; // 关闭另一个菜单
};

// 6. 分享菜单
const toggleShareMenu = () => {
  graphControls.showShareMenu = !graphControls.showShareMenu;
  graphControls.showOptionsMenu = false; // 关闭另一个菜单
};

// 导出图谱为图片
const exportImage = () => {
  if (!chart) return;
  
  const url = chart.getDataURL({
    type: 'png',
    pixelRatio: 2,
    backgroundColor: '#fff'
  });
  
  const link = document.createElement('a');
  link.download = '智能建筑知识图谱.png';
  link.href = url;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const handleNodeClick = (params) => {
  if (params.dataType === 'node') {
    selectedNode.value = true;
    const node = currentGraphData.value.nodes.find(n => n.id === params.data.id);
    if (node) {
      selectedSystem.name = node.name;
    }
  }
};

// Change view when tabs are clicked
const switchTab = (tab) => {
  activeTab.value = tab;
  activeScenario.value = null; // Reset active scenario when tab changes
  
  // Switch data based on tab
  switch(tab) {
    case 'system':
      currentGraphData.value = systemGraphData;
      break;
    case 'device':
      currentGraphData.value = deviceGraphData;
      break;
    case 'fault':
      currentGraphData.value = faultGraphData;
      break;
    case 'space':
      currentGraphData.value = spaceGraphData;
      break;
  }
  
  originalGraphData.value = currentGraphData.value;
  updateChart(currentGraphData.value);
  resetControls();
};

// Change view when scenario cards are clicked
const switchScenario = (scenario) => {
  activeScenario.value = scenario;
  
  // Switch data based on scenario
  switch(scenario) {
    case 'dependency':
      currentGraphData.value = dependencyGraphData;
      break;
    case 'expansion':
      currentGraphData.value = expansionGraphData;
      break;
    case 'security':
      currentGraphData.value = securityGraphData;
      break;
  }
  
  originalGraphData.value = currentGraphData.value;
  updateChart(currentGraphData.value);
  resetControls();
};

// 重置控制面板状态
const resetControls = () => {
  graphControls.filtered = false;
  graphControls.hierarchyView = false;
  graphControls.searchActive = false;
  graphControls.zoomLevel = 1;
  graphControls.showOptionsMenu = false;
  graphControls.showShareMenu = false;
  showSearchInput.value = false;
  searchKeyword.value = '';
};

const updateChart = (data) => {
  if (!chart) return;
  
  const option = {
    tooltip: {
      formatter: '{b}'
    },
    legend: {
      show: false
    },
    animationDuration: 1500,
    animationEasingUpdate: 'quinticInOut',
    series: [
      {
        type: 'graph',
        layout: graphControls.hierarchyView ? 'tree' : 'force',
        data: data.nodes,
        links: data.links,
        categories: [
          { name: '主系统' },
          { name: '子系统' },
          { name: '设备' },
          { name: '控制器' },
          { name: '故障点' }
        ],
        roam: true,
        label: {
          show: true,
          position: 'bottom',
          fontSize: 12
        },
        lineStyle: {
          color: 'rgba(100, 100, 150, 0.5)',
          width: 1,
          curveness: 0.3
        },
        itemStyle: {
          color: function(params) {
            // 高亮搜索结果
            if (params.data._highlight) {
              return '#ff9800';
            }
            
            // Use predefined item style if available
            if (params.data.itemStyle && params.data.itemStyle.color) {
              return params.data.itemStyle.color;
            }
            // Default colors by category
            const colors = ['#3366cc', '#4c8dea', '#28a745', '#30c997', '#ff4d4f'];
            return colors[params.data.category] || '#3366cc';
          },
          borderColor: '#fff',
          borderWidth: 2
        },
        emphasis: {
          focus: 'adjacency',
          lineStyle: {
            width: 2
          }
        },
        force: graphControls.hierarchyView ? null : {
          repulsion: 200,
          edgeLength: 120
        }
      }
    ]
  };
  
  chart.setOption(option, true);
};

const initChart = () => {
  if (!graphChart.value) return;
  
  chart = echarts.init(graphChart.value);
  updateChart(currentGraphData.value);
  chart.on('click', handleNodeClick);
  
  window.addEventListener('resize', () => {
    chart.resize();
  });
};

onMounted(() => {
  initChart();
});

// 添加浏览/管理模式切换状态
const isManageMode = ref(false);
const activeManagementTab = ref('disambiguation'); // 默认显示实体消歧标签页

// 按钮交互状态
const btnLoading = reactive({
  validation: false,
  inference: false,
  fusion: false,
  version: false,
  permissions: false
});

// 结果显示状态
const showResults = reactive({
  validation: false,
  inference: false,
  fusion: false
});

// 应用决策动画效果
const applyDecision = () => {
  // 获取表格元素
  const comparisonTable = document.querySelector('.comparison-table');
  const warningMessage = document.querySelector('.warning-message');
  const decisionSection = document.querySelector('.decision-section');
  
  // 添加应用中的状态效果
  if (comparisonTable) {
    comparisonTable.classList.add('applying-decision');
    setTimeout(() => {
      // 添加淡出效果
      comparisonTable.classList.add('fade-out');
      if (warningMessage) warningMessage.classList.add('fade-out');
      if (decisionSection) decisionSection.classList.add('fade-out');
      
      // 延迟后显示成功消息
      setTimeout(() => {
        if (comparisonTable) comparisonTable.style.display = 'none';
        if (warningMessage) warningMessage.style.display = 'none';
        if (decisionSection) decisionSection.style.display = 'none';
        
        // 创建并插入成功消息
        const successMessage = document.createElement('div');
        successMessage.className = 'success-message fade-in';
        successMessage.innerHTML = `
          <i class="fa fa-check-circle"></i>
          <h3>处理成功</h3>
          <p>已成功合并实体并更新知识图谱。</p>
          <div class="entity-stats">
            <div class="stat-box">
              <div class="stat-value">3</div>
              <div class="stat-label">合并实体</div>
            </div>
            <div class="stat-box">
              <div class="stat-value">12</div>
              <div class="stat-label">更新关系</div>
            </div>
          </div>
          <button class="btn btn-next">处理下一实体</button>
        `;
        
        // 将成功消息添加到比较部分
        const comparisonSection = document.querySelector('.comparison-section');
        if (comparisonSection) {
          comparisonSection.appendChild(successMessage);
          
          // 添加下一个按钮的点击事件
          const nextButton = successMessage.querySelector('.btn-next');
          if (nextButton) {
            nextButton.addEventListener('click', () => {
              // 重置状态，模拟处理下一个实体
              if (comparisonTable) {
                comparisonTable.classList.remove('applying-decision', 'fade-out');
                comparisonTable.style.display = '';
              }
              if (warningMessage) {
                warningMessage.classList.remove('fade-out');
                warningMessage.style.display = '';
              }
              if (decisionSection) {
                decisionSection.classList.remove('fade-out');
                decisionSection.style.display = '';
              }
              
              // 移除成功消息
              successMessage.remove();
              
              // 更新左侧实体列表，将当前处理的实体标记为已完成
              const activeEntity = document.querySelector('.entity-item.active');
              if (activeEntity) {
                activeEntity.classList.remove('active');
                activeEntity.style.opacity = '0.6';
                const statusEl = activeEntity.querySelector('.entity-status');
                if (statusEl) statusEl.textContent = '已处理';
                
                // 激活下一个实体
                const nextEntity = activeEntity.nextElementSibling;
                if (nextEntity) {
                  nextEntity.classList.add('active');
                  const nextStatusEl = document.createElement('div');
                  nextStatusEl.className = 'entity-status';
                  nextStatusEl.textContent = '当前查中';
                  nextEntity.appendChild(nextStatusEl);
                }
              }
            });
          }
        }
      }, 500);
    }, 1000);
  }
};

// 图谱验证按钮点击
const startValidation = () => {
  btnLoading.validation = true;
  
  // 模拟延迟加载
  setTimeout(() => {
    btnLoading.validation = false;
    showResults.validation = true;
    
    // 显示验证结果面板
    const resultsPanel = document.querySelector('.validation-results-panel');
    if (resultsPanel) {
      resultsPanel.style.display = 'block';
    }
    
    // 更新验证数据
    updateValidationData();
  }, 2000);
};

// 更新验证数据
const updateValidationData = () => {
  // 更新完整性检查
  const checkValues = document.querySelectorAll('.check-value.empty');
  checkValues.forEach(value => {
    const randomValue = Math.floor(Math.random() * 5);
    value.textContent = randomValue;
    value.classList.remove('empty');
    if (randomValue > 0) {
      value.classList.add('error');
    }
  });
  
  // 更新总分
  const scoreDisplay = document.querySelector('.overall-score');
  if (scoreDisplay) {
    scoreDisplay.innerHTML = '78 <span class="score-unit">/100</span>';
  }
  
  // 更新分项评分
  const scoreBars = document.querySelectorAll('.score-bar');
  const scoreValues = document.querySelectorAll('.score-item .score-value');
  
  if (scoreBars.length === scoreValues.length) {
    for (let i = 0; i < scoreBars.length; i++) {
      const score = 70 + Math.floor(Math.random() * 20);
      scoreBars[i].style.width = score + '%';
      scoreValues[i].textContent = score;
      scoreValues[i].classList.remove('empty');
    }
  }
};

// 修复验证问题
const fixIssue = (event) => {
  const btn = event.target;
  const row = btn.closest('tr');
  
  if (row) {
    // 设置按钮加载状态
    btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
    btn.disabled = true;
    
    // 模拟修复延迟
    setTimeout(() => {
      // 更新行状态
      row.style.opacity = '0.5';
      row.style.textDecoration = 'line-through';
      btn.textContent = '已修复';
      
      // 更新问题计数
      const issueCount = document.querySelector('.issue-count');
      const criticalCount = document.querySelector('.critical-count');
      
      if (issueCount) {
        issueCount.textContent = parseInt(issueCount.textContent) - 1;
      }
      
      if (criticalCount && row.querySelector('.severity.high')) {
        criticalCount.textContent = parseInt(criticalCount.textContent) - 1;
      }
    }, 1500);
  }
};

// 运行推理引擎
const runInference = () => {
  btnLoading.inference = true;
  
  // 隐藏空状态显示
  const emptyState = document.querySelector('.inference-empty-state');
  if (emptyState) {
    emptyState.style.display = 'none';
  }
  
  // 创建加载状态
  const resultsContent = document.querySelector('.inference-results-content');
  if (resultsContent) {
    const loading = document.createElement('div');
    loading.className = 'inference-loading';
    loading.innerHTML = `
      <div class="loading-spinner">
        <i class="fa fa-cog fa-spin"></i>
      </div>
      <div class="loading-text">正在运行推理引擎，分析潜在关系...</div>
    `;
    resultsContent.appendChild(loading);
    
    // 模拟延迟加载
    setTimeout(() => {
      // 移除加载状态
      loading.remove();
      
      // 显示结果
      btnLoading.inference = false;
      showResults.inference = true;
      
      // 显示结果列表
      const resultsList = document.querySelector('.inference-results-list');
      if (resultsList) {
        resultsList.style.display = 'block';
      }
      
      // 更新统计数据
      const inferredCount = document.querySelector('.blue-box .stat-number');
      if (inferredCount) {
        inferredCount.textContent = '3';
      }
      
      // 更新最后运行时间
      const lastRunInfo = document.querySelector('.last-run-info');
      if (lastRunInfo) {
        const now = new Date();
        lastRunInfo.textContent = `上次推理: ${now.getHours()}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
      }
    }, 2000);
  }
};

// 处理推理结果
const handleInferenceResult = (event, accept) => {
  const btn = event.target;
  const row = btn.closest('tr');
  const otherBtn = accept ? 
    row.querySelector('.btn-reject') : 
    row.querySelector('.btn-accept');
  
  if (row && otherBtn) {
    // 设置按钮加载状态
    btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
    btn.disabled = true;
    otherBtn.disabled = true;
    
    // 模拟处理延迟
    setTimeout(() => {
      if (accept) {
        // 接受推理
        row.style.backgroundColor = '#f6ffed';
        btn.textContent = '已接受';
        
        // 更新统计
        const appliedCount = document.querySelector('.green-box .stat-number');
        if (appliedCount) {
          appliedCount.textContent = parseInt(appliedCount.textContent) + 1;
        }
      } else {
        // 拒绝推理
        row.style.backgroundColor = '#fff1f0';
        row.style.opacity = '0.6';
        btn.textContent = '已拒绝';
      }
    }, 1000);
  }
};

// 启动融合
const startFusion = () => {
  btnLoading.fusion = true;
  
  // 模拟延迟加载
  setTimeout(() => {
    btnLoading.fusion = false;
    showResults.fusion = true;
    
    // 显示冲突表格行
    const rows = document.querySelectorAll('.conflict-table tbody tr');
    rows.forEach((row, index) => {
      setTimeout(() => {
        row.style.display = 'table-row';
        row.style.animation = 'fadeIn 0.5s forwards';
      }, index * 300);
    });
  }, 2000);
};

// 解决冲突
const resolveConflict = (event) => {
  const btn = event.target;
  const row = btn.closest('tr');
  
  if (row) {
    // 设置按钮加载状态
    btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
    btn.disabled = true;
    
    // 模拟处理延迟
    setTimeout(() => {
      // 更新行状态
      row.style.opacity = '0.5';
      row.style.textDecoration = 'line-through';
      btn.textContent = '已解决';
      
      // 更新图表预览
      const chartPlaceholder = document.querySelector('.chart-placeholder');
      if (chartPlaceholder) {
        chartPlaceholder.innerHTML = `
          <div class="chart-preview-active">
            <i class="fa fa-check-circle" style="color: #52c41a; font-size: 24px; margin-bottom: 10px;"></i>
            <div>已解决 1/3 个冲突</div>
            <div style="font-size: 12px; color: #999; margin-top: 5px;">可以在解决所有冲突后查看完整预览</div>
          </div>
        `;
      }
    }, 1500);
  }
};

// 创建版本快照
const createSnapshot = () => {
  btnLoading.version = true;
  
  // 模拟延迟加载
  setTimeout(() => {
    btnLoading.version = false;
    
    // 获取版本列表
    const versionList = document.querySelector('.version-list');
    if (versionList) {
      // 创建新版本元素
      const now = new Date();
      const dateStr = `${now.getFullYear()}/${now.getMonth()+1}/${now.getDate()} ${now.getHours()}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
      
      const newVersion = document.createElement('div');
      newVersion.className = 'version-item';
      newVersion.style.opacity = '0';
      newVersion.innerHTML = `
        <div class="version-icon-container">
          <div class="version-circle" style="background-color: #f6ffed; color: #52c41a;">
            <i class="fa fa-plus"></i>
          </div>
        </div>
        
        <div class="version-info">
          <div class="version-name">
            更新版本 v1.1.0
            <span class="version-tag">当前</span>
          </div>
          <div class="version-desc">用户创建的快照</div>
          
          <div class="version-actions">
            <a href="#" class="version-action">
              <i class="fa fa-reply"></i> 回溯
            </a>
            <a href="#" class="version-action">
              <i class="fa fa-exchange"></i> 对比
            </a>
            <a href="#" class="version-action">
              <i class="fa fa-history"></i> 变更历史
            </a>
          </div>
        </div>
        
        <div class="version-meta">
          <div class="version-time">${dateStr}</div>
          <div class="version-author">当前用户</div>
        </div>
      `;
      
      // 移除当前标签
      const existingVersions = versionList.querySelectorAll('.version-item');
      existingVersions.forEach(version => {
        const tag = version.querySelector('.version-tag');
        if (tag) tag.remove();
      });
      
      // 插入新版本
      versionList.insertBefore(newVersion, versionList.firstChild);
      
      // 应用动画
      setTimeout(() => {
        newVersion.style.opacity = '1';
        newVersion.style.transition = 'opacity 0.5s ease';
      }, 100);
      
      // 更新统计
      const statNumber = document.querySelector('.version-stats .stat-number');
      const statDate = document.querySelector('.version-stats .stat-date');
      
      if (statNumber) {
        statNumber.textContent = parseInt(statNumber.textContent) + 1;
      }
      
      if (statDate) {
        statDate.textContent = `${now.getFullYear()}/${now.getMonth()+1}/${now.getDate()}`;
      }
    }
  }, 2000);
};

// 保存权限设置
const savePermissions = () => {
  btnLoading.permissions = true;
  
  // 模拟延迟加载
  setTimeout(() => {
    btnLoading.permissions = false;
    
    // 显示成功消息
    const permissionsContent = document.querySelector('.permissions-content');
    if (permissionsContent) {
      const message = document.createElement('div');
      message.className = 'permission-success-message';
      message.innerHTML = '<i class="fa fa-check-circle"></i> 权限设置已保存';
      permissionsContent.appendChild(message);
      
      // 自动消失
      setTimeout(() => {
        message.style.opacity = '0';
        setTimeout(() => {
          message.remove();
        }, 500);
      }, 3000);
    }
  }, 1500);
};

// 添加事件监听器
onMounted(() => {
  initChart();
  
  // 添加按钮点击事件监听
  document.addEventListener('click', (e) => {
    // 图谱验证
    if (e.target.closest('.validation-btn.primary')) {
      runValidation();
    }
    
    // 修复验证问题
    if (e.target.closest('.btn-fix')) {
      fixIssue(e);
    }
    
    // 关系推理
    if (e.target.closest('.run-inference-btn .primary-btn')) {
      runInference();
    }
    
    // 处理推理结果
    if (e.target.closest('.btn-accept')) {
      handleInferenceResult(e, true);
    }
    
    if (e.target.closest('.btn-reject')) {
      handleInferenceResult(e, false);
    }
    
    // 图谱融合
    if (e.target.closest('.workspace-actions .btn-primary')) {
      startFusion();
    }
    
    // 解决冲突
    if (e.target.closest('.btn-resolve')) {
      resolveConflict(e);
    }
    
    // 版本管理
    if (e.target.closest('.btn-create-version')) {
      createSnapshot();
    }
    
    // 保存权限设置
    if (e.target.closest('.btn-save-permissions')) {
      savePermissions();
    }
  });
});
</script>

<style scoped>
.graph-container {
  display: flex;
  height: 100vh;
  background-color: #f8f8f8;
  color: #333;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
}

.main-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.content-section {
  margin-bottom: 25px;
}

.header-section {
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
  position: relative;
}

.scene-section {
  padding-top: 10px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.business-section {
  padding-top: 10px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.graph-section {
  padding-top: 10px;
  position: relative;
}

.graph-and-details {
  display: flex;
  gap: 20px;
}

.graph-controls {
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  width: 40px;
  align-self: stretch;
}

.control-item {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
}

.control-item.active {
  background-color: #e6f7ff;
  color: #1677ff;
}

.control-item:hover {
  background-color: #f0f2f5;
}

.control-item i {
  font-size: 16px;
  color: #666;
}

.control-item.active i {
  color: #1677ff;
}

.zoom-level {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 10px;
  background-color: #1677ff;
  color: white;
  border-radius: 8px;
  padding: 1px 4px;
  transform: translate(25%, -25%);
}

.graph-view {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
  flex: 1;
  height: 600px;
  position: relative;
  overflow: hidden;
}

/* Header styles */ 
.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.05);
  margin-right: 80px; /* 给右侧留出空间，确保不会被切换按钮遮挡 */
}

.header-title h1 {
  font-size: 20px;
  font-weight: bold;
  margin: 0;
}

.stats-container {
  display: flex;
  color: #666;
  font-size: 14px;
}

.stat-item {
  margin-right: 16px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.action-button {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  font-size: 14px;
  cursor: pointer;
}

.action-button.primary {
  background-color: #3a7eff;
  color: white;
}

.action-button.secondary {
  background-color: white;
  color: #3a7eff;
  border: 1px solid #3a7eff;
}

.action-button i {
  margin-right: 6px;
}

.scene-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
  padding-left: 5px;
}

.scene-desc {
  color: #999;
  font-size: 14px;
  font-weight: normal;
  margin-left: 10px;
}

.graph-tabs {
  display: flex;
  margin-bottom: 0;
  gap: 15px;
}

.tab {
  flex: 1;
  background-color: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab.active {
  border-left: 3px solid #1677ff;
}

.tab i {
  font-size: 18px;
  color: #1677ff;
  margin-right: 8px;
}

.tab-desc {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.scenario-title {
  font-size: 16px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  padding-left: 5px;
}

.scenario-title i {
  color: #ff9800;
  margin-right: 8px;
  font-size: 18px;
}

.scenario-cards {
  display: flex;
  gap: 15px;
  margin-bottom: 0;
}

.scenario-card {
  flex: 1;
  background-color: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.scenario-card.active {
  border-left: 3px solid #1677ff;
  background-color: #f0f7ff;
}

.scenario-card:hover {
  box-shadow: 0 4px 10px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.scenario-card i {
  color: #1677ff;
  margin-right: 8px;
  font-size: 18px;
}

.card-desc {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.graph-chart {
  width: 100%;
  height: 100%;
}

.graph-legend {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 8px 12px;
  border-radius: 4px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  font-size: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
  display: inline-block;
}

.legend-dot.system {
  background-color: #3366cc;
}

.legend-dot.device {
  background-color: #4c8dea;
}

.legend-dot.relation {
  background-color: #28a745;
}

.legend-dot.alert {
  background-color: #f5222d;
}

.legend-dot.controller {
  background-color: #30c997;
}

/* Node details panel */
.node-details {
  width: 320px;
  background-color: white;
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
  padding: 20px;
  border-radius: 8px;
  height: 600px;
  overflow-y: auto;
}

.details-title {
  font-size: 16px;
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.system-info {
  margin-bottom: 20px;
}

.system-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}

.system-status {
  font-size: 14px;
  color: #52c41a;
  display: flex;
  align-items: center;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
  display: inline-block;
}

.status-indicator.normal {
  background-color: #52c41a;
}

.status-indicator.warning {
  background-color: #faad14;
}

.status-indicator.error {
  background-color: #f5222d;
}

.details-section {
  margin-bottom: 20px;
  background-color: #f9f9f9;
  border-radius: 6px;
  padding: 15px;
}

.section-title {
  font-size: 14px;
  margin: 0 0 15px 0;
}

.property-item, .relation-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 14px;
}

.property-label, .relation-label {
  color: #666;
}

.property-value, .relation-value {
  color: #333;
  font-weight: 500;
}

.link-btn {
  color: #1677ff;
  text-decoration: none;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.action-btn {
  flex: 1;
  padding: 8px 0;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.action-btn.primary {
  background-color: #1677ff;
  color: white;
}

.action-btn.secondary {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #d9d9d9;
}

.action-btn i {
  margin-right: 6px;
}

/* 搜索框 */
.search-overlay {
  position: absolute;
  top: 0;
  left: 60px;
  z-index: 10;
  padding: 10px;
}

.search-container {
  display: flex;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.search-container input {
  width: 200px;
  padding: 8px 12px;
  border: none;
  outline: none;
  font-size: 14px;
}

.search-container button {
  background-color: #f0f0f0;
  border: none;
  padding: 0 10px;
  cursor: pointer;
}

.search-container button:hover {
  background-color: #e0e0e0;
}

.search-container .close-btn {
  background-color: #f5f5f5;
}

/* 菜单样式 */
.options-menu,
.share-menu {
  position: absolute;
  left: 60px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  z-index: 10;
  overflow: hidden;
  width: 160px;
}

.options-menu {
  top: 120px;
}

.share-menu {
  top: 240px;
}

.menu-item {
  padding: 10px 15px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.menu-item:hover {
  background-color: #f0f7ff;
}

.menu-item i {
  margin-right: 8px;
  width: 16px;
  color: #1677ff;
}

/* 浏览/管理切换按钮样式 */
.mode-switch {
  position: fixed;
  top: 120px;
  right: 25px;
  display: flex;
  background-color: #F1F4FA;
  border-radius: 4px;
  z-index: 1000;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  overflow: hidden;
}

.mode-option {
  padding: 10px 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #666;
  user-select: none;
}

.mode-option.active {
  background-color: #2468f2;
  color: white;
  font-weight: 500;
}

.mode-option:hover:not(.active) {
  background-color: #e6e6e6;
}

/* 删除旧的切换按钮样式 */
.mode-label {
  display: none;
}

.switch {
  display: none;
}

.slider {
  display: none;
}

/* 管理模式样式 */
.management-content {
  flex: 1;
  padding: 20px;
  background-color: #f5f7fa;
  height: 100vh;
  overflow-y: auto;
}

.management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.management-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.management-actions {
  display: flex;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-primary {
  background-color: #5b6bfa;
  color: white;
}

.btn-success {
  background-color: #52c41a;
  color: white;
}

.btn-apply {
  background-color: #1890ff;
  color: white;
  padding: 6px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}

.management-tabs {
  display: flex;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 20px;
}

.management-tab-item {
  padding: 12px 20px;
  cursor: pointer;
  font-size: 14px;
  position: relative;
  color: #666;
}

.management-tab-item.active {
  color: #2468f2;
  font-weight: 500;
}

.management-tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #2468f2;
}

.disambiguation-tool {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.tool-header {
  display: flex;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #f8fafd;
  border-radius: 8px 8px 0 0;
}

.tool-icon {
  background-color: #e6f7ff;
  color: #1890ff;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.tool-icon i {
  font-size: 20px;
}

.tool-title {
  margin: 0 0 6px 0;
  font-size: 16px;
  font-weight: 600;
}

.tool-desc {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.disambiguation-content {
  display: flex;
  padding: 20px;
}

.entity-section {
  width: 280px;
  margin-right: 20px;
}

.entity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.entity-header h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
}

.entity-count {
  background-color: #f0f0f0;
  color: #666;
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
}

.entity-list {
  background-color: #f9f9f9;
  border-radius: 4px;
}

.entity-item {
  position: relative;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  display: flex;
  flex-direction: column;
}

.entity-item:last-child {
  border-bottom: none;
}

.entity-item.active {
  background-color: #e6f7ff;
}

.entity-marker {
  width: 5px;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  background-color: #1890ff;
  opacity: 0;
}

.entity-item.active .entity-marker {
  opacity: 1;
}

.entity-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.entity-count-small {
  font-size: 12px;
  color: #999;
}

.entity-status {
  position: absolute;
  right: 12px;
  top: 12px;
  background-color: #e6f7ff;
  color: #1890ff;
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 12px;
}

.comparison-section {
  flex: 1;
}

.comparison-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.comparison-header h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
}

.warning-message {
  background-color: #fff9e6;
  border: 1px solid #ffe58f;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 20px;
  color: #d48806;
  font-weight: 500;
}

.warning-message i {
  margin-right: 8px;
}

.warning-message p {
  margin: 8px 0 0 0;
  font-weight: normal;
  color: #666;
  font-size: 13px;
}

.comparison-table {
  margin-bottom: 20px;
}

.comparison-table table {
  width: 100%;
  border-collapse: collapse;
}

.comparison-table th, .comparison-table td {
  border: 1px solid #eee;
  padding: 10px;
  text-align: left;
}

.comparison-table th {
  background-color: #f5f5f5;
  font-weight: 500;
}

.comparison-table th:first-child {
  width: 100px;
}

.decision-section h3 {
  margin: 0 0 12px 0;
  font-size: 15px;
  font-weight: 500;
}

.decision-option {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  cursor: pointer;
}

.decision-option input {
  margin-top: 2px;
  margin-right: 8px;
}

/* 应用决策的动画效果 */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.applying-decision {
  animation: pulse 0.8s infinite;
  position: relative;
}

.applying-decision::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(24, 144, 255, 0.1);
  z-index: 1;
}

.fade-out {
  animation: fadeOut 0.5s forwards;
}

.fade-in {
  animation: fadeIn 0.5s forwards;
}

.success-message {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  padding: 24px;
  text-align: center;
  color: #52c41a;
  margin-top: 20px;
}

.success-message i {
  font-size: 48px;
  margin-bottom: 16px;
}

.success-message h3 {
  font-size: 18px;
  margin: 0 0 10px 0;
  color: #52c41a;
}

.success-message p {
  color: #666;
  margin-bottom: 20px;
}

.entity-stats {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin: 20px 0;
}

.stat-box {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.btn-next {
  background-color: #52c41a;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  margin-top: 10px;
}

.btn-next:hover {
  background-color: #389e0d;
}

/* 图谱融合工具样式 */
.fusion-tool {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.fusion-icon {
  background-color: #e6f4ff;
  color: #1677ff;
}

.fusion-content {
  display: flex;
  padding: 20px;
  gap: 20px;
}

.data-sources-section {
  width: 300px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
}

.source-count {
  background-color: #f0f0f0;
  color: #666;
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
}

.sources-list {
  background-color: #f9f9f9;
  border-radius: 4px;
}

.source-item {
  position: relative;
  padding: 15px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.source-item:last-child {
  border-bottom: none;
}

.source-item.active {
  background-color: #e6f7ff;
}

.source-marker {
  width: 4px;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  background-color: #1890ff;
  opacity: 0;
}

.source-item.active .source-marker {
  opacity: 1;
}

.source-name {
  font-weight: 500;
  margin-bottom: 6px;
}

.source-info {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.source-date {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.source-quality {
  display: flex;
  align-items: center;
}

.quality-bar {
  flex: 1;
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  margin-right: 8px;
}

.quality-fill {
  height: 100%;
  background-color: #52c41a;
  border-radius: 3px;
}

.quality-value {
  font-size: 12px;
  color: #52c41a;
  font-weight: 500;
}

.add-source {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  color: #1890ff;
  background-color: #f0f7ff;
}

.add-icon {
  margin-right: 8px;
}

.add-text {
  font-size: 14px;
}

.fusion-workspace {
  flex: 1;
}

.workspace-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.workspace-header h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
}

.workspace-actions {
  display: flex;
  gap: 10px;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-default {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #d9d9d9;
}

.fusion-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  background-color: white;
  border-radius: 6px;
  padding: 15px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.stat-icon i {
  font-size: 20px;
}

.conflict-icon {
  background-color: #fff2e8;
  color: #fa541c;
}

.entity-icon {
  background-color: #e6f7ff;
  color: #1890ff;
}

.relation-icon {
  background-color: #f6ffed;
  color: #52c41a;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.conflict-list {
  background-color: white;
  border-radius: 6px;
  padding: 15px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  margin-bottom: 20px;
}

.conflict-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.conflict-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.filter-options {
  display: flex;
  gap: 10px;
}

.filter-label {
  font-size: 12px;
  color: #666;
  padding: 2px 8px;
  border-radius: 10px;
  cursor: pointer;
  background-color: #f5f5f5;
}

.filter-label.active {
  background-color: #e6f7ff;
  color: #1890ff;
}

.conflict-table {
  width: 100%;
  border-collapse: collapse;
}

.conflict-table th, .conflict-table td {
  padding: 10px;
  text-align: left;
  font-size: 13px;
}

.conflict-table th {
  color: #666;
  font-weight: 500;
  border-bottom: 1px solid #f0f0f0;
}

.conflict-type {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 2px;
  font-size: 12px;
}

.conflict-type.attr {
  background-color: #f9f0ff;
  color: #722ed1;
}

.conflict-type.rel {
  background-color: #fcf4e6;
  color: #fa8c16;
}

.conflict-type.name {
  background-color: #e6fffb;
  color: #13c2c2;
}

.btn-resolve {
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 2px;
  cursor: pointer;
  font-size: 12px;
}

.fusion-preview {
  background-color: white;
  border-radius: 6px;
  padding: 15px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.preview-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.preview-actions {
  display: flex;
  gap: 5px;
}

.btn-preview {
  background: none;
  border: 1px solid #d9d9d9;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #666;
}

.btn-preview.active {
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
}

.preview-chart {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #999;
}

.chart-icon {
  font-size: 36px;
  margin-bottom: 10px;
  color: #d9d9d9;
}

.chart-text {
  font-size: 14px;
  margin-bottom: 5px;
}

.chart-subtext {
  font-size: 12px;
  color: #bbb;
}

/* 图谱验证工具样式 */
.validation-tool {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.validation-icon {
  background-color: #f0f9ff;
  color: #1890ff;
}

.validation-content {
  padding: 20px;
}

.validation-actions {
  display: flex;
  margin-bottom: 20px;
  align-items: center;
}

.validation-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  margin-right: 10px;
}

.validation-btn.primary {
  background-color: #1890ff;
  color: white;
}

.validation-btn.secondary {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #d9d9d9;
}

.validation-btn.export {
  background-color: #52c41a;
  color: white;
}

.spacer {
  flex: 1;
}

.validation-panels {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.validation-panel {
  flex: 1;
  background-color: #f9f9f9;
  border-radius: 8px;
  overflow: hidden;
}

.panel-header {
  background-color: #f5f5f5;
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
}

.panel-header h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.panel-content {
  padding: 15px;
}

.check-item {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.check-item:last-child {
  border-bottom: none;
}

.check-label {
  color: #666;
}

.check-value {
  font-weight: 500;
}

.check-value.empty {
  color: #bbb;
}

.check-value.error {
  color: #ff4d4f;
}

.quality-panel .panel-content {
  padding-top: 5px;
}

.score-display {
  text-align: center;
  margin-bottom: 20px;
}

.overall-score {
  font-size: 30px;
  font-weight: 600;
  color: #1890ff;
}

.score-unit {
  font-size: 16px;
  color: #999;
  font-weight: normal;
}

.score-details {
  margin-top: 10px;
}

.score-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.score-label {
  width: 60px;
  color: #666;
}

.score-bar-container {
  flex: 1;
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  margin: 0 15px;
  overflow: hidden;
}

.score-bar {
  height: 100%;
  background-color: #1890ff;
  border-radius: 3px;
  transition: width 0.5s ease;
}

.score-value {
  width: 30px;
  text-align: right;
  font-weight: 500;
}

/* 验证结果面板 */
.validation-results-panel {
  background-color: #f9f9f9;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 20px;
}

.results-header {
  background-color: #f5f5f5;
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-header h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.results-summary {
  font-size: 14px;
  color: #666;
}

.issue-count, .critical-count {
  color: #ff4d4f;
  font-weight: 600;
}

.results-content {
  padding: 15px;
}

.results-filters {
  display: flex;
  margin-bottom: 15px;
  align-items: center;
  gap: 15px;
}

.filter-group {
  display: flex;
  align-items: center;
}

.filter-group label {
  margin-right: 8px;
  font-size: 13px;
  color: #666;
}

.filter-group select {
  padding: 6px 10px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: white;
  font-size: 13px;
}

.search-group {
  flex: 1;
  display: flex;
  align-items: center;
}

.search-group input {
  flex: 1;
  padding: 6px 10px;
  border: 1px solid #d9d9d9;
  border-radius: 4px 0 0 4px;
  font-size: 13px;
}

.search-group button {
  padding: 6px 10px;
  background-color: #f0f0f0;
  border: 1px solid #d9d9d9;
  border-left: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
}

.results-table-container {
  max-height: 300px;
  overflow-y: auto;
}

.results-table {
  width: 100%;
  border-collapse: collapse;
}

.results-table th, .results-table td {
  padding: 10px;
  text-align: left;
  font-size: 13px;
  border-bottom: 1px solid #f0f0f0;
}

.results-table th {
  background-color: #fafafa;
  color: #666;
  font-weight: 500;
}

.severity {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
}

.severity.high {
  background-color: #fff1f0;
  color: #ff4d4f;
}

.severity.medium {
  background-color: #fff7e6;
  color: #fa8c16;
}

.severity.low {
  background-color: #f0f5ff;
  color: #1890ff;
}

.btn-fix {
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 2px;
  cursor: pointer;
  font-size: 12px;
}

/* 关系推理工具样式 */
.inference-tool {
  margin-bottom: 20px;
}

.inference-layout {
  display: flex;
  gap: 20px;
}

.inference-left-panel {
  width: 35%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.inference-right-panel {
  width: 65%;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.inference-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  height: fit-content;
}

.inference-card-header {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.rules-count {
  color: #666;
  font-size: 14px;
}

.inference-card-content {
  padding: 15px 20px;
}

.rules-list {
  margin-bottom: 20px;
}

.rule-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.rule-item:last-child {
  border-bottom: none;
}

.rule-info {
  flex: 1;
}

.rule-name {
  font-weight: 500;
  margin-bottom: 4px;
  color: #1890ff;
}

.rule-desc {
  font-size: 13px;
  color: #666;
}

.rule-toggle {
  margin-left: 15px;
  margin-top: 2px;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 22px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
}

input:checked + .slider {
  background-color: #1890ff;
}

input:focus + .slider {
  box-shadow: 0 0 1px #1890ff;
}

input:checked + .slider:before {
  transform: translateX(22px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

.run-inference-btn {
  margin-top: 10px;
}

.primary-btn {
  width: 100%;
  padding: 10px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.secondary-btn {
  padding: 8px 16px;
  background-color: white;
  color: #1890ff;
  border: 1px solid #1890ff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.stats-card {
  height: auto;
}

.stats-container {
  display: flex;
  gap: 15px;
}

.stat-box {
  flex: 1;
  padding: 15px;
  border-radius: 6px;
  text-align: center;
}

.blue-box {
  background-color: #f0f5ff;
  color: #1890ff;
}

.green-box {
  background-color: #f6ffed;
  color: #52c41a;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 13px;
}

.last-run-info {
  margin-top: 15px;
  text-align: center;
  color: #999;
  font-size: 13px;
}

.inference-results-header {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.results-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.results-actions {
  display: flex;
  gap: 10px;
}

.inference-results-content {
  padding: 20px;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.inference-empty-state {
  text-align: center;
  color: #999;
}

.empty-icon {
  font-size: 40px;
  color: #d9d9d9;
  margin-bottom: 10px;
}

.empty-text {
  font-size: 16px;
}

.inference-results-list {
  width: 100%;
}

.inference-table {
  width: 100%;
  border-collapse: collapse;
}

.inference-table th, 
.inference-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

.inference-table th {
  background-color: #fafafa;
  color: #666;
  font-weight: 500;
  font-size: 14px;
}

.confidence-level {
  display: flex;
  align-items: center;
}

.confidence-bar {
  height: 6px;
  border-radius: 3px;
  margin-right: 8px;
}

.confidence-level.high .confidence-bar {
  background-color: #52c41a;
  width: 87%;
}

.confidence-level.medium .confidence-bar {
  background-color: #faad14;
  width: 65%;
}

.confidence-level.low .confidence-bar {
  background-color: #f5222d;
  width: 42%;
}

.confidence-level.high {
  color: #52c41a;
}

.confidence-level.medium {
  color: #faad14;
}

.confidence-level.low {
  color: #f5222d;
}

.btn-accept, 
.btn-reject {
  padding: 4px 10px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  border: none;
}

.btn-accept {
  background-color: #52c41a;
  color: white;
  margin-right: 5px;
}

.btn-reject {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #d9d9d9;
}

/* 版本管理工具样式 */
.version-tool {
  margin-bottom: 20px;
}

.version-icon {
  background-color: #eaeffd;
  color: #4263eb;
}

.version-content {
  padding: 20px;
}

.version-layout {
  display: flex;
  gap: 20px;
}

.version-main-panel {
  flex: 7;
}

.version-side-panel {
  flex: 4;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.version-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 0;
}

.version-card-header {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.btn-create-version {
  padding: 6px 15px;
  background-color: #4263eb;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.version-list {
  padding: 0;
}

.version-item {
  display: flex;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.version-icon-container {
  margin-right: 15px;
  position: relative;
}

.version-circle {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #e6f7ff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
}

.version-info {
  flex: 1;
}

.version-name {
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}

.version-tag {
  padding: 2px 8px;
  background-color: #e6f7ff;
  color: #1890ff;
  border-radius: 10px;
  font-size: 12px;
  margin-left: 10px;
}

.version-desc {
  color: #666;
  margin-bottom: 10px;
  font-size: 14px;
}

.version-actions {
  display: flex;
  gap: 15px;
}

.version-action {
  color: #1890ff;
  text-decoration: none;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.version-meta {
  min-width: 140px;
  text-align: right;
}

.version-time {
  color: #999;
  font-size: 13px;
  margin-bottom: 5px;
}

.version-author {
  color: #666;
  font-size: 14px;
}

.permissions-content {
  padding: 15px 20px;
}

.permission-section {
  margin-bottom: 20px;
}

.permission-section:last-child {
  margin-bottom: 0;
}

.permission-label {
  font-weight: 500;
  margin-bottom: 10px;
  color: #333;
}

.permission-selector {
  position: relative;
}

.form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  appearance: none;
  font-size: 14px;
  color: #333;
}

.select-arrow {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  pointer-events: none;
}

.user-groups {
  background-color: #f9f9f9;
  border-radius: 4px;
  overflow: hidden;
}

.user-group-item {
  display: flex;
  justify-content: space-between;
  padding: 10px 15px;
  border-bottom: 1px solid #f0f0f0;
}

.user-group-item:last-child {
  border-bottom: none;
}

.user-group-name {
  color: #333;
  font-weight: 500;
}

.user-group-role {
  color: #666;
}

.checkbox-container {
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 25px;
  cursor: pointer;
  user-select: none;
}

.checkbox-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 16px;
  width: 16px;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
}

.checkbox-container:hover input ~ .checkmark {
  background-color: #f5f5f5;
}

.checkbox-container input:checked ~ .checkmark {
  background-color: #1890ff;
  border-color: #1890ff;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.checkbox-container input:checked ~ .checkmark:after {
  display: block;
}

.checkbox-container .checkmark:after {
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.permission-actions {
  margin-top: 20px;
  text-align: center;
}

.btn-save-permissions {
  width: 100%;
  padding: 10px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.version-stats {
  padding: 15px 20px;
  display: flex;
}

.version-stats .stat-box {
  padding: 15px;
  border-radius: 6px;
  text-align: center;
  flex: 1;
}

.light-blue {
  background-color: #f0f7ff;
  color: #4263eb;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 5px;
}

.stat-date {
  font-size: 14px;
  color: inherit;
  opacity: 0.7;
}

/* 添加新的交互样式 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.conflict-table tbody tr {
  display: none;
}

.inference-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.loading-spinner {
  font-size: 36px;
  color: #1890ff;
  margin-bottom: 15px;
}

.loading-text {
  color: #666;
}

.save-success-message {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  padding: 10px;
  border-radius: 4px;
  margin-top: 15px;
  display: flex;
  align-items: center;
  color: #52c41a;
  transition: opacity 0.5s ease;
}

.save-success-message i {
  margin-right: 8px;
}

.chart-preview-active {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 交互相关样式 */
.inference-loading {
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  font-size: 40px;
  color: #1890ff;
  margin-bottom: 15px;
}

.loading-text {
  color: #666;
}

.permission-success-message {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  padding: 10px;
  border-radius: 4px;
  margin-top: 15px;
  color: #52c41a;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.5s ease;
}

.permission-success-message i {
  margin-right: 8px;
}

/* 点击实体项时的交互样式 */
.entity-item {
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.entity-item:hover:not(.active) {
  background-color: #f5f5f5;
}

/* 按钮禁用状态 */
button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
</style>