/**
 * RAG (Retrieval-Augmented Generation) API 请求工具
 * 用于处理与知识库检索相关的API请求
 */

import axios from 'axios';

// 默认配置
const ragService = {
  // 基础URL，根据实际部署环境修改
  baseURL: 'https://ragflow-4029862aab3ad76af3e3a564499a320b.zcznbj.com',
  
  // 访问令牌，实际应用中应从登录或环境变量中获取
  token: 'YOUR_ACCESS_TOKEN',
  
  // 创建请求实例
  createRequest() {
    return axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json',
        'Accept': '*/*'
      },
      timeout: 30000 // 30秒超时
    });
  },
  
  // 设置新的访问令牌
  setToken(newToken) {
    this.token = newToken;
  },
  
  // 设置新的基础URL
  setBaseURL(newBaseURL) {
    this.baseURL = newBaseURL;
  },
  
  // 发送GET请求
  async get(url, params = {}) {
    try {
      const request = this.createRequest();
      const response = await request.get(url, { params });
      return response.data;
    } catch (error) {
      console.error('RAG GET请求错误:', error);
      throw error;
    }
  },
  
  // 发送POST请求
  async post(url, data = {}) {
    try {
      const request = this.createRequest();
      const response = await request.post(url, data);
      return response.data;
    } catch (error) {
      console.error('RAG POST请求错误:', error);
      throw error;
    }
  },
  
  // 发送带流式响应的POST请求
  async postStream(url, data = {}, onProgress) {
    try {
      const request = this.createRequest();
      const response = await request.post(url, data, {
        responseType: 'stream',
        onDownloadProgress: onProgress
      });
      return response;
    } catch (error) {
      console.error('RAG 流式请求错误:', error);
      throw error;
    }
  }
};

export default ragService; 