<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户账号" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入用户账号" clearable />
      </el-form-item>
      <el-form-item label="公司" prop="company">
        <el-input v-model="queryParams.company" placeholder="请输入公司" clearable />
      </el-form-item>
      <el-form-item label="用户昵称" prop="nickName">
        <el-input v-model="queryParams.nickName" placeholder="请输入用户昵称" clearable />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input v-model="queryParams.mobile" placeholder="请输入手机号" clearable />
      </el-form-item>
      <el-form-item label="用户邮箱" prop="email">
        <el-input v-model="queryParams.email" placeholder="请输入用户邮箱" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="cyan" icon="Search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" size="mini" @click="handleAdd" v-hasPermi="['system:tenant:add']">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:tenant:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:tenant:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Upload" size="mini" @click="handleExport"
          v-hasPermi="['system:tenant:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tenantList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="租户id" align="center" prop="id" />
      <el-table-column label="租户标志" align="center" prop="tenantKey" />
      <el-table-column label="用户账号" align="center" prop="userName" />
      <el-table-column label="公司" align="center" prop="company" />
      <el-table-column  label="状态" align="center" key="status">
        <template #default="scope">
          <el-switch
                  v-model="scope.row.status"
                  active-value="0"
                  inactive-value="1"
                  @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="用户昵称" align="center" prop="nickName" />
      <el-table-column label="手机号" align="center" prop="mobile" />
      <el-table-column label="用户邮箱" align="center" prop="email" />
      <el-table-column label="操作" width="300" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:tenant:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:tenant:remove']">删除</el-button>
          <el-button link type="primary" icon="Refresh" @click="handleResetPwd(scope.row)"
            v-hasPermi="['system:tenant:resetPwd']">重置</el-button>
          <el-button link type="primary" icon="User" @click="handleGrantAuth(scope.row)"
            v-hasPermi="['system:tenant:grantAuth']">授权</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改租户管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="租户标志" prop="tenantKey" v-if="form.id==null">
          <el-input v-model="form.tenantKey" placeholder="请输入租户唯一标志（10字符内字母或数字，全局唯一）" />
        </el-form-item>
        <el-form-item label="公司" prop="company">
          <el-input v-model="form.company" placeholder="请输入公司" />
        </el-form-item>
        <el-form-item label="用户姓名" prop="nickName">
          <el-input v-model="form.nickName" placeholder="请输入用户昵称" />
        </el-form-item>
        <el-form-item label="登录账号" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入用户名称" />
        </el-form-item>
        <el-form-item v-if="form.userId == undefined" label="用户密码" prop="password">
          <el-input v-model="form.password" placeholder="请输入密码" type="password" />
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="form.mobile" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入用户邮箱" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改角色配置对话框 -->
    <el-dialog :title="title" v-model="openGrantAuth" width="500px" append-to-body>
      <el-form ref="roleRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="菜单权限">
          <el-checkbox v-model="menuExpand" @change="handleCheckedTreeExpand($event, 'menu')">展开/折叠</el-checkbox>
          <el-checkbox v-model="menuNodeAll" @change="handleCheckedTreeNodeAll($event, 'menu')">全选/全不选</el-checkbox>
          <el-checkbox v-model="form.menuCheckStrictly" @change="handleCheckedTreeConnect($event, 'menu')">父子联动
          </el-checkbox>
          <el-tree class="tree-border" :data="menuOptions" show-checkbox ref="menuRef" node-key="id"
            :check-strictly="!form.menuCheckStrictly" empty-text="加载中，请稍候"
            :props="{ label: 'label', children: 'children' }"></el-tree>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitGrantAuth">确 定</el-button>
          <el-button @click="cancelGrantAuth">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="Tenant">
  import {
    listTenant,
    getTenant,
    delTenant,
    addTenant,
    updateTenant,
    exportTenant,
    resetUserPwd,
    grantAuth,
    changeTenantStatus
  } from "@/api/system/tenant";
  import {
    getToken
  } from "@/utils/auth";
  import {
    treeselect as menuTreeselect,
    roleMenuTreeselect
  } from "@/api/system/menu";
  import {
    getRole
  } from "@/api/system/role";

  const router = useRouter();
  const {
    proxy
  } = getCurrentInstance();

  const loading = ref(true);
  const single = ref(true);
  const multiple = ref(true);
  const showSearch = ref(true);
  const open = ref(false);
  const openGrantAuth = ref(false);
  const menuExpand = ref(false);
  const menuNodeAll = ref(false);
  const ids = ref([]);
  const total = ref(0);
  const tenantList = ref([]);
  const title = ref("");
  const menuOptions = ref([]);
  const menuRef = ref(null);
  const defaultProps = ref({
    children: "children",
    label: "label"
  });
  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      userName: null,
      company: null,
      nickName: null,
      mobile: null,
      email: null,
    },
    rules: {
      tenantKey: [
        { required: true, message: "租户标志不能为空", trigger: "blur" },
        {
          pattern: /^[A-Za-z0-9]+$/,
          message: "请输入正确的租户标志",
          trigger: "blur",
        },
      ],
      userName: [{
        required: true,
        message: "用户账号不能为空",
        trigger: "blur"
      }],
      password: [{
        required: true,
        message: "密码不能为空",
        trigger: "blur"
      }],
      company: [{
        required: true,
        message: "公司不能为空",
        trigger: "blur"
      }],
      nickName: [{
        required: true,
        message: "用户姓名不能为空",
        trigger: "blur"
      }],
      email: [{
          required: true,
          message: "邮箱地址不能为空",
          trigger: "blur"
        },
        {
          type: "email",
          message: "'请输入正确的邮箱地址",
          trigger: ["blur", "change"],
        },
      ],
      mobile: [{
          required: true,
          message: "手机号码不能为空",
          trigger: "blur"
        },
        {
          pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
          message: "请输入正确的手机号码",
          trigger: "blur",
        },
      ],
    },
  });

  const {
    queryParams,
    rules,
    form
  } = toRefs(data);
  /** 查询租户管理列表 */
  function getList() {
    loading.value = true;
    listTenant(queryParams).then(response => {
      tenantList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }
  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }
  // 表单重置
  function reset() {
    form.value = {
      id: null,
      deptId: null,
      userId: null,
      roleId: null,
      userName: null,
      password: null,
      salt: null,
      company: null,
      nickName: null,
      mobile: null,
      email: null,
      delFlag: null,
      createBy: null,
      createTime: null,
      updateBy: null,
      updateTime: null,
      remark: null,
      menuExpand: false,
      menuNodeAll: false,
      // 菜单列表
      menuOptions: [],
      defaultProps: {
        children: "children",
        label: "label"
      },
      menuCheckStrictly: true,
      deptCheckStrictly: true,
    };
    proxy.resetForm("formRef");
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryForm");
    handleQuery();
  }
  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }
  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加租户";
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const id = row.id || ids.value
    getTenant(id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改租户";
    });
  }
  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      "system/tenant/export", {
        ...queryParams.value,
      },
      `tenant_${new Date().getTime()}.xlsx`
    );
  }

  /** 重置密码按钮操作 */
  function handleResetPwd(row) {
    proxy
      .$prompt('请输入"' + row.userName + '"的新密码', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        inputPattern: /^.{5,20}$/,
        inputErrorMessage: "用户密码长度必须介于 5 和 20 之间",
      })
      .then(({
        value
      }) => {
        resetUserPwd(row.id, value).then((response) => {
          proxy.$modal.msgSuccess("修改成功，新密码是：" + value);
        });
      })
      .catch(() => {});
  }

  /** 授权操作 */
  function handleGrantAuth(row) {
    reset();
    const roleId = row.roleId;
    const roleMenu = getRoleMenuTreeselect(roleId);
    getRole(roleId).then(response => {
      form.value = response.data;
      console.log(form.value)
      openGrantAuth.value = true;
      nextTick(() => {
        roleMenu.then(res => {
          nextTick(() => {
            if (menuRef.value) {
              menuRef.value.setCheckedKeys(res.checkedKeys);
            }
          });
          // this.$refs.menu.setCheckedKeys(res.checkedKeys);
        });
      });
      title.value = "授权";
    });

  }
  /** 根据角色ID查询菜单树结构 */
  function getRoleMenuTreeselect(roleId) {
    return roleMenuTreeselect(roleId).then(response => {
      menuOptions.value = response.menus;
      return response;
    });
  }

  /** 所有菜单节点数据 */
  function getMenuAllCheckedKeys() {
    // 目前被选中的菜单节点
    let checkedKeys = menuRef.value.getHalfCheckedKeys();
    // 半选中的菜单节点
    let halfCheckedKeys = menuRef.value.getCheckedKeys();
    checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
    return checkedKeys;
  }

  function submitGrantAuth() {
    proxy.$refs["roleRef"].validate(valid => {
      if (valid) {
        console.log(form.value)
        form.value.menuIds = getMenuAllCheckedKeys();
        grantAuth(form.value).then(response => {
          if (response.code === 200) {
            proxy.$modal.msgSuccess("修改成功");
            openGrantAuth.value = false;
            getList();
          }
        });
      }
    });

  }
  // 取消按钮（授权）
  function cancelGrantAuth() {
    openGrantAuth.value = false;
    reset();
  }
  function handleStatusChange(row){
    let text = row.status === "0" ? "启用" : "停用";
    proxy.$modal.confirm('确认要"' + text + '""' + row.userName + '"用户吗？').then(function() {
      return changeTenantStatus(row.id, row.status);
    }).then(() => {
      proxy.$modal.msgSuccess(text + "成功");
    }).catch(function() {
      row.status = row.status === "0" ? "1" : "0";
    });
  }
  /** 树权限（展开/折叠）*/
  function handleCheckedTreeExpand(value, type) {
    let treeList = menuOptions.value;
    for (let i = 0; i < treeList.length; i++) {
      menuRef.value.store.nodesMap[treeList[i].id].expanded = value;
    }
  }
  /** 树权限（全选/全不选） */
  function handleCheckedTreeNodeAll(value, type) {
    menuRef.value.setCheckedNodes(value ? menuOptions.value : []);
  }
  /** 树权限（父子联动） */
  function handleCheckedTreeConnect(value, type) {
    form.value.menuCheckStrictly = value ? true : false;
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal
      .confirm('是否确认删除租户编号为"' + _ids + '"的数据项？')
      .then(function () {
        return delTenant(_ids);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
      })
      .catch(() => {});
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
      if (valid) {
        if (form.value.id != null) {
          updateTenant(form.value).then((response) => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addTenant(form.value).then((response) => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  getList()
</script>