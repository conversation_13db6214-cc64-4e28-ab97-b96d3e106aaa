:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
  'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* 默认亮色主题变量 */
  --background-color: #F5F5F5;
  --card-background: #FFFFFF;
  --text-primary: #333333;
  --text-secondary: #757575;
  --border-color: #E0E0E0;
  --accent-color: #1976D2;
  --shadow-color: rgba(0, 0, 0, 0.05);
  --hover-shadow: rgba(0, 0, 0, 0.1);
  
  /* 按钮颜色 */
  --primary: #1976D2;
  --primary-hover: #1565C0;
  --secondary: #3F51B5;
  --secondary-hover: #303F9F;
  
  /* 其他交互颜色 */
  --success: #4CAF50;
  --error: #F44336;
  --warning: #FFC107;
  --info: #2196F3;

  color: var(--text-primary);
  background-color: var(--background-color);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  
  transition: color 0.3s ease, background-color 0.3s ease;
}

[data-theme="dark"] {
  --background-color: #121212;
  --card-background: #1E1E1E;
  --text-primary: #FFFFFF;
  --text-secondary: #AAAAAA;
  --border-color: #333333;
  --accent-color: #64B5F6;
  --shadow-color: rgba(0, 0, 0, 0.2);
  --hover-shadow: rgba(0, 0, 0, 0.3);
  
  /* 按钮颜色 - 暗色 */
  --primary: #64B5F6;
  --primary-hover: #42A5F5;
  --secondary: #5C6BC0;
  --secondary-hover: #3F51B5;
  
  /* 其他交互颜色 - 暗色 */
  --success: #66BB6A;
  --error: #EF5350;
  --warning: #FFCA28;
  --info: #42A5F5;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

#app {
  width: 100%;
  height: 100%;
  color: var(--text-primary);
  background-color: var(--background-color);
}

@media (max-width: 480px) {
  html {
    font-size: 14px;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  html {
    font-size: 15px;
  }
}

@media (min-width: 769px) {
  html {
    font-size: 16px;
  }
}

a {
  font-weight: 500;
  color: var(--accent-color);
  text-decoration: inherit;
  transition: color 0.3s ease;
}
a:hover {
  opacity: 0.8;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
  color: var(--text-primary);
  background-color: var(--background-color);
}

h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
  transition: color 0.3s ease;
}

button {
  border-radius: 8px;
  border: 1px solid var(--border-color);
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: var(--card-background);
  color: var(--text-primary);
  cursor: pointer;
  transition: border-color 0.25s, background-color 0.3s, color 0.3s;
}
button:hover {
  border-color: var(--accent-color);
}
button:focus,
button:focus-visible {
  outline: 2px solid var(--accent-color);
}

.card {
  padding: 2em;
  background-color: var(--card-background);
  color: var(--text-primary);
  border-radius: 8px;
  box-shadow: 0 2px 8px var(--shadow-color);
  transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}
