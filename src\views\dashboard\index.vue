<template>
    <div>
      <div class="main-content">
        <!-- 项目标题和信息 -->
        <div class="project-header">
          <div class="project-title">
            <v-icon color="#4285f4" size="24" class="mr-2">mdi-office-building</v-icon>
            <div class="title-with-selector">
              <span>{{ currentProject.name }}</span>
              <v-menu offset-y min-width="200">
                <template v-slot:activator="{ on, attrs }">
                  <v-btn icon small v-bind="attrs" v-on="on" class="project-selector-btn">
                    <v-icon size="18">mdi-chevron-down</v-icon>
                  </v-btn>
                </template>
                <v-list class="project-selector-list">
                  <v-list-item-group v-model="selectedProjectIndex" color="primary">
                    <v-list-item v-for="(project, index) in projectsList" :key="index" @click="selectProject(index)">
                      <v-list-item-content>
                        <v-list-item-title>{{ project.name }}</v-list-item-title>
                        <v-list-item-subtitle class="text-caption">{{ project.code }}</v-list-item-subtitle>
                      </v-list-item-content>
                    </v-list-item>
                  </v-list-item-group>
                </v-list>
              </v-menu>
            </div>
          </div>
          <div class="project-info">
            <span class="info-item">项目编号: {{ currentProject.code }}</span>
            <span class="divider">|</span>
            <span class="info-item">竣工日期: {{ currentProject.completionDate }}</span>
          </div>
          <div class="project-actions">
            <v-btn color="#e3f2fd" class="action-btn mr-2" @click="openExportDialog">
              <v-icon left size="16" color="#4285f4">mdi-file-export</v-icon>
              导出报告
            </v-btn>
            <v-btn color="#e8f5e9" class="action-btn mr-2" @click="openUpdateDialog">
              <v-icon left size="16" color="#4caf50">mdi-refresh</v-icon>
              更新知识库
            </v-btn>
            <v-btn color="#fce4ec" class="action-btn" @click="openThemeDialog">
              <v-icon left size="16" color="#e91e63">mdi-cog</v-icon>
              功能主题
            </v-btn>
          </div>
        </div>
  
        <!-- 更新知识库对话框 -->
        <v-dialog v-model="updateDialog" max-width="480" content-class="update-dialog">
          <v-card class="update-dialog-card">
            <v-card-title class="update-dialog-title">
              <div class="update-title-icon">
  
              </div>
              <span>更新知识库</span>
              <v-icon class="close-icon" @click="updateDialog = false">mdi-close</v-icon>
            </v-card-title>
  
            <v-card-text class="update-dialog-content">
              <div class="update-status">
                <div class="update-progress-label">
                  <span>更新进度</span>
                  <span class="update-percentage">{{ updateProgress }}%</span>
                </div>
                <div class="progress-container">
                  <v-progress-linear v-model="updateProgress" height="8" rounded color="#4caf50"
                    background-color="#e8f5e9" class="update-progress-bar"></v-progress-linear>
                  <div class="progress-overlay" :style="`width: ${updateProgress}%`"></div>
                </div>
              </div>
  
              <div class="update-message" :class="{ 'completed': updateProgress === 100 }">
                {{ updateMessage }}
              </div>
            </v-card-text>
  
            <v-divider></v-divider>
  
            <v-card-actions class="update-dialog-actions">
              <v-spacer></v-spacer>
              <v-btn color="grey-darken-1" variant="text" class="cancel-btn" @click="updateDialog = false"
                :disabled="isUpdating">
                取消
              </v-btn>
              <v-btn color="success" variant="elevated" class="update-btn" @click="startUpdate" :loading="isUpdating"
                :disabled="isUpdating">
                {{ updateProgress === 100 ? '完成' : '开始更新' }}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
  
        <!-- 导出报告对话框 -->
        <v-dialog v-model="exportDialog" max-width="580" content-class="export-dialog">
          <v-card class="export-dialog-card">
            <v-card-title class="export-dialog-title">
              <v-icon color="#4285f4" size="22" class="export-icon">mdi-file-export</v-icon>
              <span>导出报告</span>
              <v-icon class="close-icon" @click="exportDialog = false">mdi-close</v-icon>
            </v-card-title>
  
            <v-card-text class="export-dialog-content">
              <p class="export-desc">请选择导出文件格式：</p>
              <div class="export-options">
                <div class="export-option-card" :class="{ 'selected': exportFormat === 'pdf' }"
                  @click="exportFormat = 'pdf'">
                  <div class="option-content">
                    <div class="format-icon-wrapper pdf">
                      <v-icon color="white" size="22">mdi-file-pdf-box</v-icon>
                    </div>
                    <div class="format-details">
                      <div class="format-name">PDF格式</div>
                      <div class="format-desc">适合打印分享</div>
                    </div>
                  </div>
                  <div class="option-check">
                    <div class="check-circle" v-if="exportFormat === 'pdf'">
                      <v-icon color="white" size="14">mdi-check</v-icon>
                    </div>
                  </div>
                </div>
  
                <div class="export-option-card" :class="{ 'selected': exportFormat === 'excel' }"
                  @click="exportFormat = 'excel'">
                  <div class="option-content">
                    <div class="format-icon-wrapper excel">
                      <v-icon color="white" size="22">mdi-file-excel</v-icon>
                    </div>
                    <div class="format-details">
                      <div class="format-name">Excel格式</div>
                      <div class="format-desc">适合数据分析</div>
                    </div>
                  </div>
                  <div class="option-check">
                    <div class="check-circle" v-if="exportFormat === 'excel'">
                      <v-icon color="white" size="14">mdi-check</v-icon>
                    </div>
                  </div>
                </div>
  
                <div class="export-option-card" :class="{ 'selected': exportFormat === 'word' }"
                  @click="exportFormat = 'word'">
                  <div class="option-content">
                    <div class="format-icon-wrapper word">
                      <v-icon color="white" size="22">mdi-file-word</v-icon>
                    </div>
                    <div class="format-details">
                      <div class="format-name">Word格式</div>
                      <div class="format-desc">适合文档编辑</div>
                    </div>
                  </div>
                  <div class="option-check">
                    <div class="check-circle" v-if="exportFormat === 'word'">
                      <v-icon color="white" size="14">mdi-check</v-icon>
                    </div>
                  </div>
                </div>
              </div>
            </v-card-text>
  
            <v-divider></v-divider>
  
            <v-card-actions class="export-dialog-actions">
              <v-spacer></v-spacer>
              <v-btn color="grey-darken-1" variant="text" class="cancel-btn" @click="exportDialog = false">
                取消
              </v-btn>
              <v-btn color="primary" variant="elevated" class="export-btn" @click="exportReport" :loading="isExporting">
                {{ updateProgress === 100 ? '完成' : '导出' }}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
  
        <!-- 导出进度对话框 -->
        <v-dialog v-model="exportProgressDialog" persistent max-width="360" content-class="progress-dialog">
          <v-card class="progress-dialog-card">
            <v-card-text class="progress-content">
              <div class="progress-animation">
                <v-progress-circular indeterminate color="primary" size="64" width="6"
                  class="progress-indicator"></v-progress-circular>
              </div>
              <div class="progress-text">
                <div class="progress-title">导出中</div>
                <div class="progress-subtitle">正在生成您的报告，请稍候...</div>
              </div>
            </v-card-text>
          </v-card>
        </v-dialog>
  
        <!-- 卡片统计区域 -->
        <v-row class="stat-cards mt-3">
          <!-- 工程文档卡片 -->
          <v-col cols="12" sm="6" md="3">
            <v-card class="stat-card">
              <div class="card-content">
                <div class="card-icon blue">
                  <v-icon>mdi-file-document-outline</v-icon>
                </div>
                <div class="card-info">
                  <div class="card-label-row">
                    <div class="card-label">
                      工程文档
                      <v-icon size="14" color="#9e9e9e" class="info-icon">mdi-information-outline</v-icon>
                    </div>
                    <div class="card-secondary">累计月增量: +125</div>
                  </div>
                  <div class="card-value">1248</div>
                  <div class="card-trend positive">
                    ↑ 12% 较上月增加: +125
                  </div>
                </div>
              </div>
            </v-card>
          </v-col>
  
          <!-- 运维数据卡片 -->
          <v-col cols="12" sm="6" md="3">
            <v-card class="stat-card">
              <div class="card-content">
                <div class="card-icon green">
                  <v-icon>mdi-database</v-icon>
                </div>
                <div class="card-info">
                  <div class="card-label-row">
                    <div class="card-label">
                      运维数据
                      <v-icon size="14" color="#9e9e9e" class="info-icon">mdi-information-outline</v-icon>
                    </div>
                    <div class="card-secondary">本周新增: +256</div>
                  </div>
                  <div class="card-value">5673</div>
                  <div class="card-trend positive">
                    ↑ 8% 本周新增: +256
                  </div>
                </div>
              </div>
            </v-card>
          </v-col>
  
          <!-- 知识安全卡片 -->
          <v-col cols="12" sm="6" md="3">
            <v-card class="stat-card">
              <div class="card-content">
                <div class="card-icon purple">
                  <v-icon>mdi-shield-check</v-icon>
                </div>
                <div class="card-info">
                  <div class="card-label-row">
                    <div class="card-label">
                      知识安全
                      <v-icon size="14" color="#9e9e9e" class="info-icon">mdi-information-outline</v-icon>
                    </div>
                    <div class="card-secondary">本周新增: +156</div>
                  </div>
                  <div class="card-value">3756</div>
                  <div class="card-trend positive">
                    ↑ 15% 本周新增: +156
                  </div>
                </div>
              </div>
            </v-card>
          </v-col>
  
          <!-- 智能问答卡片 -->
          <v-col cols="12" sm="6" md="3">
            <v-card class="stat-card">
              <div class="card-content">
                <div class="card-icon indigo">
                  <v-icon>mdi-message-text</v-icon>
                </div>
                <div class="card-info">
                  <div class="card-label-row">
                    <div class="card-label">
                      智能问答
                      <v-icon size="14" color="#9e9e9e" class="info-icon">mdi-information-outline</v-icon>
                    </div>
                    <div class="card-secondary">解决率: 92%</div>
                  </div>
                  <div class="card-value">2845</div>
                  <div class="card-trend positive">
                    ↑ 25% 解决率: 92%
                  </div>
                </div>
              </div>
            </v-card>
          </v-col>
        </v-row>
  
        <!-- 详细统计模块 -->
        <v-row class="dashboard-modules mt-3">
          <!-- 知识管理健康指数 -->
          <v-col cols="12" md="3">
            <v-card class="module-card health-index-card">
              <v-card-title class="module-title">
                <v-icon color="#F44336" class="mr-2">mdi-heart-pulse</v-icon>
                知识管理健康指数
                <v-icon size="18" color="#9e9e9e" class="info-icon ml-1">mdi-information-outline</v-icon>
              </v-card-title>
              <v-card-text class="module-content">
                <div class="gauge-chart-container">
                  <div id="healthGaugeChart" ref="healthGaugeChart"
                    style="width: 120%; height: 160px; margin: 0 auto 0 -10%; position: relative; display: block;"></div>
                </div>
  
                <div class="health-metrics">
                  <div class="metric-row">
                    <div class="metric">
                      <div class="metric-label">
                        完整性
                        <v-icon size="14" color="#9e9e9e">mdi-information-outline</v-icon>
                      </div>
                      <div class="metric-value-container">
                        <div class="metric-value">92%</div>
                        <div class="metric-dot blue"></div>
                      </div>
                    </div>
                    <div class="metric">
                      <div class="metric-label">
                        初始性
                        <v-icon size="14" color="#9e9e9e">mdi-information-outline</v-icon>
                      </div>
                      <div class="metric-value-container">
                        <div class="metric-value">85%</div>
                        <div class="metric-dot green"></div>
                      </div>
                    </div>
                  </div>
                  <div class="metric-row">
                    <div class="metric">
                      <div class="metric-label">
                        准确性
                        <v-icon size="14" color="#9e9e9e">mdi-information-outline</v-icon>
                      </div>
                      <div class="metric-value-container">
                        <div class="metric-value">78%</div>
                        <div class="metric-dot black"></div>
                      </div>
                    </div>
                    <div class="metric">
                      <div class="metric-label">
                        一致性
                        <v-icon size="14" color="#9e9e9e">mdi-information-outline</v-icon>
                      </div>
                      <div class="metric-value-container">
                        <div class="metric-value">93%</div>
                        <div class="metric-dot purple"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </v-card-text>
            </v-card>
          </v-col>
  
          <!-- 知识处理流水线 -->
          <v-col cols="12" md="5">
            <v-card class="module-card pipeline-card">
              <v-card-title class="module-title">
                <v-icon color="#4CAF50" class="mr-2">mdi-chart-timeline-variant</v-icon>
                知识处理流水线
              </v-card-title>
              <v-card-text class="module-content">
                <div class="pipeline-container">
                  <div class="pipeline-steps-horizontal">
                    <div class="pipeline-step">
                      <div class="step-icon blue">
                        <v-icon color="white" size="20">mdi-database</v-icon>
                      </div>
                      <div class="step-info">
                        <div class="step-name">原始数据</div>
                        <div class="step-value">1,473</div>
                      </div>
                    </div>
                    <div class="pipeline-connector-horizontal"></div>
                    <div class="pipeline-step">
                      <div class="step-icon purple">
                        <v-icon color="white" size="20">mdi-filter-variant</v-icon>
                      </div>
                      <div class="step-info">
                        <div class="step-name">结构化处理</div>
                        <div class="step-value">1,399</div>
                      </div>
                    </div>
                    <div class="pipeline-connector-horizontal"></div>
                    <div class="pipeline-step">
                      <div class="step-icon green">
                        <v-icon color="white" size="20">mdi-account-group</v-icon>
                      </div>
                      <div class="step-info">
                        <div class="step-name">知识审查</div>
                        <div class="step-value">1,329</div>
                      </div>
                    </div>
                    <div class="pipeline-connector-horizontal"></div>
                    <div class="pipeline-step">
                      <div class="step-icon red">
                        <v-icon color="white" size="20">mdi-brain</v-icon>
                      </div>
                      <div class="step-info">
                        <div class="step-name">智能应用</div>
                        <div class="step-value">1,156</div>
                      </div>
                    </div>
                  </div>
  
                  <div class="pipeline-progress">
                    <div class="progress-label-row">
                      <div class="progress-label">知识转化率</div>
                      <div class="progress-value">78%</div>
                    </div>
                    <v-progress-linear height="8" rounded color="#4caf50" background-color="#e8f5e9"
                      :value="78"></v-progress-linear>
                  </div>
                </div>
              </v-card-text>
            </v-card>
          </v-col>
  
          <!-- 查询效率 -->
          <v-col cols="12" md="4">
            <v-card class="module-card query-efficiency-card">
              <v-card-title class="module-title">
                <v-icon color="#2196F3" class="mr-2">mdi-chart-line</v-icon>
                查询效率
              </v-card-title>
              <v-card-text class="module-content">
                <div class="chart-container">
                  <div id="queryChart" ref="queryChart"
                    style="width: 100%; height: 180px; margin: 0 auto; position: relative; display: block;"></div>
                </div>
                <div class="metrics-row">
                  <div class="metric-box">
                    <div class="metric-title">日均访问量</div>
                    <div class="metric-big-value">156</div>
                  </div>
                  <div class="metric-box">
                    <div class="metric-title">平均响应时间</div>
                    <div class="metric-big-value">0.87<span class="unit">s</span></div>
                  </div>
                </div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
  
        <!-- 数据分析模块 -->
        <v-row class="dashboard-modules mt-3">
          <!-- 知识增长趋势 -->
          <v-col cols="12" md="6">
            <v-card class="module-card trend-card">
              <v-card-title class="module-title">
                <v-icon color="#1976D2" class="mr-2">mdi-chart-line-variant</v-icon>
                知识增长趋势
                <div class="growth-rate">较上月: <span class="positive">+16.5%</span></div>
              </v-card-title>
              <v-card-text class="module-content">
                <div class="chart-container growth-chart">
                  <div id="growthChart" ref="growthChart"
                    style="width: 100%; height: 240px; margin: 0 auto; position: relative;"></div>
                  <!-- 自定义图例容器 -->
                  <div id="custom-growth-legend" class="custom-legend-container"></div>
                </div>
              </v-card-text>
            </v-card>
          </v-col>
  
          <!-- 知识来源分布 -->
          <v-col cols="12" md="6">
            <v-card class="module-card distribution-card">
              <v-card-title class="module-title">
                <v-icon color="#673AB7" class="mr-2">mdi-chart-pie</v-icon>
                知识来源分布
              </v-card-title>
              <v-card-text class="module-content">
                <div class="distribution-container">
                  <div class="chart-side">
                    <div id="distributionChart" ref="distributionChart"
                      style="width: 100%; height: 240px; margin: 0 auto; position: relative;"></div>
                  </div>
                  <div class="distribution-stats">
                    <div class="distribution-item">
                      <div class="stat-label">工程文档</div>
                      <div class="stat-progress">
                        <div class="progress-bar engineering" :style="{ width: '42%' }"></div>
                      </div>
                      <div class="stat-value">42%</div>
                    </div>
                    <div class="distribution-item">
                      <div class="stat-label">设备说明书</div>
                      <div class="stat-progress">
                        <div class="progress-bar instruction" :style="{ width: '27%' }"></div>
                      </div>
                      <div class="stat-value">27%</div>
                    </div>
                    <div class="distribution-item">
                      <div class="stat-label">电子版资料</div>
                      <div class="stat-progress">
                        <div class="progress-bar electronic" :style="{ width: '18%' }"></div>
                      </div>
                      <div class="stat-value">18%</div>
                    </div>
                    <div class="distribution-item">
                      <div class="stat-label">标准规范</div>
                      <div class="stat-progress">
                        <div class="progress-bar standard" :style="{ width: '13%' }"></div>
                      </div>
                      <div class="stat-value">13%</div>
                    </div>
                  </div>
                </div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
  
        <!-- 主题选择对话框 -->
        <v-dialog v-model="themeDialog" max-width="400" content-class="theme-dialog">
          <v-card class="theme-dialog-card">
            <v-card-title class="theme-dialog-title">
              <v-icon color="#5c6bc0" class="mr-2">mdi-palette</v-icon>
              选择主题
            </v-card-title>
            <v-card-text>
              <v-row class="theme-options">
                <v-col cols="6">
                  <v-card class="theme-option-card" :class="{ 'selected': selectedTheme === 'light' }"
                    @click="selectTheme('light')" elevation="2">
                    <div class="theme-preview light-theme">
                      <div class="preview-header"></div>
                      <div class="preview-content">
                        <div class="preview-card"></div>
                        <div class="preview-card"></div>
                      </div>
                    </div>
                    <div class="theme-name">
                      <v-icon size="16" color="#4285f4" v-if="selectedTheme === 'light'"
                        class="mr-1">mdi-check-circle</v-icon>
                      亮色模式
                    </div>
                  </v-card>
                </v-col>
                <v-col cols="6">
                  <v-card class="theme-option-card" :class="{ 'selected': selectedTheme === 'dark' }"
                    @click="selectTheme('dark')" elevation="2">
                    <div class="theme-preview dark-theme">
                      <div class="preview-header"></div>
                      <div class="preview-content">
                        <div class="preview-card"></div>
                        <div class="preview-card"></div>
                      </div>
                    </div>
                    <div class="theme-name">
                      <v-icon size="16" color="#4285f4" v-if="selectedTheme === 'dark'"
                        class="mr-1">mdi-check-circle</v-icon>
                      暗色模式
                    </div>
                  </v-card>
                </v-col>
              </v-row>
            </v-card-text>
            <v-card-actions class="theme-dialog-actions">
              <v-spacer></v-spacer>
              <v-btn color="primary" variant="elevated" class="apply-theme-btn" @click="applyTheme">
                应用
              </v-btn>
              <v-btn color="grey-darken-1" variant="text" class="cancel-btn" @click="themeDialog = false">
                取消
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
  
        <!-- 项目系统概览 -->
        <v-row class="dashboard-modules mt-3">
          <v-col cols="12">
            <div style="display: flex;align-items: center;justify-content: space-between;width: 100%;">
              <!-- 项目系统概览标题和按钮 -->
              <div class="system-overview-header mb-2">
                <div class="overview-left">
                  <div class="overview-title">
                    <v-icon color="#4285f4" size="24" class="mr-2">mdi-developer-board</v-icon>
                    项目系统概览
                  </div>
                </div>
              </div>
  
              <!-- 视图切换按钮 - 单独一行 -->
              <div class="view-mode-buttons mb-2">
                <div class="view-toggle-buttons">
                  <button class="view-btn" :class="{ 'active': viewMode === 'list' }" @click="switchToListView">
                    <v-icon size="18" :color="viewMode === 'list' ? '#4285f4' : '#757575'" class="mr-1">mdi-table</v-icon>
                    列表视图
                  </button>
                  <button class="view-btn" :class="{ 'active': viewMode === 'relation' }" @click="switchToRelationView">
                    <v-icon size="18" :color="viewMode === 'relation' ? '#4285f4' : '#757575'"
                      class="mr-1">mdi-relation-many-to-many</v-icon>
                    关系视图
                  </button>
                </div>
              </div>
            </div>
          </v-col>
          <v-col cols="12">
            <!-- 系统卡片内容区域 -->
            <div class="system-cards-container" v-if="viewMode === 'list'">
              <div class="navigation-button left" @click="changePage('prev')" :class="{ 'disabled': currentPage === 0 }">
                <v-icon>mdi-chevron-left</v-icon>
              </div>
  
              <div class="cards-page-container">
                <div class="system-cards-wrapper" v-for="(page, pageIndex) in paginatedCards" :key="pageIndex"
                  v-show="pageIndex === currentPage">
                  <template v-for="(card, index) in page" :key="index">
                    <div class="system-card" :class="card.class" v-if="!card.isEmpty">
                      <div class="system-card-header">
                        <div class="system-icon" :class="card.iconClass">
                          <v-icon color="white" size="24">{{ card.icon }}</v-icon>
                        </div>
                        <div class="system-info">
                          <div class="system-name">{{ card.name }}</div>
                          <div class="system-desc">{{ card.desc }}</div>
                        </div>
                      </div>
                      <div class="system-card-body">
                        <div class="system-metrics">
                          <div class="metrics-row">
                            <div class="system-metric">{{ card.metric1 }}</div>
                            <div class="system-metric">{{ card.metric2 }}</div>
                          </div>
                        </div>
                        <div class="system-status-row">
                          <div class="system-status" :class="card.status">
                            <div class="status-dot"></div>
                            <span>{{ card.statusText }}</span>
                          </div>
                          <a href="#" class="view-more" @click.prevent="openSystemDetail(card)">查看详情</a>
                        </div>
                      </div>
                    </div>
                    <div v-else class="empty-card"></div>
                  </template>
                </div>
              </div>
  
              <div class="navigation-button right" @click="changePage('next')"
                :class="{ 'disabled': currentPage >= paginatedCards.length - 1 }">
                <v-icon>mdi-chevron-right</v-icon>
              </div>
            </div>
  
            <!-- 系统详情对话框 -->
            <v-dialog v-model="systemDetailDialog" max-width="800" content-class="system-detail-dialog">
              <v-card class="system-detail-card">
                <v-card-title class="system-detail-header" :class="currentSystemDetail.iconClass">
                  <div class="detail-header-content">
                    <div class="detail-title-wrapper">
                      <div class="detail-icon-wrapper" :class="currentSystemDetail.iconClass">
                        <v-icon color="white" size="24">{{ currentSystemDetail.icon }}</v-icon>
                      </div>
                      <div class="detail-title-text">
                        <div class="detail-title">{{ currentSystemDetail.name }}</div>
                        <div class="detail-subtitle">{{ currentSystemDetail.desc }}</div>
                      </div>
                    </div>
                    <v-icon class="close-detail-icon" @click="systemDetailDialog = false">mdi-close</v-icon>
                  </div>
                </v-card-title>
  
                <v-card-text class="system-detail-content">
                  <!-- 将运行状态和设备统计改为上下布局 -->
                  <div class="detail-section-container">
                    <!-- 运行状态区域 -->
                    <div class="detail-section full-width">
                      <div class="status-cards">
                        <div class="status-card">
                          <div class="card-title">运行状态</div>
                          <div class="status-warning">
                            <div class="warning-dot"></div>
                            告警中
                          </div>
                        </div>
                        <div class="status-card">
                          <div class="card-title">在线率</div>
                          <div class="status-value">98.5%</div>
                        </div>
                        <div class="status-card">
                          <div class="card-title">故障率</div>
                          <div class="status-value">0.2%</div>
                        </div>
                      </div>
                    </div>
  
                    <!-- 设备统计区域 -->
                    <div class="detail-section full-width">
                      <div class="detail-section-title">
                        <v-icon size="18" color="#4285f4" class="mr-2">mdi-server</v-icon>
                        设备统计
                      </div>
                      <div class="device-cards">
                        <div class="device-card">
                          <div class="device-icon-container">
                            <v-icon size="18" color="#4285f4">mdi-lan</v-icon>
                          </div>
                          <div class="device-info">
                            <div class="device-value">124</div>
                            <div class="device-label">交换机数量</div>
                          </div>
                        </div>
                        <div class="device-card">
                          <div class="device-icon-container">
                            <v-icon size="18" color="#9c27b0">mdi-access-point</v-icon>
                          </div>
                          <div class="device-info">
                            <div class="device-value">87</div>
                            <div class="device-label">AP节点数量</div>
                          </div>
                        </div>
                        <div class="device-card">
                          <div class="device-icon-container">
                            <v-icon size="18" color="#4caf50">mdi-view-grid</v-icon>
                          </div>
                          <div class="device-info">
                            <div class="device-value">48</div>
                            <div class="device-label">配线架</div>
                          </div>
                        </div>
                        <div class="device-card">
                          <div class="device-icon-container">
                            <v-icon size="18" color="#3f51b5">mdi-flash</v-icon>
                          </div>
                          <div class="device-info">
                            <div class="device-value">36</div>
                            <div class="device-label">光纤节点</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
  
                  <!-- 近期告警 -->
                  <div class="detail-section" style="margin-top: 16px;">
                    <div class="detail-section-title">
                      <v-icon size="18" color="#ff9800" class="mr-2">mdi-bell</v-icon>
                      近期告警
                    </div>
                    <div class="alert-table-container">
                      <table class="alert-table">
                        <thead>
                          <tr>
                            <th>告警时间</th>
                            <th>告警类型</th>
                            <th>告警设备</th>
                            <th>处理状态</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td>2025-05-08 14:55</td>
                            <td>网络延迟</td>
                            <td>交换机-C12</td>
                            <td>
                              <div class="alert-status pending">
                                <v-icon color="#ff9800" size="14">mdi-clock-outline</v-icon>
                                处理中
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>2025-05-07 02:02</td>
                            <td>带宽占用</td>
                            <td>AP-B34</td>
                            <td>
                              <div class="alert-status resolved">
                                <v-icon color="#4caf50" size="14">mdi-check-circle</v-icon>
                                已处理
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>2025-05-06 09:24</td>
                            <td>端口错误</td>
                            <td>交换机-E35</td>
                            <td>
                              <div class="alert-status resolved">
                                <v-icon color="#4caf50" size="14">mdi-check-circle</v-icon>
                                已处理
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
  
                  <!-- 维修记录 -->
                  <div class="detail-section">
                    <div class="detail-section-title">
                      <v-icon size="18" color="#4285f4" class="mr-2">mdi-wrench</v-icon>
                      维修记录
                    </div>
                    <div class="maintenance-table-container">
                      <table class="maintenance-table">
                        <thead>
                          <tr>
                            <th>维保时间</th>
                            <th>维保项目</th>
                            <th>维保人员</th>
                            <th>维保结果</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td>2025-05-03</td>
                            <td>系统升级</td>
                            <td><span class="person-tag"><v-icon size="12" color="#4285f4"
                                  class="mr-1">mdi-account</v-icon>张工</span></td>
                            <td>
                              <div class="maintenance-status optimized">
                                <v-icon color="#4caf50" size="14">mdi-check-circle</v-icon>
                                优化完成
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>2025-04-23</td>
                            <td>性能测试</td>
                            <td><span class="person-tag"><v-icon size="12" color="#4285f4"
                                  class="mr-1">mdi-account</v-icon>王工</span></td>
                            <td>
                              <div class="maintenance-status normal">
                                <v-icon color="#4caf50" size="14">mdi-check-circle</v-icon>
                                正常
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>2025-04-08</td>
                            <td>性能测试</td>
                            <td><span class="person-tag"><v-icon size="12" color="#4285f4"
                                  class="mr-1">mdi-account</v-icon>王工</span></td>
                            <td>
                              <div class="maintenance-status optimized">
                                <v-icon color="#4caf50" size="14">mdi-check-circle</v-icon>
                                优化完成
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </v-card-text>
  
                <v-card-actions class="system-detail-actions">
                  <v-spacer></v-spacer>
                  <v-btn color="#e3f2fd" class="action-btn" @click="exportSystemReport">
                    <v-icon left size="16" color="#4285f4">mdi-file-export</v-icon>
                    导出报告
                  </v-btn>
                  <v-btn color="primary" class="close-btn" @click="systemDetailDialog = false">
                    关闭
                  </v-btn>
                </v-card-actions>
              </v-card>
            </v-dialog>
  
            <!-- 关系视图模式 -->
            <div class="relation-view-container" v-if="viewMode === 'relation'">
              <!-- 关系图顶部控制按钮 -->
              <div class="relation-top-controls">
                <!-- 左侧三个按钮 -->
                <div class="left-buttons">
                  <button class="control-btn" :class="{ 'active': relationFilter === 'all' }"
                    @click="setRelationFilter('all')">
                    <v-icon size="16" :color="relationFilter === 'all' ? '#4285f4' : '#757575'"
                      class="mr-1">mdi-apps</v-icon>
                    全部
                  </button>
                  <button class="control-btn" :class="{ 'active': relationFilter === 'data' }"
                    @click="setRelationFilter('data')">
                    <v-icon size="16" :color="relationFilter === 'data' ? '#4285f4' : '#757575'"
                      class="mr-1">mdi-database</v-icon>
                    数据流向
                  </button>
                  <button class="control-btn" :class="{ 'active': relationFilter === 'control' }"
                    @click="setRelationFilter('control')">
                    <v-icon size="16" :color="relationFilter === 'control' ? '#4285f4' : '#757575'"
                      class="mr-1">mdi-tune</v-icon>
                    控制关系
                  </button>
                </div>
  
                <!-- 右侧三个图标 -->
                <div class="right-actions">
                  <span class="action-label">布局:</span>
                  <div class="action-icons">
                    <button class="icon-btn" :class="{ 'active': currentLayout === 'force' }"
                      @click="changeLayout('force')">
                      <v-icon size="20"
                        :color="currentLayout === 'force' ? '#4285f4' : '#757575'">mdi-arrange-send-backward</v-icon>
                    </button>
                    <button class="icon-btn" :class="{ 'active': currentLayout === 'circular' }"
                      @click="changeLayout('circular')">
                      <v-icon size="20" :color="currentLayout === 'circular' ? '#4285f4' : '#757575'">mdi-refresh</v-icon>
                    </button>
                    <button class="icon-btn" :class="{ 'active': currentLayout === 'tree' }"
                      @click="changeLayout('tree')">
                      <v-icon size="20" :color="currentLayout === 'tree' ? '#4285f4' : '#757575'">mdi-view-grid</v-icon>
                    </button>
                  </div>
                </div>
              </div>
  
              <!-- 关系图布局切换按钮 -->
              <div class="relation-layout-controls" v-if="false">
                <div class="layout-buttons">
                  <button class="layout-btn" :class="{ 'active': currentLayout === 'force' }"
                    @click="changeLayout('force')">
                    <v-icon size="16" :color="currentLayout === 'force' ? '#4285f4' : '#757575'"
                      class="mr-1">mdi-graph</v-icon>
                    力导向布局
                  </button>
                  <button class="layout-btn" :class="{ 'active': currentLayout === 'circular' }"
                    @click="changeLayout('circular')">
                    <v-icon size="16" :color="currentLayout === 'circular' ? '#4285f4' : '#757575'"
                      class="mr-1">mdi-chart-bubble</v-icon>
                    环形布局
                  </button>
                  <button class="layout-btn" :class="{ 'active': currentLayout === 'tree' }"
                    @click="changeLayout('tree')">
                    <v-icon size="16" :color="currentLayout === 'tree' ? '#4285f4' : '#757575'"
                      class="mr-1">mdi-file-tree</v-icon>
                    树形布局
                  </button>
                </div>
              </div>
  
              <div class="relation-graph">
                <div id="relationChart" ref="relationChart" style="width: 100%; height: 560px;"></div>
              </div>
            </div>
          </v-col>
        </v-row>
      </div>
    </div>
  </template>
  
  <script setup name="Index">
  import { ref, onMounted, onUnmounted, reactive, nextTick, watch } from 'vue';
  import * as echarts from 'echarts';
  // 引入组件
  
  
  // 项目列表数据
  const projectsList = ref([
    {
      name: '上海金融中心智能化项目',
      code: 'BLDG-2023-0128',
      completionDate: '2023-12-31'
    },
    {
      name: '北京科技园区智慧系统',
      code: 'BLDG-2023-0156', 
      completionDate: '2024-06-30'
    },
    {
      name: '广州商业中心管理平台',
      code: 'BLDG-2023-0187',
      completionDate: '2024-03-15'
    },
    {
      name: '深圳智慧城市示范工程',
      code: 'BLDG-2023-0203',
      completionDate: '2024-09-20'
    }
  ]);
  
  // 当前选中的项目索引
  const selectedProjectIndex = ref(0);
  
  // 当前项目
  const currentProject = ref(projectsList.value[0]);
  
  // 选择项目
  const selectProject = (index) => {
    selectedProjectIndex.value = index;
    currentProject.value = projectsList.value[index];
  };
  
  // 图表实例
  let queryChart = null;
  let healthGaugeChart = null;
  let growthChart = null;
  let distributionChart = null;
  let relationChart = null;
  
  // 健康指数仪表盘初始化
  const initHealthGaugeChart = () => {
    const chartContainer = document.getElementById('healthGaugeChart');
    if (!chartContainer) {
      console.error('找不到健康指数图表容器');
      return;
    }
  
    console.log('初始化健康指数图表...');
  
    // 如果图表已存在，先销毁
    try {
      if (healthGaugeChart && typeof healthGaugeChart.dispose === 'function') {
        healthGaugeChart.dispose();
      }
    } catch (error) {
      console.error('销毁健康指数图表时出错:', error);
    }
  
    try {
      // 初始化图表（自适应容器大小）
      healthGaugeChart = echarts.init(chartContainer);
  
      // 设置图表选项
      const option = {
        series: [
          {
            type: 'gauge',
            startAngle: 180,
            endAngle: 0,
            center: ['60%', '55%'],
            radius: '90%',
            min: 0,
            max: 100,
            animation: true,
            animationDuration: 1000,
            splitNumber: 0,
            pointer: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLine: {
              roundCap: true,
              lineStyle: {
                width: 12,
                color: [
                  [0.87, '#4285f4'],
                  [1, '#e9eef3']
                ]
              }
            },
            title: {
              show: true,
              offsetCenter: [0, '25px'],
              fontSize: 12,
              color: '#777',
              fontWeight: 'normal',
              fontFamily: 'Arial'
            },
            detail: {
              offsetCenter: [0, '-25px'],
              formatter: '{value}',
              valueAnimation: true,
              fontSize: 38,
              fontWeight: 'bold',
              color: '#4285f4',
              fontFamily: 'Arial'
            },
            data: [
              {
                value: 87,
                name: '健康指数',
                title: {
                  show: true,
                  offsetCenter: [0, '25px'],
                  fontSize: 12,
                  color: '#777',
                  fontWeight: 'normal'
                }
              }
            ],
            anchor: {
              show: false
            },
            progress: {
              show: true,
              width: 12,
              roundCap: true,
              itemStyle: {
                color: '#4285f4'
              }
            }
          }
        ]
      };
  
      // 应用配置
      healthGaugeChart.setOption(option);
  
      console.log('健康指数图表已初始化');
    } catch (error) {
      console.error('初始化健康指数图表时出错:', error);
      healthGaugeChart = null;
    }
  };
  
  // 初始化查询效率图表
  const initQueryChart = () => {
    const chartContainer = document.getElementById('queryChart');
    if (!chartContainer) {
      console.error('找不到查询效率图表容器');
      return;
    }
  
    // 如果图表已存在，先销毁
    try {
      if (queryChart && typeof queryChart.dispose === 'function') {
        queryChart.dispose();
      }
    } catch (error) {
      console.error('销毁查询效率图表时出错:', error);
    }
  
    try {
      // 初始化图表（自适应容器大小）
      queryChart = echarts.init(chartContainer);
  
      // 图表配置
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          bottom: '5%',
          data: ['日均访问量', '响应时间(s)'],
          icon: 'circle',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 12
          },
          padding: [0, 0, 0, 0]
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '20%',
          top: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月'],
          axisLine: {
            lineStyle: {
              color: '#E0E0E0'
            }
          },
          axisLabel: {
            fontSize: 12
          },
          axisTick: {
            show: false
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '访问量',
            min: 120,
            max: 180,
            interval: 20,
            position: 'left',
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              fontSize: 12,
              formatter: '{value}'
            },
            splitLine: {
              lineStyle: {
                color: '#F5F5F5'
              }
            }
          },
          {
            type: 'value',
            name: '响应时间',
            min: 0.85,
            max: 1.2,
            interval: 0.1,
            position: 'right',
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              fontSize: 12,
              formatter: '{value} s'
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '日均访问量',
            type: 'line',
            data: [120, 135, 142, 148, 160, 175],
            smooth: true,
            symbol: 'circle',
            symbolSize: 8,
            showSymbol: true,
            itemStyle: {
              color: '#2196F3',
              borderWidth: 2,
              borderColor: '#fff'
            },
            lineStyle: {
              width: 3
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(33, 150, 243, 0.2)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(33, 150, 243, 0.02)'
                  }
                ]
              }
            }
          },
          {
            name: '响应时间(s)',
            type: 'line',
            yAxisIndex: 1,
            data: [1.18, 1.15, 1.03, 0.95, 0.9, 0.85],
            smooth: true,
            symbol: 'circle',
            symbolSize: 8,
            showSymbol: true,
            itemStyle: {
              color: '#4CAF50',
              borderWidth: 2,
              borderColor: '#fff'
            },
            lineStyle: {
              width: 3
            }
          }
        ]
      };
  
      // 应用配置
      queryChart.setOption(option);
      console.log('查询效率图表已初始化');
    } catch (error) {
      console.error('初始化查询效率图表时出错:', error);
      queryChart = null;
    }
  };
  
  // 处理窗口大小变化
  const handleResize = () => {
    try {
      if (healthGaugeChart && typeof healthGaugeChart.resize === 'function') {
        console.log('调整健康指数图表大小');
        healthGaugeChart.resize();
      }
    } catch (error) {
      console.error('调整健康指数图表大小时出错:', error);
    }
  
    try {
      if (queryChart && typeof queryChart.resize === 'function') {
        queryChart.resize();
      }
    } catch (error) {
      console.error('调整查询效率图表大小时出错:', error);
    }
  
    try {
      if (growthChart && typeof growthChart.resize === 'function') {
        growthChart.resize();
      }
    } catch (error) {
      console.error('调整知识增长趋势图表大小时出错:', error);
    }
  
    try {
      if (distributionChart && typeof distributionChart.resize === 'function') {
        distributionChart.resize();
      }
    } catch (error) {
      console.error('调整知识来源分布图表大小时出错:', error);
    }
  
    try {
      if (relationChart && typeof relationChart.resize === 'function') {
        console.log('调整关系图表大小');
        relationChart.resize();
      }
    } catch (error) {
      console.error('调整关系图表大小时出错:', error);
    }
  };
  
  // 组件挂载时初始化图表
  onMounted(() => {
    // 添加窗口大小变化事件监听
    window.addEventListener('resize', handleResize);
  
    // 设置默认关系过滤器和布局
    relationFilter.value = 'all';
    currentLayout.value = 'force';
  
    // 等待DOM完全渲染后初始化图表
    nextTick(() => {
      // 延迟初始化以确保DOM已完全渲染
      setTimeout(() => {
        try {
          const healthContainer = document.getElementById('healthGaugeChart');
          const queryContainer = document.getElementById('queryChart');
          const growthContainer = document.getElementById('growthChart');
          const distributionContainer = document.getElementById('distributionChart');
          const relationContainer = document.getElementById('relationChart');
  
          if (healthContainer && queryContainer) {
            console.log('找到图表容器，开始初始化');
            // 初始化图表
            initHealthGaugeChart();
            initQueryChart();
            initGrowthChart();
            initDistributionChart();
  
            // 确保视图切换时也能初始化关系图
            if (viewMode.value === 'relation' && relationContainer) {
              console.log('初始化关系图...');
              initRelationChart();
            }
  
            // 手动触发一次resize以确保图表正确渲染
            setTimeout(handleResize, 300);
          } else {
            console.error('找不到图表容器，重试初始化');
            // 再多等待一段时间重试
            setTimeout(() => {
              initHealthGaugeChart();
              initQueryChart();
              initGrowthChart();
              initDistributionChart();
  
              // 确保视图切换时也能初始化关系图
              if (viewMode.value === 'relation' && relationContainer) {
                console.log('重试初始化关系图...');
                initRelationChart();
              }
  
              // 强制重新调整大小
              setTimeout(handleResize, 300);
            }, 500);
          }
        } catch (error) {
          console.error('初始化图表时出错:', error);
        }
      }, 200);
    });
  });
  
  // 重置所有图表
  const resetCharts = () => {
    try {
      // 销毁现有图表实例
      if (healthGaugeChart && typeof healthGaugeChart.dispose === 'function') {
        healthGaugeChart.dispose();
        healthGaugeChart = null;
      }
    } catch (error) {
      console.error('重置健康指数图表时出错:', error);
    }
  
    try {
      if (queryChart && typeof queryChart.dispose === 'function') {
        queryChart.dispose();
        queryChart = null;
      }
    } catch (error) {
      console.error('重置查询效率图表时出错:', error);
    }
  
    try {
      if (growthChart && typeof growthChart.dispose === 'function') {
        growthChart.dispose();
        growthChart = null;
      }
    } catch (error) {
      console.error('重置知识增长趋势图表时出错:', error);
    }
  
    try {
      if (distributionChart && typeof distributionChart.dispose === 'function') {
        distributionChart.dispose();
        distributionChart = null;
      }
    } catch (error) {
      console.error('重置知识来源分布图表时出错:', error);
    }
  
    // 重新初始化
    nextTick(() => {
      setTimeout(() => {
        initHealthGaugeChart();
        initQueryChart();
        initGrowthChart();
        initDistributionChart();
      }, 200);
    });
  };
  
  // 组件卸载时销毁图表，移除事件监听
  onUnmounted(() => {
    try {
      // 销毁图表
      if (healthGaugeChart && typeof healthGaugeChart.dispose === 'function') {
        healthGaugeChart.dispose();
        healthGaugeChart = null;
      }
    } catch (error) {
      console.error('卸载健康指数图表时出错:', error);
    }
  
    try {
      if (queryChart && typeof queryChart.dispose === 'function') {
        queryChart.dispose();
        queryChart = null;
      }
    } catch (error) {
      console.error('卸载查询效率图表时出错:', error);
    }
  
    try {
      if (growthChart && typeof growthChart.dispose === 'function') {
        growthChart.dispose();
        growthChart = null;
      }
    } catch (error) {
      console.error('卸载知识增长趋势图表时出错:', error);
    }
  
    try {
      if (distributionChart && typeof distributionChart.dispose === 'function') {
        distributionChart.dispose();
        distributionChart = null;
      }
    } catch (error) {
      console.error('卸载知识来源分布图表时出错:', error);
    }
  
    try {
      if (relationChart && typeof relationChart.dispose === 'function') {
        relationChart.dispose();
        relationChart = null;
      }
    } catch (error) {
      console.error('卸载关系图表时出错:', error);
    }
  
    // 移除事件监听
    window.removeEventListener('resize', handleResize);
  });
  
  // 更新知识库相关
  const updateDialog = ref(false);
  const updateProgress = ref(0);
  const isUpdating = ref(false);
  const updateMessage = ref('准备更新知识库...');
  
  // 打开更新知识库对话框
  const openUpdateDialog = () => {
    updateDialog.value = true;
    updateProgress.value = 0;
    isUpdating.value = false;
    updateMessage.value = '准备更新知识库...';
  };
  
  // 模拟更新过程
  const startUpdate = () => {
    // 如果已完成，点击"完成"按钮时关闭对话框
    if (updateProgress.value === 100) {
      updateDialog.value = false;
      return;
    }
  
    isUpdating.value = true;
    updateProgress.value = 0;
  
    const messages = [
      { progress: 2, message: '正在连接服务器...' },
      { progress: 10, message: '已建立连接，检查更新内容...' },
      { progress: 18, message: '服务器连接成功' },
      { progress: 25, message: '正在扫描知识资源...' },
      { progress: 32, message: '发现56项新增内容' },
      { progress: 38, message: '识别12项内容更新' },
      { progress: 45, message: '正在分析知识结构...' },
      { progress: 55, message: '重建知识关联图谱...' },
      { progress: 62, message: '更新知识层级关系...' },
      { progress: 70, message: '优化知识检索索引...' },
      { progress: 78, message: '同步知识库服务器...' },
      { progress: 85, message: '验证数据完整性...' },
      { progress: 92, message: '正在完成最终更新...' },
      { progress: 98, message: '清理临时缓存...' },
      { progress: 100, message: '知识库更新完成！共同步68个新知识点' }
    ];
  
    let currentIndex = 0;
  
    // 平滑过渡的进度更新
    const smoothUpdate = () => {
      if (currentIndex >= messages.length) return;
  
      const targetProgress = messages[currentIndex].progress;
      const targetMessage = messages[currentIndex].message;
      const currentProgress = updateProgress.value;
  
      // 如果当前进度小于目标进度，则平滑增加
      if (currentProgress < targetProgress) {
        updateProgress.value += 1;
        setTimeout(smoothUpdate, 50);
      } else {
        // 达到目标进度，更新消息并移至下一步
        updateMessage.value = targetMessage;
        currentIndex++;
  
        // 如果还有下一步，则延迟继续
        if (currentIndex < messages.length) {
          setTimeout(smoothUpdate, 800);
        } else {
          // 所有步骤完成
          setTimeout(() => {
            isUpdating.value = false;
  
            // 显示更新成功提示
            if (window.ElMessage) {
              window.ElMessage({
                message: '知识库已成功更新！共同步68个新知识点',
                type: 'success',
                duration: 3000
              });
            }
          }, 1000);
        }
      }
    };
  
    // 开始平滑更新
    setTimeout(smoothUpdate, 500);
  };
  
  // 导出报告相关
  const exportDialog = ref(false);
  const exportFormat = ref('pdf');
  const exportProgressDialog = ref(false);
  const isExporting = ref(false);
  
  // 打开导出对话框
  const openExportDialog = () => {
    exportDialog.value = true;
  };
  
  // 导出报告
  const exportReport = () => {
    // 关闭选择对话框
    exportDialog.value = false;
  
    // 显示导出进度
    isExporting.value = true;
    exportProgressDialog.value = true;
  
    // 模拟导出过程
    setTimeout(() => {
      // 关闭进度对话框
      exportProgressDialog.value = false;
      isExporting.value = false;
  
      // 显示导出成功提示
      const formatNames = {
        pdf: 'PDF',
        excel: 'Excel',
        word: 'Word'
      };
  
      if (window.ElMessage) {
        window.ElMessage({
          message: `报告已成功导出为${formatNames[exportFormat.value]}格式`,
          type: 'success',
          duration: 3000
        });
      }
  
      // 模拟下载文件
      simulateDownload(exportFormat.value);
    }, 2000);
  };
  
  // 模拟下载文件
  const simulateDownload = (format) => {
    const fileExtensions = {
      pdf: 'pdf',
      excel: 'xlsx',
      word: 'docx'
    };
  
    const link = document.createElement('a');
    link.href = `#`;
    link.download = `上海金融中心智能化项目报告.${fileExtensions[format]}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  // 主题相关状态
  const themeDialog = ref(false);
  const currentTheme = ref('light'); // 默认亮色主题
  const selectedTheme = ref('light');
  
  // 打开主题选择对话框
  const openThemeDialog = () => {
    selectedTheme.value = currentTheme.value;
    themeDialog.value = true;
  };
  
  // 选择主题
  const selectTheme = (theme) => {
    selectedTheme.value = theme;
  };
  
  // 应用主题
  const applyTheme = () => {
    currentTheme.value = selectedTheme.value;
    document.documentElement.setAttribute('data-theme', currentTheme.value);
  
    // 设置Vuetify主题
    document.body.classList.remove('theme--light', 'theme--dark');
    document.body.classList.add(`theme--${currentTheme.value}`);
  
    // 设置CSS变量以便全局使用
    if (currentTheme.value === 'dark') {
      document.documentElement.style.setProperty('--background-color', '#121212');
      document.documentElement.style.setProperty('--card-background', '#1E1E1E');
      document.documentElement.style.setProperty('--text-primary', '#FFFFFF');
      document.documentElement.style.setProperty('--text-secondary', '#AAAAAA');
      document.documentElement.style.setProperty('--border-color', '#333333');
    } else {
      document.documentElement.style.setProperty('--background-color', '#F5F5F5');
      document.documentElement.style.setProperty('--card-background', '#FFFFFF');
      document.documentElement.style.setProperty('--text-primary', '#333333');
      document.documentElement.style.setProperty('--text-secondary', '#757575');
      document.documentElement.style.setProperty('--border-color', '#E0E0E0');
    }
  
    // 保存到localStorage
    localStorage.setItem('app-theme', currentTheme.value);
  
    // 关闭对话框
    themeDialog.value = false;
  
    // 显示主题切换成功提示
    showThemeChangeNotification(currentTheme.value);
  };
  
  // 显示主题切换成功的提示
  const showThemeChangeNotification = (theme) => {
    const message = theme === 'dark' ? '已切换到暗色模式' : '已切换到亮色模式';
    // 如果使用的是Element Plus
    if (window.ElMessage) {
      window.ElMessage({
        message,
        type: 'success',
        duration: 2000
      });
    } else {
      // 简单的原生提示
      const notification = document.createElement('div');
      notification.textContent = message;
      notification.style.position = 'fixed';
      notification.style.bottom = '20px';
      notification.style.left = '50%';
      notification.style.transform = 'translateX(-50%)';
      notification.style.padding = '10px 20px';
      notification.style.backgroundColor = theme === 'dark' ? '#424242' : '#E3F2FD';
      notification.style.color = theme === 'dark' ? '#FFFFFF' : '#1976D2';
      notification.style.borderRadius = '4px';
      notification.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
      notification.style.zIndex = '9999';
  
      document.body.appendChild(notification);
  
      setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transition = 'opacity 0.5s ease';
        setTimeout(() => document.body.removeChild(notification), 500);
      }, 2000);
    }
  };
  
  // 初始化时检查保存的主题
  if (typeof window !== 'undefined') {
    const savedTheme = localStorage.getItem('app-theme');
    if (savedTheme) {
      currentTheme.value = savedTheme;
      selectedTheme.value = savedTheme;
      document.documentElement.setAttribute('data-theme', currentTheme.value);
  
      // 初始化主题
      if (currentTheme.value === 'dark') {
        document.documentElement.style.setProperty('--background-color', '#121212');
        document.documentElement.style.setProperty('--card-background', '#1E1E1E');
        document.documentElement.style.setProperty('--text-primary', '#FFFFFF');
        document.documentElement.style.setProperty('--text-secondary', '#AAAAAA');
        document.documentElement.style.setProperty('--border-color', '#333333');
        document.body.classList.add('theme--dark');
      } else {
        document.documentElement.style.setProperty('--background-color', '#F5F5F5');
        document.documentElement.style.setProperty('--card-background', '#FFFFFF');
        document.documentElement.style.setProperty('--text-primary', '#333333');
        document.documentElement.style.setProperty('--text-secondary', '#757575');
        document.documentElement.style.setProperty('--border-color', '#E0E0E0');
        document.body.classList.add('theme--light');
      }
    }
  }
  
  // 图表配置
  const chartOptions = {
    title: {
      text: '近30天访问量趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1日', '5日', '10日', '15日', '20日', '25日', '30日']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '访问量',
        type: 'line',
        smooth: true,
        data: [120, 132, 101, 134, 90, 230, 210],
        areaStyle: {
          opacity: 0.3
        },
        itemStyle: {
          color: '#5246e6'
        }
      },
      {
        name: '新增知识',
        type: 'bar',
        data: [20, 32, 21, 34, 25, 30, 10],
        itemStyle: {
          color: '#42A5F5'
        }
      }
    ]
  };
  
  // 饼图配置
  const pieChartOptions = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '知识分类',
        type: 'pie',
        radius: '70%',
        data: [
          { value: 1048, name: '结构设计' },
          { value: 735, name: '建筑设计' },
          { value: 580, name: '给排水' },
          { value: 484, name: '暖通空调' },
          { value: 300, name: '电气工程' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
  
  // 表格数据
  const tableData = [
    {
      title: '建筑结构抗震设计规范解析',
      category: '结构设计',
      author: '张工',
      date: '2023-05-04'
    },
    {
      title: '绿色建筑评价标准详解',
      category: '建筑设计',
      author: '李工',
      date: '2023-05-03'
    },
    {
      title: '建筑电气设计常见问题分析',
      category: '电气工程',
      author: '王工',
      date: '2023-05-02'
    },
    {
      title: '智能建筑控制系统设计指南',
      category: '智能建筑',
      author: '赵工',
      date: '2023-05-01'
    }
  ];
  
  // 初始化知识增长趋势图表
  const initGrowthChart = () => {
    const chartContainer = document.getElementById('growthChart');
    if (!chartContainer) {
      console.error('找不到知识增长趋势图表容器');
      return;
    }
  
    // 如果图表已存在，先销毁
    try {
      if (growthChart && typeof growthChart.dispose === 'function') {
        growthChart.dispose();
      }
    } catch (error) {
      console.error('销毁知识增长趋势图表时出错:', error);
    }
  
    try {
      // 初始化图表
      growthChart = echarts.init(chartContainer);
  
      // 图表配置
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          show: true,
          top: 'auto',
          bottom: 0,
          itemWidth: 12,
          itemHeight: 12,
          icon: 'circle',
          textStyle: {
            fontSize: 13,
            color: '#666'
          },
          data: ['文档', '知识节点', '知识关系']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: 50,
          top: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月'],
          axisLine: {
            lineStyle: {
              color: '#E0E0E0'
            }
          },
          axisLabel: {
            fontSize: 12
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          min: 0,
          splitNumber: 4,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              color: '#F5F5F5'
            }
          }
        },
        series: [
          {
            name: '文档',
            type: 'line',
            data: [1000, 1050, 1100, 1150, 1200, 1250],
            smooth: true,
            symbol: 'circle',
            symbolSize: 8,
            showSymbol: true,
            itemStyle: {
              color: '#1976D2',
              borderWidth: 2,
              borderColor: '#fff'
            },
            lineStyle: {
              width: 3
            }
          },
          {
            name: '知识节点',
            type: 'line',
            data: [3000, 3200, 3400, 3600, 3700, 3800],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            showSymbol: true,
            itemStyle: {
              color: '#00BFA5',
              borderWidth: 2,
              borderColor: '#fff'
            },
            lineStyle: {
              width: 3
            }
          },
          {
            name: '知识关系',
            type: 'line',
            data: [4200, 4500, 4700, 5000, 5300, 5700],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            showSymbol: true,
            itemStyle: {
              color: '#FFA000',
              borderWidth: 2,
              borderColor: '#fff'
            },
            lineStyle: {
              width: 3
            }
          }
        ]
      };
  
      // 应用配置
      growthChart.setOption(option);
      console.log('知识增长趋势图表已初始化');
    } catch (error) {
      console.error('初始化知识增长趋势图表时出错:', error);
      growthChart = null;
    }
  };
  
  // 初始化知识来源分布图表
  const initDistributionChart = () => {
    const chartContainer = document.getElementById('distributionChart');
    if (!chartContainer) {
      console.error('找不到知识来源分布图表容器');
      return;
    }
  
    // 如果图表已存在，先销毁
    try {
      if (distributionChart && typeof distributionChart.dispose === 'function') {
        distributionChart.dispose();
      }
    } catch (error) {
      console.error('销毁知识来源分布图表时出错:', error);
    }
  
    try {
      // 初始化图表
      distributionChart = echarts.init(chartContainer);
  
      // 图表配置
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          show: true,
          bottom: 5,
          itemWidth: 12,
          itemHeight: 12,
          icon: 'circle',
          textStyle: {
            fontSize: 13,
            color: '#666'
          },
          data: ['工程文档', '设备说明书', '电子版资料', '标准规范']
        },
        color: ['#2196F3', '#4CAF50', '#FFC107', '#F44336'],
        series: [
          {
            name: '知识来源分布',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '45%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 4,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: false
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 42, name: '工程文档' },
              { value: 27, name: '设备说明书' },
              { value: 18, name: '电子版资料' },
              { value: 13, name: '标准规范' }
            ]
          }
        ]
      };
  
      // 应用配置
      distributionChart.setOption(option);
      console.log('知识来源分布图表已初始化');
    } catch (error) {
      console.error('初始化知识来源分布图表时出错:', error);
      distributionChart = null;
    }
  };
  
  // 初始化ECharts关系图
  const initRelationChart = () => {
    const chartContainer = document.getElementById('relationChart');
    if (!chartContainer) {
      console.error('找不到关系图表容器');
      return;
    }
  
    console.log('正在初始化关系图...');
  
    // 如果图表已存在，先销毁
    try {
      if (relationChart && typeof relationChart.dispose === 'function') {
        relationChart.dispose();
      }
    } catch (error) {
      console.error('销毁关系图表时出错:', error);
    }
  
    try {
      // 初始化图表
      relationChart = echarts.init(chartContainer);
  
      // 选择适当的布局
      let layout = currentLayout.value;
      let filter = relationFilter.value;
  
      // 根据过滤类型设置连接
      let linkData = [];
  
      if (filter === 'all' || filter === 'data') {
        // 数据流向连接
        linkData.push(
          { source: 0, target: 1, lineStyle: { normal: { color: '#4e81bd' } } },
          { source: 2, target: 1, lineStyle: { normal: { color: '#5baa5f' } } },
          { source: 3, target: 4, lineStyle: { normal: { color: '#a07662' } } }
        );
      }
  
      if (filter === 'all' || filter === 'control') {
        // 控制关系连接
        linkData.push(
          { source: 0, target: 2, lineStyle: { normal: { color: '#4e81bd' } } },
          { source: 0, target: 4, lineStyle: { normal: { color: '#4e81bd' } } },
          { source: 1, target: 4, lineStyle: { normal: { color: '#e36159' } } },
          { source: 1, target: 5, lineStyle: { normal: { color: '#e36159' } } },
          { source: 2, target: 4, lineStyle: { normal: { color: '#5baa5f' } } }
        );
      }
  
      let seriesConfig = {
        type: 'graph',
        layout: currentLayout.value,
        symbolSize: 60,
        focusNodeAdjacency: true,
        roam: true,
        edgeSymbol: ['none', 'none'],
        categories: [
          {
            name: '楼宇系统 (315)',
            itemStyle: {
              normal: {
                color: "#4e81bd",
              }
            }
          },
          {
            name: '安防系统 (642)',
            itemStyle: {
              normal: {
                color: "#e36159",
              }
            }
          },
          {
            name: '网络系统 (124)',
            itemStyle: {
              normal: {
                color: "#5baa5f",
              }
            }
          },
          {
            name: '音视频 (275)',
            itemStyle: {
              normal: {
                color: "#a07662",
              }
            }
          },
          {
            name: '消防系统 (386)',
            itemStyle: {
              normal: {
                color: "#f79646",
              }
            }
          },
          {
            name: '电梯系统 (48)',
            itemStyle: {
              normal: {
                color: "#7fccbf",
              }
            }
          }
        ],
        label: {
          normal: {
            show: true,
            textStyle: {
              fontSize: 14
            },
            position: 'right'
          }
        },
        edgeLabel: {
          normal: {
            show: false
          }
        },
        data: [
          {
            name: '楼宇系统',
            category: 0,
            draggable: true,
            symbolSize: 70
          },
          {
            name: '安防系统',
            category: 1,
            draggable: true,
            symbolSize: 80
          },
          {
            name: '网络系统',
            category: 2,
            draggable: true,
            symbolSize: 50
          },
          {
            name: '音视频',
            category: 3,
            draggable: true,
            symbolSize: 65
          },
          {
            name: '消防系统',
            category: 4,
            draggable: true,
            symbolSize: 70
          },
          {
            name: '电梯系统',
            category: 5,
            draggable: true,
            symbolSize: 45
          }
        ],
        links: linkData,
        lineStyle: {
          normal: {
            opacity: 0.9,
            width: 2,
            curveness: 0.2
          }
        }
      };
  
      // 根据不同布局类型设置特定配置
      if (layout === 'force') {
        seriesConfig.force = {
          repulsion: 1200,
          gravity: 0.2,
          edgeLength: 120,
          layoutAnimation: true
        };
      } else if (layout === 'circular') {
        seriesConfig.circular = {
          rotateLabel: true
        };
      } else if (layout === 'tree') {
        // 树形布局以楼宇系统为根节点
        seriesConfig.layout = 'force';
        seriesConfig.force = {
          repulsion: 800,
          gravity: 0.1,
          edgeLength: 100,
          layoutAnimation: true,
          initLayout: 'circular'
        };
  
        // 树形布局需要特殊的连接方式
        if (filter === 'all') {
          seriesConfig.links = [
            { source: 0, target: 1, lineStyle: { normal: { color: '#4e81bd' } } },
            { source: 0, target: 2, lineStyle: { normal: { color: '#4e81bd' } } },
            { source: 0, target: 4, lineStyle: { normal: { color: '#4e81bd' } } },
            { source: 0, target: 3, lineStyle: { normal: { color: '#4e81bd' } } },
            { source: 0, target: 5, lineStyle: { normal: { color: '#4e81bd' } } }
          ];
        }
      }
  
      // 图表配置
      const option = {
        title: {
          text: '项目系统关系图',
          left: 'center',
          top: 20,
          textStyle: {
            fontSize: 18,
            fontWeight: 'normal',
            color: '#333'
          }
        },
        tooltip: {},
        animationDurationUpdate: 1500,
        animationEasingUpdate: 'quinticInOut',
        legend: {
          x: "center",
          y: "top",
          top: 50,
          itemGap: 20,
          itemWidth: 15,
          itemHeight: 15,
          textStyle: {
            fontSize: 13
          },
          show: true,
          data: [
            { name: "楼宇系统 (315)", icon: 'circle' },
            { name: "安防系统 (642)", icon: 'circle' },
            { name: "网络系统 (124)", icon: 'circle' },
            { name: "音视频 (275)", icon: 'circle' },
            { name: "消防系统 (386)", icon: 'circle' },
            { name: "电梯系统 (48)", icon: 'circle' }
          ]
        },
        series: [seriesConfig]
      };
  
      // 应用配置
      relationChart.setOption(option);
      console.log('关系图表已初始化，使用' + layout + '布局');
  
      // 强制重绘以确保显示
      setTimeout(() => {
        if (relationChart) {
          relationChart.resize();
        }
      }, 200);
    } catch (error) {
      console.error('初始化关系图表时出错:', error);
      relationChart = null;
    }
  };
  
  // 关系视图模式
  const viewMode = ref('list');
  // 关系图布局类型
  const currentLayout = ref('force');
  // 关系图过滤类型
  const relationFilter = ref('all');
  
  // 切换到列表视图
  const switchToListView = () => {
    viewMode.value = 'list';
  };
  
  // 切换到关系视图
  const switchToRelationView = () => {
    viewMode.value = 'relation';
    // 切换到关系视图后，确保图表初始化
    nextTick(() => {
      setTimeout(() => {
        initRelationChart();
      }, 100);
    });
  };
  
  // 切换布局
  const changeLayout = (layout) => {
    currentLayout.value = layout;
    // 当布局改变时重新初始化图表
    if (viewMode.value === 'relation') {
      nextTick(() => {
        setTimeout(() => {
          initRelationChart();
        }, 100);
      });
    }
  };
  
  // 设置关系过滤器
  const setRelationFilter = (filter) => {
    relationFilter.value = filter;
    // 重新初始化图表以应用过滤器
    if (viewMode.value === 'relation') {
      nextTick(() => {
        setTimeout(() => {
          initRelationChart();
        }, 100);
      });
    }
  };
  
  // 卡片容器引用
  const cardsContainer = ref(null);
  
  // 滚动卡片
  const scrollCards = (direction) => {
    if (cardsContainer.value) {
      const scrollAmount = 300; // 每次滚动的距离
      const currentScroll = cardsContainer.value.scrollLeft;
      const newScroll = direction === 'right'
        ? currentScroll + scrollAmount
        : currentScroll - scrollAmount;
  
      cardsContainer.value.scrollTo({
        left: newScroll,
        behavior: 'smooth'
      });
    }
  };
  
  // 分页相关
  const paginatedCards = ref([]);
  const currentPage = ref(0);
  
  // 改变页面
  const changePage = (action) => {
    if (action === 'prev' && currentPage.value > 0) {
      currentPage.value--;
    } else if (action === 'next' && currentPage.value < paginatedCards.value.length - 1) {
      currentPage.value++;
    }
  };
  
  // 系统卡片数据
  const systemCards = [
    {
      class: 'building-system',
      iconClass: 'building',
      icon: 'mdi-home-automation',
      name: '楼宇自动化系统',
      desc: '监控管理楼宇空调、给排水、照明控制等',
      metric1: '控制器数量: 315',
      metric2: '端口数量: 12,573',
      status: 'normal',
      statusText: '正常'
    },
    {
      class: 'security-system',
      iconClass: 'security',
      icon: 'mdi-cctv',
      name: '安防监控系统',
      desc: '视频监控、人员管理、门禁管理等',
      metric1: '摄像头数量: 642',
      metric2: '门禁设备: 212',
      status: 'normal',
      statusText: '正常'
    },
    {
      class: 'network-system',
      iconClass: 'network',
      icon: 'mdi-server-network',
      name: '综合布线系统',
      desc: '电话、数据、视频等业务信息传输网络',
      metric1: '交换机数量: 124',
      metric2: 'AP节点数量: 87',
      status: 'warning',
      statusText: '告警中'
    },
    {
      class: 'audio-system',
      iconClass: 'audio',
      icon: 'mdi-volume-high',
      name: '音视频系统',
      desc: '会议室设备、信息发布、背景音乐等',
      metric1: '会议室设备: 46',
      metric2: '播放终端数: 275',
      status: 'normal',
      statusText: '正常'
    },
    {
      class: 'fire-system',
      iconClass: 'fire',
      icon: 'mdi-fire',
      name: '消防系统',
      desc: '火灾报警、消防联动、应急疏散等',
      metric1: '探测器数量: 1,245',
      metric2: '消防设备: 386',
      status: 'normal',
      statusText: '正常'
    },
    {
      class: 'elevator-system',
      iconClass: 'elevator',
      icon: 'mdi-elevator',
      name: '电梯系统',
      desc: '电梯监控、运行管理、维保调度等',
      metric1: '电梯数量: 48',
      metric2: '监控点位: 192',
      status: 'normal',
      statusText: '正常'
    }
  ];
  
  // 分页处理
  const paginateCards = () => {
    const rowsPerPage = 2; // 每页2行
    const cardsPerRow = 5; // 每行5个卡片
    const pageSize = rowsPerPage * cardsPerRow; // 每页10个卡片
  
    paginatedCards.value = [];
  
    // 模拟添加更多卡片使得总数达到两页
    const allCards = [...systemCards];
  
    // 复制一些卡片使总数达到更多
    const additionalCards = systemCards.map(card => ({ ...card }));
    const moreCards = systemCards.map(card => ({ ...card }));
    allCards.push(...additionalCards, ...moreCards);
  
    // 分页处理
    for (let i = 0; i < allCards.length; i += pageSize) {
      const pageCards = allCards.slice(i, i + pageSize);
  
      // 如果这一页卡片不足10个，添加空白卡片补齐
      if (pageCards.length < pageSize) {
        const emptyCount = pageSize - pageCards.length;
        for (let j = 0; j < emptyCount; j++) {
          pageCards.push({
            class: 'empty-system',
            iconClass: '',
            icon: '',
            name: '',
            desc: '',
            metric1: '',
            metric2: '',
            status: '',
            statusText: '',
            isEmpty: true
          });
        }
      }
  
      paginatedCards.value.push(pageCards);
    }
  };
  
  // 组件挂载时初始化分页
  onMounted(() => {
    paginateCards();
  });
  
  // 监听视图模式变化
  watch(() => viewMode.value, (newMode) => {
    if (newMode === 'relation') {
      console.log('视图切换到关系图模式');
      // 延迟执行以确保DOM已更新
      nextTick(() => {
        setTimeout(() => {
          initRelationChart();
        }, 100);
      });
    }
  });
  
  // 监听布局模式变化
  watch(() => currentLayout.value, (newLayout) => {
    console.log('关系图布局切换为:', newLayout);
    if (viewMode.value === 'relation') {
      // 仅在关系图视图模式下更新
      nextTick(() => {
        setTimeout(() => {
          initRelationChart();
        }, 100);
      });
    }
  });
  
  // 系统详情对话框
  const systemDetailDialog = ref(false);
  // 当前系统详情
  const currentSystemDetail = ref({});
  
  // 打开系统详情对话框
  const openSystemDetail = (card) => {
    currentSystemDetail.value = card;
    systemDetailDialog.value = true;
  };
  
  // 导出系统报告
  const exportSystemReport = () => {
    // 关闭系统详情对话框
    systemDetailDialog.value = false;
  
    // 显示导出进度
    isExporting.value = true;
    exportProgressDialog.value = true;
  
    // 模拟导出过程
    setTimeout(() => {
      // 关闭进度对话框
      exportProgressDialog.value = false;
      isExporting.value = false;
  
      // 显示导出成功提示
      const formatNames = {
        pdf: 'PDF',
        excel: 'Excel',
        word: 'Word'
      };
  
      if (window.ElMessage) {
        window.ElMessage({
          message: `系统报告已成功导出为${formatNames[exportFormat.value]}格式`,
          type: 'success',
          duration: 3000
        });
      }
  
      // 模拟下载文件
      simulateDownload(exportFormat.value);
    }, 2000);
  };
  </script>
  
  <style scoped>
  .main-content {
    min-height: calc(100vh - 150px);
    overflow-y: auto;
    padding: 15px;
  }
  
  .project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    flex-wrap: wrap;
  }
  
  .project-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
  }
  
  .title-with-selector {
    display: flex;
    align-items: center;
    position: relative;
  }
  
  .project-selector-btn {
    margin-left: 4px;
    opacity: 0.6;
    transition: all 0.2s ease;
  }
  
  .project-title:hover .project-selector-btn {
    opacity: 1;
    transform: translateY(-1px);
  }
  
  .project-selector-list {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
  }
  
  .project-title:hover {
    color: #1976d2;
    transform: translateX(5px);
  }
  
  .project-title:hover .v-icon {
    transform: rotate(10deg);
  }
  
  .project-info {
    color: #757575;
    font-size: 14px;
  }
  
  .info-item {
    margin: 0 4px;
  }
  
  .divider {
    margin: 0 8px;
    color: #ccc;
  }
  
  .project-actions {
    display: flex;
    margin-top: 10px;
  }
  
  .action-btn {
    text-transform: none !important;
    font-weight: 400 !important;
    letter-spacing: normal !important;
    font-size: 13px !important;
    border-radius: 4px !important;
    height: 36px !important;
    transition: all 0.3s ease !important;
    position: relative;
    overflow: hidden;
  }
  
  .action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1) !important;
  }
  
  .action-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.4s ease, height 0.4s ease;
  }
  
  .action-btn:hover::before {
    width: 150%;
    height: 150%;
  }
  
  .stat-cards {
    margin-left: -8px;
    margin-right: -8px;
  }
  
  .stat-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
    height: 100%;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }
  
  .stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1) !important;
  }
  
  .stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
    transition: height 0.3s ease;
  }
  
  .stat-card:hover::after {
    height: 100%;
  }
  
  .card-content {
    display: flex;
    padding: 16px;
  }
  
  .card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 8px;
    margin-right: 16px;
    flex-shrink: 0;
    transition: all 0.3s ease;
  }
  
  .stat-card:hover .card-icon {
    transform: scale(1.1);
  }
  
  .card-icon.blue {
    background-color: #e3f2fd;
    color: #1976d2;
  }
  
  .card-icon.green {
    background-color: #e8f5e9;
    color: #4caf50;
  }
  
  .card-icon.purple {
    background-color: #f3e5f5;
    color: #9c27b0;
  }
  
  .card-icon.indigo {
    background-color: #e8eaf6;
    color: #3f51b5;
  }
  
  .card-info {
    flex: 1;
  }
  
  .card-label-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    width: 100%;
  }
  
  .card-label {
    font-size: 14px;
    color: #757575;
    display: flex;
    align-items: center;
  }
  
  .card-secondary {
    font-size: 12px;
    color: #9e9e9e;
    white-space: nowrap;
    padding-right: 2px;
  }
  
  .card-value {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 6px;
    transition: all 0.3s ease;
  }
  
  .stat-card:hover .card-value {
    transform: scale(1.05);
    color: #1976d2;
  }
  
  .card-trend {
    font-size: 12px;
    padding: 2px 0;
    transition: all 0.3s ease;
    position: relative;
    display: inline-block;
  }
  
  .card-trend.positive {
    color: #4caf50;
  }
  
  .card-trend.negative {
    color: #f44336;
  }
  
  .card-trend.positive:hover {
    transform: translateX(5px);
  }
  
  .card-trend.negative:hover {
    transform: translateX(5px);
  }
  
  .v-icon {
    transition: all 0.3s ease;
  }
  
  .card-label .info-icon {
    margin-left: 4px;
  }
  
  @media (max-width: 960px) {
    .project-header {
      flex-direction: column;
      align-items: flex-start;
    }
  
    .project-title,
    .project-info,
    .project-actions {
      margin-bottom: 8px;
    }
  }
  
  @media (max-width: 1200px) {
    .card-label-row {
      flex-direction: column;
      align-items: flex-start;
    }
  
    .card-secondary {
      margin-top: 4px;
      margin-left: 0;
    }
  }
  
  /* 主题对话框样式 */
  .theme-dialog-title {
    font-size: 18px;
    font-weight: 500;
    padding: 16px 24px;
    color: var(--text-primary);
  }
  
  .theme-options {
    padding: 0 8px;
  }
  
  .theme-option-card {
    cursor: pointer;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
  }
  
  .theme-option-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08) !important;
  }
  
  .theme-option-card.selected {
    border-color: #4285f4;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2) !important;
  }
  
  .theme-option-card.selected::after {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #4285f4;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    z-index: 2;
  }
  
  .theme-preview {
    height: 120px;
    padding: 8px;
  }
  
  .theme-preview.light-theme {
    background-color: #f5f5f5;
  }
  
  .theme-preview.dark-theme {
    background-color: #263238;
  }
  
  .preview-header {
    height: 20px;
    margin-bottom: 10px;
    background-color: #fff;
    border-radius: 4px;
  }
  
  .dark-theme .preview-header {
    background-color: #37474f;
  }
  
  .preview-content {
    display: flex;
    height: calc(100% - 30px);
    gap: 6px;
  }
  
  .preview-card {
    flex: 1;
    background-color: #fff;
    border-radius: 4px;
  }
  
  .dark-theme .preview-card {
    background-color: #37474f;
  }
  
  .theme-name {
    text-align: center;
    padding: 10px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    color: var(--text-primary);
  }
  
  .theme-dialog-card {
    background-color: var(--card-background);
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow-color);
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
  }
  
  .theme-dialog-actions {
    padding: 16px;
  }
  
  .apply-theme-btn {
    background-color: var(--accent-color) !important;
    color: white !important;
    transition: all 0.3s ease;
    font-weight: 500;
  }
  
  .apply-theme-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 8px var(--shadow-color) !important;
  }
  
  .cancel-btn {
    color: var(--text-secondary) !important;
    transition: all 0.3s ease;
  }
  
  .cancel-btn:hover {
    color: var(--text-primary) !important;
    transform: translateY(-2px);
  }
  
  /* 导出对话框样式 */
  .export-dialog-card {
    background-color: var(--card-background);
    border-radius: 16px;
    overflow: hidden;
    transition: background-color 0.3s ease;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;
  }
  
  .export-dialog-title {
    font-size: 18px;
    font-weight: 600;
    padding: 16px 24px;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    position: relative;
  }
  
  .export-icon {
    margin-right: 12px;
  }
  
  .close-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    opacity: 0.6;
    transition: all 0.2s ease;
  }
  
  .close-icon:hover {
    opacity: 1;
    transform: translateY(-50%) rotate(90deg);
  }
  
  .export-dialog-content {
    padding: 0 24px 16px;
    overflow: visible;
  }
  
  .export-desc {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 16px;
  }
  
  .export-options {
    display: flex;
    flex-direction: row;
    gap: 12px;
    flex-wrap: wrap;
    margin: 0 -4px;
  }
  
  .export-option-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: var(--background-color);
    border: 2px solid transparent;
    position: relative;
    margin: 0 4px;
    flex: 1;
    width: calc(33.33% - 8px);
    min-width: 0;
    overflow: hidden;
    max-width: 180px;
  }
  
  @media (max-width: 520px) {
    .export-option-card {
      width: 100%;
      max-width: none;
      margin-bottom: 8px;
    }
  
    .export-options {
      flex-direction: column;
    }
  }
  
  .export-option-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
  
  .export-option-card.selected {
    border-color: var(--accent-color);
    background-color: rgba(25, 118, 210, 0.04);
  }
  
  .option-content {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
    overflow: hidden;
  }
  
  .format-icon-wrapper {
    width: 32px;
    height: 32px;
    min-width: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    transition: all 0.2s ease;
    flex-shrink: 0;
  }
  
  .format-icon-wrapper.pdf {
    background-color: #f44336;
  }
  
  .format-icon-wrapper.excel {
    background-color: #4caf50;
  }
  
  .format-icon-wrapper.word {
    background-color: #2196f3;
  }
  
  .format-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
    min-width: 0;
    max-width: calc(100% - 50px);
    overflow: hidden;
  }
  
  .format-name {
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .format-desc {
    font-size: 12px;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .option-check {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
    flex-shrink: 0;
  }
  
  .check-circle {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: scaleIn 0.2s ease;
  }
  
  @keyframes scaleIn {
    0% {
      transform: scale(0);
    }
  
    70% {
      transform: scale(1.1);
    }
  
    100% {
      transform: scale(1);
    }
  }
  
  .export-dialog-actions {
    padding: 12px 24px;
    display: flex;
    justify-content: flex-end;
    background-color: var(--card-background);
  }
  
  .export-btn {
    min-width: 90px;
    background-color: var(--accent-color) !important;
    color: white !important;
    margin-left: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 8px !important;
    height: 40px !important;
    letter-spacing: 0.5px;
  }
  
  .export-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3) !important;
  }
  
  .cancel-btn {
    min-width: 90px;
    color: var(--text-secondary) !important;
    transition: all 0.3s ease;
    border-radius: 8px !important;
  }
  
  /* 进度对话框样式 */
  .progress-dialog-card {
    background-color: var(--card-background);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15) !important;
    padding: 0;
  }
  
  .progress-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 36px 24px;
    text-align: center;
  }
  
  .progress-animation {
    margin-bottom: 28px;
    position: relative;
  }
  
  .progress-animation::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 84px;
    height: 84px;
    border-radius: 50%;
    background-color: rgba(25, 118, 210, 0.08);
    z-index: -1;
  }
  
  .progress-indicator {
    z-index: 1;
  }
  
  .progress-text {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  
  .progress-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
  }
  
  .progress-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    max-width: 260px;
    margin: 0 auto;
  }
  
  /* 更新知识库对话框样式 */
  .update-dialog-card {
    background-color: var(--card-background);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15) !important;
    padding: 0;
  }
  
  .update-dialog-title {
    font-size: 18px;
    font-weight: 600;
    padding: 16px 24px;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    position: relative;
  }
  
  .update-title-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background-color: #4caf50;
    margin-right: 12px;
    box-shadow: 0 3px 6px rgba(76, 175, 80, 0.2);
  }
  
  .close-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    opacity: 0.6;
    transition: all 0.2s ease;
  }
  
  .close-icon:hover {
    opacity: 1;
    transform: translateY(-50%) rotate(90deg);
  }
  
  .update-dialog-content {
    padding: 8px 24px 20px;
  }
  
  .update-status {
    margin-bottom: 20px;
  }
  
  .update-progress-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    font-size: 14px;
    color: var(--text-primary);
  }
  
  .update-percentage {
    font-size: 14px;
    font-weight: 600;
    color: #4caf50;
  }
  
  .progress-container {
    position: relative;
    width: 100%;
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .update-progress-bar {
    border-radius: 4px;
    overflow: hidden;
  }
  
  .progress-overlay {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, rgba(76, 175, 80, 0.2) 0%, rgba(76, 175, 80, 0.5) 50%, rgba(76, 175, 80, 0.2) 100%);
    background-size: 200% 100%;
    animation: moveGradient 2s linear infinite;
    border-radius: 4px;
    pointer-events: none;
  }
  
  @keyframes moveGradient {
    0% {
      background-position: 0% 0%;
    }
  
    100% {
      background-position: 200% 0%;
    }
  }
  
  .update-message {
    font-size: 14px;
    color: var(--text-secondary);
    background-color: var(--background-color);
    padding: 12px 16px;
    border-radius: 8px;
    margin-top: 16px;
    position: relative;
    transition: all 0.3s ease;
  }
  
  .update-message.completed {
    color: #2e7d32;
    background-color: #e8f5e9;
    font-weight: 500;
  }
  
  .update-dialog-actions {
    padding: 12px 24px;
    display: flex;
    justify-content: flex-end;
    background-color: var(--card-background);
  }
  
  .update-btn {
    min-width: 90px;
    background-color: #4caf50 !important;
    color: white !important;
    margin-left: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 8px !important;
    height: 40px !important;
    letter-spacing: 0.5px;
  }
  
  .update-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3) !important;
  }
  
  /* 图表样式 */
  .chart-container {
    width: 100%;
    height: 180px;
    position: relative;
    margin-bottom: 10px;
  }
  
  .gauge-chart-container {
    text-align: center;
    position: relative;
    width: 100%;
    height: 150px;
    margin-bottom: 8px;
    overflow: visible;
  }
  
  .metrics-row {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
  }
  
  .metric-box {
    flex: 1;
    text-align: center;
    padding: 10px;
    background-color: #f6f8fa;
    border-radius: 6px;
    margin: 0 6px;
  }
  
  .metric-title {
    font-size: 13px;
    color: #757575;
    margin-bottom: 4px;
  }
  
  .metric-big-value {
    font-size: 24px;
    font-weight: 600;
    color: #333;
  }
  
  .unit {
    font-size: 14px;
    font-weight: normal;
    margin-left: 2px;
  }
  
  .pipeline-container {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  
  .pipeline-progress {
    margin-top: 5px;
    padding: 0 10px;
  }
  
  .progress-label-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
  }
  
  .progress-label {
    font-size: 13px;
    color: #757575;
  }
  
  .progress-value {
    font-size: 13px;
    font-weight: 600;
    color: #4caf50;
  }
  
  /* 健康指数卡片特定样式 */
  .health-metrics {
    margin-top: 3px;
  }
  
  .metric-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
  }
  
  .metric {
    flex: 1;
    background-color: rgba(0, 0, 0, 0.02);
    padding: 6px 8px;
    border-radius: 5px;
    margin: 0 3px;
  }
  
  /* 为每个指标添加特定背景色 */
  .metric:nth-child(1) {
    background-color: #e3f2fd;
    /* 浅蓝色背景 - 完整性 */
  }
  
  .metric:nth-child(2) {
    background-color: #e8f5e9;
    /* 浅绿色背景 - 初始性 */
  }
  
  .metric-row:nth-child(2) .metric:nth-child(1) {
    background-color: #f5f5f5;
    /* 浅灰色背景 - 准确性 */
  }
  
  .metric-row:nth-child(2) .metric:nth-child(2) {
    background-color: #f3e5f5;
    /* 浅紫色背景 - 一致性 */
  }
  
  .metric-label {
    font-size: 13px;
    color: #757575;
    display: flex;
    align-items: center;
    margin-bottom: 5px;
  }
  
  .metric-value-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .metric-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
  
  .metric-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
  }
  
  .metric-dot.blue {
    background-color: #2196F3;
  }
  
  .metric-dot.green {
    background-color: #4CAF50;
  }
  
  .metric-dot.black {
    background-color: #333;
  }
  
  .metric-dot.purple {
    background-color: #9C27B0;
  }
  
  /* 所有模块卡片的样式调整 */
  .module-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  .module-card .module-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    padding: 6px 12px 12px;
  }
  
  .module-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1) !important;
  }
  
  .module-title {
    font-size: 16px;
    font-weight: 500;
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
  }
  
  .module-content {
    padding: 16px;
  }
  
  .dashboard-modules .v-col {
    display: flex;
    margin-bottom: 10px;
  }
  
  @media (max-width: 960px) {
    .dashboard-modules .v-col {
      margin-bottom: 10px;
    }
  }
  
  /* 确保图表容器有足够的空间和正确的样式 */
  #healthGaugeChart,
  #queryChart {
    min-height: 200px !important;
    width: 100% !important;
    height: 200px !important;
    margin: 0 auto;
    position: relative !important;
    display: block !important;
    visibility: visible !important;
    z-index: 1;
  }
  
  .chart-container,
  .gauge-chart-container {
    width: 100%;
    min-height: 130px;
    margin-bottom: 8px;
    position: relative;
    overflow: visible;
    z-index: 1;
  }
  
  .gauge-chart-container {
    text-align: center;
    position: relative;
    width: 100%;
    height: 160px;
    margin-bottom: 10px;
    overflow: visible;
  }
  
  /* 水平流水线步骤样式 */
  .pipeline-steps-horizontal {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    padding: 5px 0;
    margin-bottom: 8px;
  }
  
  .pipeline-connector-horizontal {
    flex: 1;
    height: 2px;
    background-color: #e0e0e0;
    margin: 18px 5px 0;
  }
  
  .pipeline-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 70px;
  }
  
  .step-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    margin-bottom: 6px;
  }
  
  .step-icon.blue {
    background-color: #4e9ff5;
  }
  
  .step-icon.purple {
    background-color: #9378fa;
  }
  
  .step-icon.green {
    background-color: #46c35f;
  }
  
  .step-icon.red {
    background-color: #ff5c77;
  }
  
  .step-info {
    width: 100%;
  }
  
  .step-name {
    font-size: 14px;
    color: #555;
    margin-bottom: 2px;
    white-space: nowrap;
  }
  
  .step-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
  
  .pipeline-container {
    padding: 0;
  }
  
  .pipeline-progress {
    margin-top: 3px;
    padding: 5px 10px;
  }
  
  .progress-label-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3px;
  }
  
  .progress-label {
    font-size: 14px;
    color: #757575;
  }
  
  .progress-value {
    font-size: 14px;
    font-weight: 600;
    color: #4caf50;
  }
  
  /* 所有模块卡片的样式调整 */
  .module-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .module-card .module-content {
    padding: 8px 12px;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  .module-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1) !important;
  }
  
  .module-title {
    font-size: 14px;
    font-weight: 500;
    padding: 10px 12px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
  }
  
  .dashboard-modules .v-col {
    display: flex;
  }
  
  .dashboard-modules .v-col .v-card {
    width: 100%;
  }
  
  .metrics-row {
    display: flex;
    justify-content: space-between;
    margin-top: 6px;
  }
  
  .metric-box {
    flex: 1;
    text-align: center;
    padding: 6px;
    background-color: #f6f8fa;
    border-radius: 6px;
    margin: 0 3px;
  }
  
  .metric-title {
    font-size: 11px;
    color: #757575;
    margin-bottom: 2px;
  }
  
  .metric-big-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
  
  .unit {
    font-size: 11px;
    font-weight: normal;
    margin-left: 2px;
  }
  
  /* 健康指数卡片特定样式 */
  .health-metrics {
    margin-top: 3px;
  }
  
  .metric-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
  }
  
  .metric {
    flex: 1;
    background-color: rgba(0, 0, 0, 0.02);
    padding: 6px 8px;
    border-radius: 5px;
    margin: 0 3px;
  }
  
  /* 为每个指标添加特定背景色 */
  .metric:nth-child(1) {
    background-color: #e3f2fd;
    /* 浅蓝色背景 - 完整性 */
  }
  
  .metric:nth-child(2) {
    background-color: #e8f5e9;
    /* 浅绿色背景 - 初始性 */
  }
  
  .metric-row:nth-child(2) .metric:nth-child(1) {
    background-color: #f5f5f5;
    /* 浅灰色背景 - 准确性 */
  }
  
  .metric-row:nth-child(2) .metric:nth-child(2) {
    background-color: #f3e5f5;
    /* 浅紫色背景 - 一致性 */
  }
  
  .metric-label {
    font-size: 13px;
    color: #757575;
    display: flex;
    align-items: center;
    margin-bottom: 5px;
  }
  
  .metric-value-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .metric-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
  
  .metric-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
  }
  
  .metric-dot.blue {
    background-color: #2196F3;
  }
  
  .metric-dot.green {
    background-color: #4CAF50;
  }
  
  .metric-dot.black {
    background-color: #333;
  }
  
  .metric-dot.purple {
    background-color: #9C27B0;
  }
  
  @media (max-width: 960px) {
    .dashboard-modules .v-col {
      margin-bottom: 10px;
    }
  
    .pipeline-steps-horizontal {
      flex-wrap: wrap;
    }
  
    .pipeline-step {
      margin-bottom: 8px;
    }
  }
  
  /* 知识增长趋势图表和知识来源分布图表样式 */
  .growth-rate {
    margin-left: auto;
    font-size: 14px;
    color: #757575;
  }
  
  .growth-rate .positive {
    color: #4CAF50;
    font-weight: 500;
  }
  
  .chart-legend {
    display: flex;
    justify-content: center;
    margin-top: 10px;
    flex-wrap: wrap;
  }
  
  .legend-item {
    display: flex;
    align-items: center;
    margin: 0 12px;
  }
  
  .legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 6px;
  }
  
  .legend-color.document {
    background-color: #1976D2;
  }
  
  .legend-color.instruction {
    background-color: #00BFA5;
  }
  
  .legend-color.relation {
    background-color: #FFA000;
  }
  
  .legend-text {
    font-size: 13px;
    color: #757575;
  }
  
  .distribution-container {
    display: flex;
    flex-direction: row;
    height: 100%;
  }
  
  .chart-side {
    flex: 1;
    min-width: 0;
  }
  
  .distribution-stats {
    flex: 1;
    padding-left: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .distribution-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }
  
  .stat-label {
    width: 85px;
    font-size: 14px;
    color: #555;
    white-space: nowrap;
  }
  
  .stat-progress {
    flex: 1;
    height: 6px;
    background-color: #f0f0f0;
    border-radius: 3px;
    margin: 0 10px;
    overflow: hidden;
  }
  
  .progress-bar {
    height: 100%;
    border-radius: 3px;
  }
  
  .progress-bar.engineering {
    background-color: #2196F3;
  }
  
  .progress-bar.instruction {
    background-color: #4CAF50;
  }
  
  .progress-bar.electronic {
    background-color: #FFC107;
  }
  
  .progress-bar.standard {
    background-color: #F44336;
  }
  
  .stat-value {
    width: 40px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    text-align: right;
  }
  
  @media (max-width: 960px) {
    .distribution-container {
      flex-direction: column;
    }
  
    .chart-side {
      margin-bottom: 20px;
    }
  
    .distribution-stats {
      padding-left: 0;
    }
  }
  
  @media (max-width: 960px) {
    .dashboard-modules .v-col {
      margin-bottom: 10px;
    }
  
    .pipeline-steps-horizontal {
      flex-wrap: wrap;
    }
  
    .pipeline-step {
      margin-bottom: 8px;
    }
  }
  
  .circle-legend-container {
    display: flex;
    justify-content: center;
    margin-top: 10px;
    border-top: 1px solid #eeeeee;
    padding-top: 10px;
  }
  
  .circle-legend-item {
    display: flex;
    align-items: center;
    margin: 0 15px;
  }
  
  .circle-icon {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
    border: 2px solid;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
  }
  
  .circle-icon.document {
    border-color: #1976D2;
  }
  
  .circle-icon.node {
    border-color: #00BFA5;
  }
  
  .circle-icon.relation {
    border-color: #FFA000;
  }
  
  .circle-icon .inner-circle {
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
  
  .circle-icon.document .inner-circle {
    background-color: #1976D2;
  }
  
  .circle-icon.node .inner-circle {
    background-color: #00BFA5;
  }
  
  .circle-icon.relation .inner-circle {
    background-color: #FFA000;
  }
  
  .circle-legend-item span {
    font-size: 13px;
    color: #666;
  }
  
  .pie-legend-container {
    display: flex;
    justify-content: center;
    margin-top: 10px;
    border-top: 1px solid #eeeeee;
    padding-top: 10px;
  }
  
  .pie-legend-item {
    display: flex;
    align-items: center;
    margin: 0 10px;
  }
  
  .pie-legend-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
  }
  
  .pie-legend-dot.engineering {
    background-color: #2196F3;
  }
  
  .pie-legend-dot.instruction {
    background-color: #4CAF50;
  }
  
  .pie-legend-dot.electronic {
    background-color: #FFC107;
  }
  
  .pie-legend-dot.standard {
    background-color: #F44336;
  }
  
  .pie-legend-item span {
    font-size: 13px;
    color: #666;
  }
  
  /* 自定义图例样式 */
  .custom-legend-container {
    display: flex;
    justify-content: center;
    margin-top: 5px;
    padding-top: 15px;
    border-top: 1px solid #eee;
  }
  
  .legend-item {
    display: flex;
    align-items: center;
    margin: 0 20px;
    cursor: pointer;
  }
  
  .legend-icon {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
    border: 2px solid;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
  }
  
  .legend-icon.document {
    border-color: #1976D2;
  }
  
  .legend-icon.node {
    border-color: #00BFA5;
  }
  
  .legend-icon.relation {
    border-color: #FFA000;
  }
  
  .legend-icon .inner-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
  
  .legend-icon.document .inner-dot {
    background-color: #1976D2;
  }
  
  .legend-icon.node .inner-dot {
    background-color: #00BFA5;
  }
  
  .legend-icon.relation .inner-dot {
    background-color: #FFA000;
  }
  
  .legend-text {
    font-size: 13px;
    color: #666;
  }
  
  .legend-icon.inactive {
    opacity: 0.5;
  }
  
  .legend-icon.inactive .inner-dot {
    background-color: #ccc;
  }
  
  .system-overview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 8px;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .overview-left {
    display: flex;
    align-items: center;
  }
  
  .overview-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    display: flex;
    align-items: center;
  }
  
  .system-cards-container {
    display: flex;
    align-items: center;
    position: relative;
    margin-top: 10px;
    overflow: hidden;
    width: 100%;
  }
  
  .system-cards-wrapper {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(2, 180px);
    gap: 16px;
    width: 100%;
    padding: 10px 0;
  }
  
  @media (max-width: 1200px) {
    .system-cards-wrapper {
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: auto;
    }
  }
  
  @media (max-width: 768px) {
    .system-cards-wrapper {
      grid-template-columns: repeat(2, 1fr);
    }
  
    .view-mode-buttons {
      width: 100%;
    }
  
    .navigation-button {
      width: 36px;
      height: 36px;
    }
  }
  
  @media (max-width: 480px) {
    .system-cards-wrapper {
      grid-template-columns: 1fr;
    }
  
    .system-card {
      min-height: 150px;
    }
  
    .view-toggle-buttons {
      width: 100%;
    }
  
    .view-btn {
      flex: 1;
    }
  }
  
  .navigation-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 100;
    color: #777;
    flex-shrink: 0;
    position: absolute;
  }
  
  .navigation-button:hover:not(.disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background-color: #4285f4;
    color: white;
  }
  
  .navigation-button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #f1f1f1;
    color: #999;
    box-shadow: none;
  }
  
  .navigation-button.left {
    left: 5px;
  }
  
  .navigation-button.right {
    right: 5px;
  }
  
  .cards-page-container {
    width: 100%;
    padding: 0 50px;
    overflow: hidden;
  }
  
  .view-mode-buttons {
    display: flex;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .view-toggle-buttons {
    display: flex;
    border-radius: 8px;
    background-color: #f5f7fa;
    padding: 4px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }
  
  .view-btn {
    background-color: transparent;
    border: none;
    color: #637381;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
  }
  
  @media (max-width: 576px) {
    .view-btn {
      min-width: auto;
      padding: 8px 10px;
      font-size: 13px;
    }
  
    .relation-top-controls {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
  
    .right-actions {
      width: 100%;
      justify-content: space-between;
    }
  
    .left-buttons {
      width: 100%;
      justify-content: space-between;
    }
  
    .control-btn {
      flex: 1;
      min-width: auto;
    }
  }
  
  .relation-view-container {
    display: flex;
    flex-direction: column;
    height: auto;
    margin: 0;
    padding: 0;
    position: relative;
    width: 100%;
    overflow: visible;
    min-height: 360px;
  }
  
  @media (max-width: 768px) {
    .relation-graph {
      height: 400px !important;
    }
  
    .relation-view-container {
      min-height: 400px;
    }
  
    #relationChart {
      height: 400px !important;
    }
  }
  
  @media (max-width: 576px) {
    .relation-graph {
      height: 300px !important;
    }
  
    .relation-view-container {
      min-height: 300px;
    }
  
    #relationChart {
      height: 300px !important;
    }
  }
  
  .relation-graph {
    background-color: #fff;
    border-radius: 8px;
    margin-top: 0;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    padding: 20px;
    width: 100%;
    overflow: visible;
  }
  
  .relation-top-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #f0f0f0;
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .left-buttons {
    display: flex;
  }
  
  .control-btn {
    background-color: transparent;
    border: none;
    color: #757575;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    padding: 8px 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    border-radius: 0;
    position: relative;
  }
  
  .system-card {
    border-radius: 8px;
    border-left: 4px solid;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05) !important;
    height: 100%;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    background-color: white;
    display: flex;
    flex-direction: column;
    min-height: 150px;
  }
  
  .system-card.building-system {
    border-left-color: #4285f4;
  }
  
  .system-card.security-system {
    border-left-color: #ea4335;
  }
  
  .system-card.network-system {
    border-left-color: #34a853;
  }
  
  .system-card.audio-system {
    border-left-color: #9c27b0;
  }
  
  .system-card.fire-system {
    border-left-color: #ff9800;
  }
  
  .system-card.elevator-system {
    border-left-color: #607d8b;
  }
  
  .system-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08) !important;
  }
  
  .system-card-header {
    display: flex;
    align-items: center;
    padding: 14px 16px;
    background-color: white;
    border-bottom: 1px solid #f5f5f5;
  }
  
  .system-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 14px;
    flex-shrink: 0;
    transition: all 0.3s ease;
  }
  
  .system-card:hover .system-icon {
    transform: scale(1.1);
  }
  
  .system-icon.building {
    background-color: #4285f4;
  }
  
  .system-icon.security {
    background-color: #ea4335;
  }
  
  .system-icon.network {
    background-color: #34a853;
  }
  
  .system-icon.audio {
    background-color: #9c27b0;
  }
  
  .system-icon.fire {
    background-color: #ff9800;
  }
  
  .system-icon.elevator {
    background-color: #607d8b;
  }
  
  .system-info {
    flex: 1;
    min-width: 0;
  }
  
  .system-name {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .system-desc {
    font-size: 12px;
    color: #757575;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .system-card-body {
    padding: 10px 14px 14px;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  .system-metrics {
    margin-top: 8px;
  }
  
  .metrics-row {
    display: flex;
    justify-content: space-between;
  }
  
  .system-metric {
    font-size: 13px;
    color: #757575;
  }
  
  .system-status-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: auto;
    border-top: 1px solid #f5f5f5;
    padding-top: 10px;
  }
  
  .system-status {
    display: flex;
    align-items: center;
  }
  
  .status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 6px;
  }
  
  .system-status.normal .status-dot {
    background-color: #4caf50;
  }
  
  .system-status.warning .status-dot {
    background-color: #ffa726;
  }
  
  .system-status.error .status-dot {
    background-color: #f44336;
  }
  
  .system-status span {
    font-size: 13px;
    color: #333;
  }
  
  .system-status.normal span {
    color: #4caf50;
  }
  
  .system-status.warning span {
    color: #ffa726;
  }
  
  .system-status.error span {
    color: #f44336;
  }
  
  .view-more {
    font-size: 13px;
    color: #1976d2;
    text-decoration: none;
    transition: all 0.3s ease;
  }
  
  .view-more:hover {
    text-decoration: underline;
  }
  
  .relation-view-container {
    display: flex;
    flex-direction: column;
    height: auto;
    margin: 0;
    padding: 0;
    position: relative;
    width: 100%;
    overflow: visible;
    min-height: 360px;
  }
  
  .relation-view-header {
    display: flex;
    justify-content: center;
    padding-top: 40px;
    margin-bottom: 10px;
  }
  
  .relation-legend {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .legend-item {
    display: flex;
    align-items: center;
    margin: 0 12px;
    font-size: 13px;
    color: #666;
  }
  
  .legend-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 6px;
  }
  
  .legend-dot.building {
    background-color: #4e81bd;
  }
  
  .legend-dot.security {
    background-color: #e36159;
  }
  
  .legend-dot.network {
    background-color: #5baa5f;
  }
  
  .legend-dot.audio {
    background-color: #a07662;
  }
  
  .legend-dot.fire {
    background-color: #f79646;
  }
  
  .legend-dot.elevator {
    background-color: #7fccbf;
  }
  
  .relation-graph {
    background-color: #fff;
    border-radius: 8px;
    margin-top: 0;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    padding: 20px;
    width: 100%;
    overflow: visible;
  }
  
  .system-node {
    position: absolute;
    text-align: center;
    z-index: 2;
    cursor: pointer;
  }
  
  .node-circle {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    margin: 0 auto 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }
  
  .system-node:hover .node-circle {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  }
  
  .node-label {
    font-size: 14px;
    color: #555;
    white-space: nowrap;
  }
  
  .building-node .node-circle {
    background-color: #4e81bd;
  }
  
  .security-node .node-circle {
    background-color: #e36159;
  }
  
  .network-node .node-circle {
    background-color: #5baa5f;
  }
  
  .audio-node .node-circle {
    background-color: #a07662;
  }
  
  .fire-node .node-circle {
    background-color: #f79646;
  }
  
  .elevator-node .node-circle {
    background-color: #7fccbf;
  }
  
  .connection-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
  }
  
  .connection {
    fill: none;
    stroke-width: 2;
    stroke-linecap: round;
    opacity: 0.6;
    transition: all 0.3s ease;
  }
  
  .connection:hover {
    stroke-width: 3;
    opacity: 1;
  }
  
  .connection-label {
    font-size: 12px;
    fill: #777;
    text-anchor: middle;
  }
  
  .building-to-security {
    stroke: rgba(78, 129, 189, 0.7);
  }
  
  .security-to-elevator {
    stroke: rgba(227, 97, 89, 0.7);
  }
  
  .building-to-network {
    stroke: rgba(78, 129, 189, 0.7);
  }
  
  .network-to-security {
    stroke: rgba(91, 170, 95, 0.7);
  }
  
  .network-to-fire {
    stroke: rgba(91, 170, 95, 0.7);
  }
  
  .audio-to-network {
    stroke: rgba(176, 125, 98, 0.6);
  }
  
  .fire-to-audio {
    stroke: rgba(247, 150, 70, 0.7);
  }
  
  .fire-to-network {
    stroke: rgba(255, 152, 0, 0.6);
  }
  
  .building-to-network {
    stroke: rgba(66, 133, 244, 0.6);
  }
  
  .fire-to-building {
    stroke: rgba(247, 150, 70, 0.7);
  }
  
  .audio-to-fire {
    stroke: rgba(160, 118, 98, 0.7);
  }
  
  .security-to-fire {
    stroke: rgba(227, 97, 89, 0.7);
  }
  
  .view-mode-buttons {
    display: flex;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .view-toggle-buttons {
    display: flex;
    border-radius: 8px;
    background-color: #f5f7fa;
    padding: 4px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }
  
  .view-btn {
    background-color: transparent;
    border: none;
    color: #637381;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
  }
  
  .view-btn:hover {
    color: #4285f4;
    background-color: rgba(66, 133, 244, 0.05);
  }
  
  .view-btn.active {
    background-color: #ffffff;
    color: #4285f4;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
  }
  
  .view-btn.active::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 3px;
    background-color: #4285f4;
    border-radius: 3px;
    animation: expandWidth 0.3s ease forwards;
  }
  
  @keyframes expandWidth {
    from {
      width: 0;
    }
  
    to {
      width: 20px;
    }
  }
  
  .relation-layout-controls {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
  }
  
  .layout-buttons {
    display: flex;
    border-radius: 8px;
    background-color: #f5f7fa;
    padding: 4px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }
  
  .layout-btn {
    background-color: transparent;
    border: none;
    color: #637381;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
  }
  
  .layout-btn:hover {
    color: #4285f4;
    background-color: rgba(66, 133, 244, 0.05);
  }
  
  .layout-btn.active {
    background-color: #ffffff;
    color: #4285f4;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
  }
  
  .layout-btn.active::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 3px;
    background-color: #4285f4;
    border-radius: 3px;
    animation: expandWidth 0.3s ease forwards;
  }
  
  .relation-top-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #f0f0f0;
  }
  
  .left-buttons {
    display: flex;
  }
  
  .control-btn {
    background-color: transparent;
    border: none;
    color: #757575;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    padding: 8px 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    border-radius: 0;
    position: relative;
  }
  
  .control-btn:not(:last-child)::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    height: 16px;
    width: 1px;
    background-color: #e0e0e0;
  }
  
  .control-btn:hover {
    color: #4285f4;
    background-color: rgba(66, 133, 244, 0.05);
  }
  
  .control-btn.active {
    color: #4285f4;
  }
  
  .right-actions {
    display: flex;
    align-items: center;
  }
  
  .action-label {
    font-size: 14px;
    color: #757575;
    margin-right: 8px;
  }
  
  .action-icons {
    display: flex;
    gap: 5px;
    border-radius: 4px;
    background-color: #f5f7fa;
    padding: 4px;
    margin-right: 5px;
  }
  
  .icon-btn {
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 4px;
  }
  
  .icon-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
  
  .icon-btn.active {
    background-color: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  .system-detail-dialog {
    background-color: var(--card-background);
    border-radius: 12px;
    overflow: hidden;
  }
  
  .system-detail-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }
  
  .system-detail-header {
    padding: 16px 20px;
    position: relative;
  }
  
  .system-detail-header.network-system {
    background-color: rgba(52, 168, 83, 0.08);
  }
  
  .system-detail-header.building-system {
    background-color: rgba(66, 133, 244, 0.08);
  }
  
  .system-detail-header.security-system {
    background-color: rgba(234, 67, 53, 0.08);
  }
  
  .system-detail-header.audio-system {
    background-color: rgba(156, 39, 176, 0.08);
  }
  
  .system-detail-header.fire-system {
    background-color: rgba(255, 152, 0, 0.08);
  }
  
  .system-detail-header.elevator-system {
    background-color: rgba(96, 125, 139, 0.08);
  }
  
  .detail-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  
  .detail-title-wrapper {
    display: flex;
    align-items: center;
  }
  
  .detail-icon-wrapper {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 14px;
    flex-shrink: 0;
  }
  
  .detail-icon-wrapper.network-system {
    background-color: #34a853;
  }
  
  .detail-icon-wrapper.building-system {
    background-color: #4285f4;
  }
  
  .detail-icon-wrapper.security-system {
    background-color: #ea4335;
  }
  
  .detail-icon-wrapper.audio-system {
    background-color: #9c27b0;
  }
  
  .detail-icon-wrapper.fire-system {
    background-color: #ff9800;
  }
  
  .detail-icon-wrapper.elevator-system {
    background-color: #607d8b;
  }
  
  .detail-title-text {
    display: flex;
    flex-direction: column;
  }
  
  .detail-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
  }
  
  .detail-subtitle {
    font-size: 13px;
    color: #757575;
  }
  
  .close-detail-icon {
    cursor: pointer;
    opacity: 0.6;
    transition: all 0.2s ease;
  }
  
  .close-detail-icon:hover {
    opacity: 1;
    transform: rotate(90deg);
  }
  
  .system-detail-content {
    padding: 12px 20px 16px;
  }
  
  .detail-sections-row {
    display: flex;
    gap: 16px;
    margin-bottom: 18px;
  }
  
  .detail-section {
    margin-bottom: 14px;
  }
  
  .detail-section.half-width {
    flex: 1;
    margin-bottom: 0;
  }
  
  .detail-section:last-child {
    margin-bottom: 0;
  }
  
  .detail-section-title {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
  }
  
  .status-stats-row {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .status-stat-card {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 8px 10px;
    border-radius: 8px;
    background-color: #f6f8fa;
    border: 1px solid #f0f0f0;
  }
  
  .stat-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    flex-shrink: 0;
  }
  
  .stat-icon.blue {
    background-color: #4285f4;
  }
  
  .stat-icon.green {
    background-color: #34a853;
  }
  
  .stat-icon.red {
    background-color: #ea4335;
  }
  
  .stat-content {
    flex: 1;
  }
  
  .stat-label {
    font-size: 12px;
    color: #757575;
    margin-bottom: 1px;
  }
  
  .stat-value {
    font-size: 15px;
    font-weight: 600;
    color: #333;
  }
  
  .warning-label {
    display: flex;
    align-items: center;
    font-size: 15px;
    font-weight: 600;
    color: #ffa726;
  }
  
  .warning-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #ffa726;
    margin-right: 6px;
    animation: pulse 1.5s infinite;
  }
  
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(255, 167, 38, 0.4);
    }
  
    70% {
      box-shadow: 0 0 0 5px rgba(255, 167, 38, 0);
    }
  
    100% {
      box-shadow: 0 0 0 0 rgba(255, 167, 38, 0);
    }
  }
  
  .device-stats-row {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .device-stat-card {
    width: calc(50% - 4px);
    min-width: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 5px;
    border-radius: 8px;
    background-color: #f6f8fa;
    border: 1px solid #f0f0f0;
    text-align: center;
  }
  
  .device-stat-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4px;
  }
  
  .device-stat-icon.blue-light {
    background-color: #e3f2fd;
  }
  
  .device-stat-icon.purple-light {
    background-color: #f3e5f5;
  }
  
  .device-stat-icon.green-light {
    background-color: #e8f5e9;
  }
  
  .device-stat-icon.indigo-light {
    background-color: #e8eaf6;
  }
  
  .device-stat-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
    line-height: 1;
  }
  
  .device-stat-label {
    font-size: 12px;
    color: #757575;
  }
  
  .alert-table-container,
  .maintenance-table-container {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }
  
  .alert-table,
  .maintenance-table {
    width: 100%;
    border-collapse: collapse;
  }
  
  .alert-table th,
  .maintenance-table th {
    background-color: #f8f9fa;
    color: #757575;
    font-weight: 500;
    padding: 7px 10px;
    text-align: left;
    font-size: 12px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .alert-table td,
  .maintenance-table td {
    padding: 7px 10px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
    font-size: 12px;
    color: #333;
  }
  
  .alert-table tr:last-child td,
  .maintenance-table tr:last-child td {
    border-bottom: none;
  }
  
  .alert-status,
  .maintenance-status {
    display: flex;
    align-items: center;
    font-size: 12px;
  }
  
  .alert-status.pending {
    color: #ff9800;
  }
  
  .alert-status.resolved,
  .maintenance-status.normal,
  .maintenance-status.optimized {
    color: #4caf50;
  }
  
  .alert-status .v-icon,
  .maintenance-status .v-icon {
    margin-right: 3px;
  }
  
  .person-tag {
    display: inline-flex;
    align-items: center;
    background-color: #f0f7ff;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    color: #1976d2;
  }
  
  .system-detail-actions {
    padding: 12px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
  
  .action-btn {
    background-color: #e3f2fd !important;
    color: #4285f4 !important;
    font-weight: 500 !important;
    padding: 0 14px !important;
    border-radius: 6px !important;
    height: 34px !important;
    transition: all 0.3s ease !important;
    font-size: 13px !important;
  }
  
  .action-btn:hover {
    background-color: #bbdefb !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(66, 133, 244, 0.2) !important;
  }
  
  .close-btn {
    background-color: #1976d2 !important;
    color: white !important;
    font-weight: 500 !important;
    padding: 0 14px !important;
    border-radius: 6px !important;
    height: 34px !important;
    transition: all 0.3s ease !important;
    font-size: 13px !important;
  }
  
  .close-btn:hover {
    background-color: #1565c0 !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(25, 118, 210, 0.3) !important;
  }
  
  @media (max-width: 768px) {
    .detail-sections-row {
      flex-direction: column;
      gap: 16px;
    }
  
    .status-stats-row {
      flex-direction: column;
      gap: 8px;
    }
  
    .device-stat-card {
      width: calc(50% - 4px);
    }
  
    .alert-table-container,
    .maintenance-table-container {
      overflow-x: auto;
    }
  }
  
  .status-metrics-grid {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .status-metric-card {
    text-align: center;
  }
  
  .metric-icon {
    margin-bottom: 5px;
  }
  
  .metric-label {
    font-size: 12px;
    color: #757575;
  }
  
  .metric-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
  
  .percent {
    font-size: 12px;
    font-weight: normal;
    margin-left: 2px;
  }
  
  .warning {
    color: #ffa726;
    font-weight: 600;
  }
  
  .status-container {
    display: flex;
    justify-content: space-between;
    background-color: #fafafa;
    border-radius: 8px;
    padding: 15px;
  }
  
  .status-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .status-label {
    font-size: 13px;
    color: #757575;
    margin-bottom: 6px;
    margin-right: 0;
  }
  
  .status-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
  
  .status-value.warning {
    color: #ff9800;
  }
  
  .devices-container {
    display: flex;
    flex-direction: column;
    background-color: #fafafa;
    border-radius: 8px;
    padding: 15px;
    gap: 12px;
  }
  
  .device-item {
    display: flex;
    align-items: center;
  }
  
  .device-icon {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
  }
  
  .device-info {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  
  .device-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-right: 8px;
  }
  
  .device-label {
    font-size: 13px;
    color: #757575;
  }
  
  .device-icon.blue {
    background-color: #4285f4;
  }
  
  .device-icon.purple {
    background-color: #9c27b0;
  }
  
  .device-icon.green {
    background-color: #4CAF50;
  }
  
  .device-icon.blue-dark {
    background-color: #3f51b5;
  }
  
  @media (max-width: 768px) {
    .dashboard-modules .v-col {
      margin-bottom: 10px;
    }
  
    .pipeline-steps-horizontal {
      flex-wrap: wrap;
    }
  
    .pipeline-step {
      margin-bottom: 8px;
    }
  }
  
  /* 状态卡片样式 */
  .status-cards {
    display: flex;
    gap: 12px;
  }
  
  .status-card {
    flex: 1;
    background-color: #f5f9ff;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    flex-direction: column;
  }
  
  .status-card:nth-child(1) {
    background-color: #f5f9ff;
  }
  
  .status-card:nth-child(2) {
    background-color: #f5fcf8;
  }
  
  .status-card:nth-child(3) {
    background-color: #f9f5fc;
  }
  
  .card-title {
    font-size: 14px;
    color: #757575;
    margin-bottom: 8px;
  }
  
  .status-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
  
  .status-warning {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
    color: #ff9800;
  }
  
  .warning-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #ff9800;
    margin-right: 6px;
  }
  
  /* 设备卡片样式 */
  .device-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .device-card {
    background-color: #fff;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    align-items: center;
  }
  
  .device-icon-container {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
  }
  
  .device-info {
    display: flex;
    flex-direction: column;
  }
  
  .device-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
  }
  
  .device-label {
    font-size: 12px;
    color: #757575;
  }
  
  @media (max-width: 768px) {
    .detail-sections-row {
      flex-direction: column;
      gap: 16px;
    }
  
    .status-cards {
      flex-direction: column;
      gap: 10px;
    }
  
    .device-cards {
      grid-template-columns: 1fr;
    }
  }
  
  /* 修改CSS以支持上下布局 */
  .detail-section-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .detail-section.full-width {
    width: 100%;
    margin-bottom: 0;
  }
  
  /* 状态卡片样式 */
  .status-cards {
    display: flex;
    gap: 12px;
  }
  
  .status-card {
    flex: 1;
    background-color: #f5f9ff;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    flex-direction: column;
  }
  
  .status-card:nth-child(1) {
    background-color: #f5f9ff;
  }
  
  .status-card:nth-child(2) {
    background-color: #f5fcf8;
  }
  
  .status-card:nth-child(3) {
    background-color: #f9f5fc;
  }
  
  .card-title {
    font-size: 14px;
    color: #757575;
    margin-bottom: 8px;
  }
  
  .status-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
  
  .status-warning {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
    color: #ff9800;
  }
  
  .warning-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #ff9800;
    margin-right: 6px;
  }
  
  /* 设备卡片样式 */
  .device-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .device-card {
    background-color: #fff;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    align-items: center;
  }
  
  .device-icon-container {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
  }
  
  .device-info {
    display: flex;
    flex-direction: column;
  }
  
  .device-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
  }
  
  .device-label {
    font-size: 12px;
    color: #757575;
  }
  
  @media (max-width: 768px) {
    .status-cards {
      flex-direction: column;
      gap: 10px;
    }
  
    .device-cards {
      grid-template-columns: 1fr;
    }
  }
  </style>