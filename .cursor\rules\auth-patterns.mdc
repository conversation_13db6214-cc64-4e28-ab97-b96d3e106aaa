---
description: 
globs: 
alwaysApply: false
---
# Authentication and Authorization Patterns

## Overview
The application uses a role-based access control (RBAC) system implemented through [permission.js](mdc:src/permission.js).

## Key Concepts
- Authentication state is managed in the Vuex store
- Route guards protect authenticated routes
- Role-based permissions control feature access
- Token-based authentication with refresh mechanism

## Implementation Details
- Navigation guards in [permission.js](mdc:src/permission.js) handle route access
- Environment files contain auth-related configuration
- Auth tokens are stored securely and managed through utilities
- API requests automatically include auth headers

## Best Practices
- Always use route meta for permission requirements
- Handle unauthorized access gracefully
- Implement proper token refresh logic
- Clear sensitive data on logout
- Use proper security headers in API calls

