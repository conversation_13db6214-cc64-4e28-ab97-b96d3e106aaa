---
description: 
globs: 
alwaysApply: false
---
# Coding Conventions

## Vue Component Structure
- Components should be organized using the Single File Component (SFC) pattern
- Use PascalCase for component names
- Component files should be placed in appropriate directories:
  - Common components in `src/components/`
  - Page components in `src/views/`
  - Layout components in `src/layout/`

## File Naming
- Vue components: PascalCase (e.g., `UserProfile.vue`)
- JavaScript utilities: camelCase (e.g., `authUtils.js`)
- API services: camelCase with 'api' suffix (e.g., `userApi.js`)
- Store modules: camelCase (e.g., `userStore.js`)

## Code Style
- Follow the `.editorconfig` settings for consistent formatting
- Use ES6+ features where appropriate
- Prefer async/await over Promise chains
- Use Vue's Composition API for new components
- Keep components focused and single-responsibility

## State Management
- Use Vuex store modules for global state
- Keep component state local when possible
- Document store mutations and actions

## API Integration
- All API calls should be centralized in the `api/` directory
- Use service classes for API endpoint grouping
- Handle errors consistently using utility functions

