/* HomeView 完整样式文件 */

/* 代码块样式 - Atom One Dark风格 */
.code-block {
  margin: 1em auto;
  border: 1px solid #181a1f;
  border-radius: 6px;
  background-color: #282c34;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.6;
}

.message-content .code-block {
  position: relative;
  margin: 16px 0;
}

.message-content .code-header {
  background-color: #21252b;
  padding: 8px 12px;
  border-bottom: 1px solid #181a1f;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message-content .code-language {
  color: #abb2bf;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.message-content .code-actions {
  display: flex;
  gap: 8px;
}

.message-content .action-button {
  background: rgba(171, 178, 191, 0.1);
  border: 1px solid rgba(171, 178, 191, 0.2);
  color: #abb2bf;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s;
}

.message-content .action-button:hover {
  background: rgba(171, 178, 191, 0.2);
  border-color: rgba(171, 178, 191, 0.3);
}

.message-content .code-pre {
  background-color: #282c34;
  color: #abb2bf;
  padding: 0;
  margin: 0;
  overflow-x: auto;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.6;
}

.message-content .code-pre pre {
  margin: 0;
  padding: 0;
  background: transparent;
  border: none;
  overflow: visible;
}

.message-content .code-pre code {
  background: transparent;
  color: inherit;
  padding: 0;
  border: none;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  display: block;
}

.message-content .line-numbers-mode {
  position: relative;
}

.message-content .line-numbers-mode .line-number {
  display: inline-block;
  width: 40px;
  text-align: right;
  padding-right: 12px;
  color: #5c6370;
  background-color: #21252b;
  border-right: 1px solid #181a1f;
  user-select: none;
  font-size: 12px;
  vertical-align: top;
}

.message-content .line-numbers-mode .line-content {
  display: inline-block;
  padding-left: 12px;
  padding-right: 12px;
  min-height: 1.6em;
  vertical-align: top;
  width: calc(100% - 52px);
  box-sizing: border-box;
}

/* Highlight.js 代码高亮样式 - VS Code风格 */
.hljs {
  display: block;
  overflow-x: auto;
  padding: 0;
  background: #282c34;
  color: #abb2bf;
}

.hljs-comment,
.hljs-quote {
  color: #608b4e;
  font-style: italic;
}

.hljs-doctag,
.hljs-keyword,
.hljs-formula {
  color: #569cd6;
  font-weight: bold;
}

.hljs-section,
.hljs-name,
.hljs-selector-tag,
.hljs-deletion,
.hljs-subst {
  color: #e06c75;
}

.hljs-literal {
  color: #56b6c2;
}

.hljs-string,
.hljs-regexp,
.hljs-addition,
.hljs-attribute,
.hljs-meta-string {
  color: #ce9178;
}

.hljs-built_in,
.hljs-class .hljs-title {
  color: #4ec9b0;
}

.hljs-attr,
.hljs-variable,
.hljs-template-variable,
.hljs-type,
.hljs-selector-class,
.hljs-selector-attr,
.hljs-selector-pseudo,
.hljs-number {
  color: #b5cea8;
}

/* SQL特定样式 */
.language-sql .hljs-keyword {
  color: #569cd6;
  font-weight: bold;
}

.language-sql .hljs-built_in,
.language-sql .hljs-function {
  color: #dcdcaa;
}

.language-sql .hljs-number {
  color: #b5cea8;
}

.language-sql .hljs-string {
  color: #ce9178;
}

.language-sql .hljs-operator {
  color: #d4d4d4;
}

.hljs-symbol,
.hljs-bullet,
.hljs-link,
.hljs-meta,
.hljs-selector-id,
.hljs-title {
  color: #61aeee;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}

.hljs-link {
  text-decoration: underline;
}

/* 思考步骤样式 */
.thinking-steps {
  margin: 16px 0;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
  overflow: hidden;
}

.thinking-header {
  background-color: #f5f5f5;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: background-color 0.3s;
}

.thinking-header:hover {
  background-color: #eeeeee;
}

.thinking-title {
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.thinking-icon {
  color: #666;
  font-size: 16px;
}

.thinking-content {
  max-height: 300px;
  overflow-y: auto;
  transition: max-height 0.3s ease;
}

.thinking-content.collapsed {
  max-height: 0;
  overflow: hidden;
}

.thinking-step {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fff;
}

.thinking-step:last-child {
  border-bottom: none;
}

.step-header {
  font-weight: 500;
  color: #555;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-number {
  background-color: #e8f0fe;
  color: #1976d2;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.step-content {
  color: #666;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.last-step .step-number {
  background-color: #e8f5e8;
  color: #4CAF50;
}

.last-step .step-header {
  color: #4CAF50;
}

/* 错误消息样式 */
.error-message {
  background-color: #fff2f0;
  border: 1px solid #ef9a9a;
  color: #d32f2f;
  padding: 12px 16px;
  border-radius: 4px;
  margin: 10px 0;
}

/* 打字指示器样式 */
.typing-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px 0;
}

.typing-indicator .dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #2196F3;
  margin: 0 3px;
  animation: typing-animation 1.4s infinite ease-in-out both;
}

.typing-indicator .dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator .dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing-animation {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.formal-content-loading {
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 8px;
  text-align: center;
}

/* 聊天容器高亮效果 */
@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(26, 115, 232, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(26, 115, 232, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(26, 115, 232, 0);
  }
}

.highlight-section {
  animation: highlight-pulse 1.5s ease-out infinite;
  transition: all 0.3s ease;
  border: 2px solid #1a73e8 !important;
}

/* 返回顶部按钮样式 */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background-color: #1677ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
  transition: all 0.3s ease;
  z-index: 1000;
}

.back-to-top:hover {
  background-color: #4096ff;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(22, 119, 255, 0.4);
}

.back-to-top .el-icon {
  color: white;
  font-size: 20px;
}

/* 输入区域样式 */
.input-area {
  padding: 20px;
  background-color: #fff;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 0 0 12px 12px;
  position: relative;
  min-height: 120px;
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  position: relative;
}

.input-wrapper {
  flex: 1;
  position: relative;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 12px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.input-wrapper:focus-within {
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
}

.message-input {
  width: 100%;
  min-height: 44px;
  max-height: 120px;
  padding: 12px 50px 12px 16px;
  border: none;
  outline: none;
  resize: none;
  font-size: 14px;
  line-height: 1.5;
  background: transparent;
  font-family: inherit;
}

.message-input::placeholder {
  color: #999;
}

.upload-button {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 6px;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-button:hover {
  background-color: #f5f5f5;
}

.upload-icon {
  font-size: 20px;
  color: #666;
}

.send-button {
  padding: 12px 24px;
  background-color: #1677ff;
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 6px;
  min-height: 44px;
}

.send-button:hover:not(:disabled) {
  background-color: #4096ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
}

.send-button:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 文件上传区域样式 */
.file-upload-area {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  font-size: 13px;
  max-width: 200px;
}

.file-info {
  flex: 1;
  margin-left: 8px;
  overflow: hidden;
}

.file-name {
  color: #333;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size {
  color: #666;
  font-size: 11px;
}

.file-remove {
  background: none;
  border: none;
  cursor: pointer;
  color: #ff4d4f;
  padding: 2px;
  border-radius: 4px;
  transition: background-color 0.3s;
  margin-left: 8px;
}

.file-remove:hover {
  background-color: #fff2f0;
}
