<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="请输入护理单元名称"
            clearable
            prefix-icon="Search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deptOptions"
            :props="{ label: 'label', children: 'children' }"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="deptTreeRef"
            node-key="id"
            highlight-current
            default-expand-all
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <el-col :span="20" :xs="24">
        <el-form
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          v-show="showSearch"
          label-width="68px"
          @submit.native.prevent
        >
          <el-form-item label="班次名称" prop="shiftsName">
            <el-input
              v-model="queryParams.shiftsName"
              placeholder="请输入班次名称"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="所属护理单元/科室id" prop="deptId" v-if="false">
            <el-input
              v-model="queryParams.deptId"
              placeholder="请输入所属护理单元/科室id"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item
            label="所属护理单元/科室名称"
            prop="deptName"
            label-width="168px"
            v-if="false"
          >
            <el-input
              v-model="queryParams.deptName"
              placeholder="请输入所属护理单元/科室名称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="简称" prop="shortName" v-if="false">
            <el-input
              v-model="queryParams.shortName"
              placeholder="请输入简称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="开始时间" prop="startTime" v-if="false">
            <el-date-picker
              clearable
              v-model="queryParams.startTime"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="请选择开始时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="结束时间" prop="endTime" v-if="false">
            <el-date-picker
              clearable
              v-model="queryParams.endTime"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="请选择结束时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="时长" prop="duration" v-if="false">
            <el-input
              v-model="queryParams.duration"
              placeholder="请输入时长"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="前半夜时长" prop="halfDuration" v-if="false">
            <el-input
              v-model="queryParams.halfDuration"
              placeholder="请输入前半夜时长"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="系数" prop="coefficient" v-if="false">
            <el-input
              v-model="queryParams.coefficient"
              placeholder="请输入系数"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="分值" prop="score" v-if="false">
            <el-input
              v-model="queryParams.score"
              placeholder="请输入分值"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="颜色" prop="color" v-if="false">
            <el-input
              v-model="queryParams.color"
              placeholder="请输入颜色"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="排序" prop="orderNum" v-if="false">
            <el-input
              v-model="queryParams.orderNum"
              placeholder="请输入排序"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="夜班标识" prop="nightFlag" v-if="false">
            <el-select
              v-model="queryParams.nightFlag"
              placeholder="请选择夜班标识"
              clearable
            >
              <el-option
                v-for="dict in sys_yes_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="凌晨开始标志" prop="earlyStartFlag" v-if="false">
            <el-select
              v-model="queryParams.earlyStartFlag"
              placeholder="请选择凌晨开始标志"
              clearable
            >
              <el-option
                v-for="dict in sys_yes_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status" v-if="false">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
              <el-option
                v-for="dict in sys_yes_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="连7变40标志" prop="sevenFourtyFlag" v-if="false">
            <el-select
              v-model="queryParams.sevenFourtyFlag"
              placeholder="请选择连7变40标志"
              clearable
            >
              <el-option
                v-for="dict in sys_yes_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="在岗状态" prop="onLeave" v-if="false">
            <el-select
              v-model="queryParams.onLeave"
              placeholder="请选择在岗状态"
              clearable
            >
              <el-option
                v-for="dict in sys_yes_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="算作白班标志" prop="isWhiteFlag" v-if="false">
            <el-select
              v-model="queryParams.isWhiteFlag"
              placeholder="请选择算作白班标志"
              clearable
            >
              <el-option
                v-for="dict in sys_yes_no"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="Plus"
              @click="handleAdd"
              v-hasPermi="['arr:workShifts:add']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="Edit"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['arr:workShifts:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['arr:workShifts:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="Download"
              @click="handleExport"
              v-hasPermi="['arr:workShifts:export']"
              >导出</el-button
            >
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="workShiftsList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="id" align="center" prop="id" v-if="false" />
          <el-table-column label="排序" align="center" prop="orderNum" width="60" />
          <el-table-column label="班次名称" align="center" prop="shiftsName" />
          <el-table-column
            label="所属护理单元/科室id"
            align="center"
            prop="deptId"
            v-if="false"
          />
          <el-table-column label="所属护理单元/科室名称" align="center" prop="deptName" />
          <el-table-column label="简称" align="center" prop="shortName" v-if="false" />
          <el-table-column label="描述" align="center" prop="description" v-if="false" />
          <el-table-column label="开始时间" align="center" prop="startTime" width="120">
            <!-- <template #default="scope"> -->
            <!-- <span>{{ parseTime(scope.row.startTime, "{y}-{m}-{d}") }}</span> -->
            <!-- <span>{{ parseTime(scope.row.startTime, "{h}:{i}:{s}") }}</span> -->
            <!-- </template> -->
          </el-table-column>
          <el-table-column label="结束时间" align="center" prop="endTime" width="120">
            <!-- <template #default="scope">
              <span>{{ parseTime(scope.row.endTime, "{h}:{i}:{s}") }}</span>
            </template> -->
          </el-table-column>
          <el-table-column label="时长" align="center" prop="duration" width="100" />
          <el-table-column
            label="前半夜时长"
            align="center"
            prop="halfDuration"
            v-if="false"
          />
          <el-table-column label="系数" align="center" prop="coefficient" v-if="false" />
          <el-table-column label="分值" align="center" prop="score" width="100" />
          <el-table-column label="颜色" align="center" prop="color" v-if="false" />
          <el-table-column label="夜班标识" align="center" prop="nightFlag" v-if="false">
            <template #default="scope">
              <dict-tag :options="sys_yes_no" :value="scope.row.nightFlag" />
            </template>
          </el-table-column>
          <el-table-column
            label="凌晨开始标志"
            align="center"
            prop="earlyStartFlag"
            v-if="false"
          >
            <template #default="scope">
              <dict-tag :options="sys_yes_no" :value="scope.row.earlyStartFlag" />
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="status" v-if="false">
            <template #default="scope">
              <dict-tag :options="sys_yes_no" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column
            label="连7变40标志"
            align="center"
            prop="sevenFourtyFlag"
            v-if="false"
          >
            <template #default="scope">
              <dict-tag :options="sys_yes_no" :value="scope.row.sevenFourtyFlag" />
            </template>
          </el-table-column>
          <el-table-column label="在岗状态" align="center" prop="onLeave" v-if="false">
            <template #default="scope">
              <dict-tag :options="sys_yes_no" :value="scope.row.onLeave" />
            </template>
          </el-table-column>
          <el-table-column
            label="算作白班标志"
            align="center"
            prop="isWhiteFlag"
            v-if="false"
          >
            <template #default="scope">
              <dict-tag :options="sys_yes_no" :value="scope.row.isWhiteFlag" />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button
                link
                type="primary"
                icon="Edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['arr:workShifts:edit']"
                >修改</el-button
              >
              <el-button
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['arr:workShifts:remove']"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改班次管理对话框 -->
    <el-dialog :title="title" v-model="open" width="900px" append-to-body>
      <el-form
        ref="workShiftsRef"
        :model="form"
        :rules="rules"
        label-width="160px"
        inline
      >
        <el-form-item label="班次名称" prop="shiftsName">
          <el-input v-model="form.shiftsName" placeholder="请输入班次名称" />
        </el-form-item>
        <el-form-item label="所属护理单元/科室" prop="deptId" style="width: 41%">
          <!-- <el-input v-model="form.deptId" placeholder="请输入所属护理单元/科室id" /> -->
          <el-tree-select
            v-model="form.deptId"
            :data="deptOptions"
            :props="{ value: 'id', label: 'label', children: 'children' }"
            value-key="id"
            placeholder="请选择护理单元"
            check-strictly
            @node-click="handleClick"
          />
        </el-form-item>
        <el-form-item label="所属护理单元/科室名称" prop="deptName" v-if="false">
          <el-input v-model="form.deptName" placeholder="请输入所属护理单元/科室名称" />
        </el-form-item>
        <el-form-item label="简称" prop="shortName">
          <el-input v-model="form.shortName" placeholder="请输入简称" />
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime" style="width: 41%">
          <el-time-picker
            v-model="form.startTime"
            placeholder="请选择开始时间"
            value-format="HH:mm:ss"
            @change="calculateTime()"
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime" style="width: 41%">
          <el-time-picker
            v-model="form.endTime"
            placeholder="请选择结束时间"
            value-format="HH:mm:ss"
            @change="calculateTime()"
          />
        </el-form-item>
        <el-form-item label="时长" prop="duration">
          <el-input v-model="form.duration" oninput="value=value.replace(/[^0-9.]/g,'').replace(/\.{2,}/g,'.').replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3')" placeholder="请输入时长" />
        </el-form-item>
        <el-form-item label="前半夜时长" prop="halfDuration">
          <el-input v-model="form.halfDuration" oninput="value=value.replace(/[^0-9.]/g,'').replace(/\.{2,}/g,'.').replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3')" placeholder="请输入前半夜时长" />
        </el-form-item>
        <el-form-item label="分值" prop="score">
          <el-input v-model="form.score" oninput="value=value.replace(/[^0-9.]/g,'').replace(/\.{2,}/g,'.').replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3')" placeholder="请输入分值" />
        </el-form-item>
        <el-form-item label="系数" prop="coefficient">
          <el-input v-model="form.coefficient" oninput="value=value.replace(/[^0-9.]/g,'').replace(/\.{2,}/g,'.').replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3')" placeholder="请输入系数" />
        </el-form-item>
        <el-form-item label="排序" prop="orderNum">
          <el-input v-model="form.orderNum" oninput="value=value.replace(/[^\d]/g,'')" placeholder="请输入排序" />
        </el-form-item>
        <el-form-item label="颜色" prop="color" style="width: 41%">
          <el-color-picker v-model="form.color" />
        </el-form-item>
        <el-form-item label="夜班标识" prop="nightFlag" style="width: 41%">
          <el-radio-group v-model="form.nightFlag">
            <el-radio v-for="dict in sys_yes_no" :key="dict.value" :label="dict.value">{{
              dict.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="凌晨开始标志" prop="earlyStartFlag" style="width: 41%">
          <el-radio-group v-model="form.earlyStartFlag">
            <el-radio v-for="dict in sys_yes_no" :key="dict.value" :label="dict.value">{{
              dict.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="算作白班标志" prop="isWhiteFlag" style="width: 41%">
          <el-radio-group v-model="form.isWhiteFlag">
            <el-radio v-for="dict in sys_yes_no" :key="dict.value" :label="dict.value">{{
              dict.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="状态" prop="status" v-if="false">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in sys_yes_no" :key="dict.value" :label="dict.value">{{
              dict.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="连7变40标志" prop="sevenFourtyFlag" v-if="false">
          <el-radio-group v-model="form.sevenFourtyFlag">
            <el-radio v-for="dict in sys_yes_no" :key="dict.value" :label="dict.value">{{
              dict.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="在岗状态" prop="onLeave" v-if="false">
          <el-radio-group v-model="form.onLeave">
            <el-radio v-for="dict in sys_yes_no" :key="dict.value" :label="dict.value">{{
              dict.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="描述" prop="description" style="width: 80%">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="备注" prop="remark" style="width: 80%">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="workShifts">
import {
  listWorkShifts,
  getWorkShifts,
  delWorkShifts,
  addWorkShifts,
  updateWorkShifts,
} from "@/api/arr/workShifts";
import { deptTreeSelect } from "@/api/system/user";

const { proxy } = getCurrentInstance();
const { sys_yes_no } = proxy.useDict("sys_yes_no");

const workShiftsList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    shiftsName: null,
    deptId: null,
    deptName: null,
    shortName: null,
    description: null,
    startTime: null,
    endTime: null,
    duration: null,
    halfDuration: null,
    coefficient: null,
    score: null,
    color: null,
    orderNum: null,
    nightFlag: null,
    earlyStartFlag: null,
    status: null,
    sevenFourtyFlag: null,
    onLeave: null,
    isWhiteFlag: null,
  },
  rules: {
    shiftsName: [{ required: true, message: "班次名称不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);
const deptNameTem = ref("")

/** 根据名称筛选部门树 */
const deptName = ref("");
watch(deptName, (val) => {
  proxy.$refs["deptTreeRef"].filter(val);
});
/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};
/** 查询部门下拉树结构 */
const deptOptions = ref(undefined);
function getDeptTree() {
  deptTreeSelect().then((response) => {
    let obj = {
      id: "",
      label: "全部",
    };
    response.data.unshift(obj);
    deptOptions.value = response.data;
  });
}

/** 获取护理单元选中节点信息 */
function handleClick(node) {
  form.value.deptName = node.label;
}
/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.value.deptId = data.id;
  deptNameTem.value = data.label;
  handleQuery();
}

/** 查询班次管理列表 */
function getList() {
  loading.value = true;
  listWorkShifts(queryParams.value).then((response) => {
    console.log(response);
    workShiftsList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    shiftsName: null,
    deptId: null,
    deptName: null,
    shortName: null,
    description: null,
    startTime: null,
    endTime: null,
    duration: null,
    halfDuration: null,
    coefficient: null,
    score: null,
    color: null,
    orderNum: null,
    nightFlag: null,
    earlyStartFlag: null,
    status: null,
    sevenFourtyFlag: null,
    onLeave: null,
    isWhiteFlag: null,
    createBy: null,
    createId: null,
    createTime: null,
    updateBy: null,
    updateId: null,
    updateTime: null,
    tenantId: null,
    delFlag: null,
    remark: null,
  };
  proxy.resetForm("workShiftsRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加班次管理";
  form.value.deptId = queryParams.value.deptId
  form.value.deptName = deptNameTem.value
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getWorkShifts(_id).then((response) => {
    console.log(response);
    form.value = response.data;
    open.value = true;
    title.value = "修改班次管理";
  });
}

/** 提交按钮 */
function submitForm() {
  form.value.coefficient = form.value.coefficient
    ? parseFloat(form.value.coefficient)
    : form.value.coefficient;
  console.log(form.value);
  proxy.$refs["workShiftsRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateWorkShifts(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addWorkShifts(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除班次管理编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delWorkShifts(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "arr/workShifts/export",
    {
      ...queryParams.value,
    },
    `workShifts_${new Date().getTime()}.xlsx`
  );
}

/* 计算时长 */
function calculateTime(){
  console.log(form.value.startTime);
  console.log(form.value.endTime);
  let stime = '1970-01-01 ' + form.value.startTime;
  let stamp = Date.parse(stime);
  let etime = '1970-01-01 ' + form.value.endTime;
  let etamp = Date.parse(etime);
  console.log(stamp);
  console.log(etamp);
  if ( form.value.startTime && form.value.endTime ) {
    let dateh = ((etamp-stamp) / (1000*60*60)).toFixed(1);
    form.value.duration = dateh>0?dateh:JSON.stringify(+dateh+24);
  }
}

getDeptTree();
getList();
</script>
