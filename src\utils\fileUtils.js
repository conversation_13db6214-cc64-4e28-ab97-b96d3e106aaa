/**
 * 文件处理相关的工具函数
 */

// 获取文件图标的函数
export const getFileIconByExtension = (filename) => {
  if (!filename) return 'mdi-file';

  // 强制确保文件名是字符串类型
  const filenameStr = String(filename);

  // 检查文件名是否包含.pdf（不区分大小写）
  if (filenameStr.toLowerCase().endsWith('.pdf')) {
    return 'mdi-file-pdf-box'; // 使用更明显的PDF图标
  }

  // 获取文件扩展名
  const parts = filenameStr.split('.');
  const extension = parts.length > 1 ? parts.pop().toLowerCase() : '';

  // 根据文件扩展名返回对应的图标
  switch (extension) {
    // 文档类型
    case 'doc':
    case 'docx':
      return 'mdi-file-word';
    case 'xls':
    case 'xlsx':
      return 'mdi-file-excel';
    case 'ppt':
    case 'pptx':
      return 'mdi-file-powerpoint';
    case 'pdf':
      return 'mdi-file-pdf-box'; // 使用更明显的PDF图标

    // 图片类型
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
    case 'svg':
      return 'mdi-file-image';

    // 音频视频类型
    case 'mp3':
    case 'wav':
    case 'ogg':
      return 'mdi-file-music';
    case 'mp4':
    case 'avi':
    case 'mov':
    case 'wmv':
      return 'mdi-file-video';

    // 压缩文件类型
    case 'zip':
    case 'rar':
    case '7z':
    case 'tar':
    case 'gz':
      return 'mdi-zip-box';

    // 代码和文本类型
    case 'txt':
      return 'mdi-file-document';
    case 'html':
    case 'xml':
      return 'mdi-language-html5';
    case 'css':
      return 'mdi-language-css3';
    case 'js':
    case 'json':
      return 'mdi-language-javascript';
    case 'py':
      return 'mdi-language-python';
    case 'java':
      return 'mdi-language-java';
    case 'c':
    case 'cpp':
      return 'mdi-language-cpp';

    // 默认图标
    default:
      return 'mdi-file';
  }
};

// 获取文件图标颜色
export const getFileIconColor = (filename) => {
  if (!filename) return 'grey';

  // 强制确保文件名是字符串类型
  const filenameStr = String(filename);

  // 检查文件名是否包含.pdf（不区分大小写）
  if (filenameStr.toLowerCase().endsWith('.pdf')) {
    return 'red-darken-2'; // PDF使用红色
  }

  // 获取文件扩展名
  const parts = filenameStr.split('.');
  const extension = parts.length > 1 ? parts.pop().toLowerCase() : '';

  // 根据文件扩展名返回对应的颜色
  switch (extension) {
    // 文档类型
    case 'doc':
    case 'docx':
      return 'blue';
    case 'xls':
    case 'xlsx':
      return 'green';
    case 'ppt':
    case 'pptx':
      return 'orange';
    case 'pdf':
      return 'red-darken-2'; // 使用更深的红色使PDF更明显

    // 图片类型
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
    case 'svg':
      return 'purple';

    // 音频视频类型
    case 'mp3':
    case 'wav':
    case 'ogg':
      return 'pink';
    case 'mp4':
    case 'avi':
    case 'mov':
    case 'wmv':
      return 'deep-purple';

    // 压缩文件类型
    case 'zip':
    case 'rar':
    case '7z':
    case 'tar':
    case 'gz':
      return 'amber';

    // 默认颜色
    default:
      return 'grey-darken-1';
  }
};

// 格式化文件大小
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取文件状态文本
export const getStatusText = (status) => {
  switch (status) {
    case 'UPLOADING': return '上传中';
    case 'PARSING': return '向量化处理中';
    case 'PROCESSING': return '向量化处理中'; // 添加处理中状态
    case 'PARSE_SUCCESS': return '解析成功';
    case 'PARSE_FAILED': return '解析失败';
    case 'UPLOAD_TIMEOUT': return '上传超时';
    case 'UPLOAD_ERROR': return '上传失败';
    case 'UNPARSED': return '未解析';
    default: return '未知状态';
  }
};

// 获取状态背景颜色
export const getStatusBgColor = (status) => {
  switch (status) {
    case 'UPLOADING': return '#E3F2FD'; // 浅蓝色背景
    case 'PARSING': return '#FFF8E1'; // 浅黄色背景
    case 'PROCESSING': return '#E8F5E9'; // 浅绿色背景(处理中)
    case 'PARSE_SUCCESS': return '#DCFCE7'; // 浅绿色背景
    case 'PARSE_FAILED': return '#FFEBEE'; // 浅红色背景
    case 'UPLOAD_TIMEOUT': return '#FFEBEE'; // 浅红色背景
    case 'UPLOAD_ERROR': return '#FFEBEE'; // 浅红色背景
    case 'UNPARSED': return '#F3F4F6'; // 浅灰色背景
    default: return '#F5F5F5'; // 浅灰色背景
  }
};

// 获取状态文字颜色
export const getStatusTextColor = (status) => {
  switch (status) {
    case 'UPLOADING': return '#1565C0'; // 蓝色文字
    case 'PARSING': return '#FF8F00'; // 橙色文字
    case 'PROCESSING': return '#2E7D32'; // 绿色文字(处理中)
    case 'PARSE_SUCCESS': return '#166534'; // 绿色文字
    case 'PARSE_FAILED': return '#C62828'; // 红色文字
    case 'UPLOAD_TIMEOUT': return '#C62828'; // 红色文字
    case 'UPLOAD_ERROR': return '#C62828'; // 红色文字
    case 'UNPARSED': return '#6B7280'; // 灰色文字
    default: return '#757575'; // 灰色文字
  }
};

// 获取预览按钮文本
export const getPreviewButtonText = (fileName) => {
  if (!fileName) return '预览';
  
  const extension = fileName.split('.').pop()?.toLowerCase();
  
  switch (extension) {
    case 'pdf':
      return '预览PDF';
    case 'doc':
    case 'docx':
      return '预览Word';
    case 'xls':
    case 'xlsx':
      return '预览Excel';
    case 'ppt':
    case 'pptx':
      return '预览PPT';
    case 'txt':
      return '预览文本';
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return '预览图片';
    default:
      return '预览文件';
  }
};

// 获取预览按钮图标
export const getPreviewButtonIcon = (fileName) => {
  if (!fileName) return 'mdi-eye';
  
  const extension = fileName.split('.').pop()?.toLowerCase();
  
  switch (extension) {
    case 'pdf':
      return 'mdi-file-pdf-box';
    case 'doc':
    case 'docx':
      return 'mdi-file-word';
    case 'xls':
    case 'xlsx':
      return 'mdi-file-excel';
    case 'ppt':
    case 'pptx':
      return 'mdi-file-powerpoint';
    case 'txt':
      return 'mdi-file-document';
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return 'mdi-file-image';
    default:
      return 'mdi-eye';
  }
};

// 获取文件位置
export const getFileLocation = (file) => {
  if (!file) return '';
  
  // 根据文件的权限类型返回位置信息
  if (file.permissionType === 'REPO' || file.operationPermission === 'EDIT') {
    return '部门私有';
  } else {
    return '单位共享';
  }
};
