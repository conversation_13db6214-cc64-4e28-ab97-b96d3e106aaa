<template>
  <div class="history-item" 
    :class="{ 'selected-history': isSelected }"
    @click="$emit('select', history)">
    <div class="history-item-content">
      <v-avatar size="28" :color="history.mode?.value === 'v2' ? 'green-lighten-5' : 'blue-lighten-5'"
        class="history-avatar">
        <v-icon size="small" :color="history.mode?.value === 'v2' ? 'green-darken-1' : 'blue-darken-1'">
          {{ history.mode?.value === 'v2' ? 'mdi-chart-bar' : 'mdi-message-text-outline' }}
        </v-icon>
      </v-avatar>
      <div class="history-text">
        <div class="history-title">{{ title }}</div>
        <div class="history-date">
          <v-icon size="x-small" color="grey-darken-1">mdi-clock-outline</v-icon>
          <span>{{ formattedDate }}</span>
        </div>
      </div>
    </div>

    <div class="history-actions">
      <v-menu location="end">
        <template v-slot:activator="{ props }">
          <v-btn variant="text" icon density="compact" size="small" color="grey-darken-1"
            class="history-menu-btn" v-bind="props" @click.stop>
            <v-icon size="small">mdi-dots-vertical</v-icon>
          </v-btn>
        </template>
        <v-list density="compact" min-width="150">
          <v-list-item @click.stop="openRename">
            <template v-slot:prepend>
              <v-icon size="small" color="grey-darken-1">mdi-pencil</v-icon>
            </template>
            <v-list-item-title>重命名</v-list-item-title>
          </v-list-item>
          <v-list-item @click.stop="$emit('delete', history)">
            <template v-slot:prepend>
              <v-icon size="small" color="error">mdi-delete</v-icon>
            </template>
            <v-list-item-title class="text-error">删除</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </div>
  </div>

  <!-- 重命名对话框 -->
  <v-dialog v-model="showRenameDialog" max-width="400" persistent>
    <v-card>
      <v-card-title class="bg-primary text-white d-flex align-center">
        <span>重命名对话</span>
        <v-spacer></v-spacer>
        <v-btn icon variant="text" @click="closeRename" color="white">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-text class="pt-4">
        <v-text-field
          v-model="newTitle"
          label="请输入新名称"
          variant="outlined"
          density="comfortable"
          autofocus
          @keyup.enter="confirmRename"
          :rules="[v => !!v || '名称不能为空']"
        ></v-text-field>
      </v-card-text>
      <v-card-actions class="pa-4">
        <v-spacer></v-spacer>
        <v-btn variant="text" @click="closeRename">取消</v-btn>
        <v-btn 
          color="primary" 
          @click="confirmRename" 
          :disabled="!newTitle.trim()"
          :loading="isRenaming"
        >
          确认
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  history: {
    type: Object,
    required: true
  },
  isSelected: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['select', 'rename', 'delete']);

// 计算属性
const title = computed(() => {
  return props.history.title.length > 30 
    ? props.history.title.substring(0, 30) + '...' 
    : props.history.title;
});

const formattedDate = computed(() => {
  if (!props.history.timestamp) return '';

  const historyDate = new Date(props.history.timestamp);
  const now = new Date();
  const diffDays = Math.floor((now - historyDate) / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return '今天 ' + props.history.timestamp.split(' ')[1];
  } else if (diffDays === 1) {
    return '昨天 ' + props.history.timestamp.split(' ')[1];
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return props.history.timestamp;
  }
});

// 重命名相关
const showRenameDialog = ref(false);
const newTitle = ref('');
const isRenaming = ref(false);

const openRename = () => {
  newTitle.value = props.history.title;
  showRenameDialog.value = true;
};

const closeRename = () => {
  showRenameDialog.value = false;
  newTitle.value = '';
  isRenaming.value = false;
};

const confirmRename = async () => {
  if (!newTitle.value.trim()) return;
  
  isRenaming.value = true;
  
  try {
    emit('rename', {
      history: props.history,
      newTitle: newTitle.value.trim()
    });
    closeRename();
  } catch (error) {
    console.error('重命名失败:', error);
  } finally {
    isRenaming.value = false;
  }
};
</script>

<style scoped>
.history-item {
  padding: 12px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-left: 3px solid transparent;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  margin: 4px 6px;
  border-radius: 0 12px 12px 0;
  position: relative;
  overflow: hidden;
}

.history-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(120deg, rgba(26, 115, 232, 0.1), rgba(26, 115, 232, 0));
  opacity: 0;
  transition: opacity 0.4s ease;
}

.history-item:hover::before {
  opacity: 1;
}

.history-item:hover {
  background-color: rgba(26, 115, 232, 0.04);
  border-left-color: #1a73e8;
  transform: translateX(4px);
}

.selected-history {
  background-color: rgba(26, 115, 232, 0.08);
  border-left: 3px solid #1a73e8;
  box-shadow: 0 4px 12px rgba(26, 115, 232, 0.1);
}

.history-item-content {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  gap: 14px;
}

.history-text {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.history-title {
  font-size: 13.5px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: color 0.3s ease;
  line-height: 1.4;
}

.history-date {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #757575;
  gap: 5px;
  opacity: 0.85;
  line-height: 1;
}

.history-actions {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.history-item:hover .history-actions {
  opacity: 1;
}

.history-menu-btn {
  margin: -8px;
  transition: transform 0.2s ease;
}

.history-menu-btn:hover {
  transform: scale(1.1);
}

:deep(.v-menu) {
  display: block;
}

:deep(.v-overlay__content) {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

:deep(.v-list) {
  padding: 4px;
  border-radius: 8px;
}

:deep(.v-list-item) {
  border-radius: 6px;
  margin: 2px 0;
}

:deep(.v-list-item:hover) {
  background-color: rgba(0, 0, 0, 0.04);
}
</style> 