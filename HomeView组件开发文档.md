# HomeView 组件开发文档

## 项目概述

HomeView 是一个基于 Vue 3 + Composition API 的知识库管理系统主页面组件，集成了文件管理、智能问答、历史记录等核心功能。该组件采用模块化设计，支持大规模文件管理、实时智能问答和完整的用户权限控制。

## 技术栈

- **前端框架**: Vue 3 (Composition API)
- **UI 组件库**: Vuetify 3 + Element Plus
- **状态管理**: Pinia
- **HTTP 客户端**: Axios
- **样式**: CSS + Scoped Styles
- **文件预览**: 自定义 PDF 预览组件
- **加密解密**: JSEncrypt (RSA)
- **代码高亮**: Highlight.js
- **文件处理**: Blob API + FileReader

## 系统架构设计

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    HomeView 主组件                           │
├─────────────────┬─────────────────┬─────────────────────────┤
│   左侧边栏区域    │    右侧内容区域   │      弹窗组件区域        │
│                │                │                        │
│ ┌─────────────┐ │ ┌─────────────┐ │ ┌─────────────────────┐ │
│ │ 知识库导航   │ │ │ 文件管理区域 │ │ │ 文件上传对话框       │ │
│ │ - 单位知识库 │ │ │ - 文件列表   │ │ │ 文件预览器          │ │
│ │ - 部门知识库 │ │ │ - 批量操作   │ │ │ 成员管理抽屉        │ │
│ │ - 权限控制   │ │ │ - 搜索筛选   │ │ │ 确认对话框          │ │
│ └─────────────┘ │ └─────────────┘ │ └─────────────────────┘ │
│                │                │                        │
│ ┌─────────────┐ │ ┌─────────────┐ │                        │
│ │ 历史记录面板 │ │ │ 智能问答区域 │ │                        │
│ │ - 时间分组   │ │ │ - 对话界面   │ │                        │
│ │ - 对话管理   │ │ │ - 流式响应   │ │                        │
│ │ - 快速加载   │ │ │ - 参考文档   │ │                        │
│ └─────────────┘ │ └─────────────┘ │                        │
└─────────────────┴─────────────────┴─────────────────────────┘
```

### 数据流设计
```
用户操作 → 组件方法 → API调用 → 状态更新 → 视图渲染
    ↓           ↓         ↓         ↓         ↓
  事件触发 → 业务逻辑 → 网络请求 → Store更新 → DOM更新
```

## 设计理念与原则

### 1. 用户体验优先
- **响应式设计**: 支持桌面端、平板、移动端
- **渐进式加载**: 优先加载核心功能，按需加载次要功能
- **即时反馈**: 所有操作都有明确的状态反馈
- **容错设计**: 网络异常、权限不足等场景的友好处理

### 2. 模块化架构
- **高内聚低耦合**: 每个功能模块独立开发和维护
- **组件复用**: 通用组件可在多个场景使用
- **状态隔离**: 不同模块的状态互不干扰
- **接口统一**: 统一的API调用和错误处理

### 3. 性能优化
- **虚拟滚动**: 大列表性能优化
- **懒加载**: 图片和组件按需加载
- **缓存策略**: API响应和计算结果缓存
- **防抖节流**: 用户输入和滚动事件优化

## 核心功能模块详解

### 1. 知识库管理模块

#### 1.1 功能概述
知识库管理是系统的核心入口，负责展示用户有权限访问的知识库列表，支持知识库切换和权限控制。

#### 1.2 设计思路
```
知识库类型分类：
├── 单位知识库（公共）
│   ├── 所有用户可见
│   ├── 文件共享范围：全单位
│   └── 管理权限：系统管理员
└── 部门知识库（私有）
    ├── 部门成员可见
    ├── 文件共享范围：部门内
    └── 管理权限：部门管理员
```

#### 1.3 具体开发功能

**1.3.1 知识库列表获取**
```javascript
// 获取用户有权限的知识库列表
const getLeftList = async () => {
  const params = { pageNum: 1, pageSize: 100 };
  const res = await reposList(params);

  if (res.code === 0) {
    repoList.value = res.data.list;
    // 处理权限标识
    repoList.value.forEach(repo => {
      repo.canEdit = repo.operationPermission === 'EDIT';
      repo.canView = repo.isJoined || repo.operationPermission === 'VIEW';
    });
  }
};
```

**1.3.2 知识库切换逻辑**
```javascript
const selectMenuItem = (group, item, repo = null) => {
  activeMenuGroup.value = group;
  activeMenuItem.value = item;

  if (repo) {
    repoIds.value = repo.repoId;
    currentKnowledgeBasePath.value = repo.repoDesc;
    // 切换知识库时重置文件列表
    resetFileList();
    // 加载新知识库的文件
    loadKnowledgeBaseFiles(repo.repoId);
  }
};
```

**1.3.3 权限控制实现**
```javascript
// 权限检查计算属性
const isHighLevelAdmin = computed(() => {
  return userStore.permissions?.includes('ai:knowledge:member');
});

const isCurrentRepoAdmin = computed(() => {
  if (!activeMenuItem.value?.startsWith('repo_')) return false;
  const currentRepoId = activeMenuItem.value.split('_')[1];
  const currentRepo = repoList.value.find(repo => repo.repoId == currentRepoId);
  return currentRepo?.operationPermission === 'EDIT';
});
```

### 2. 文件管理系统

#### 2.1 功能概述
文件管理系统是知识库的核心功能，支持文件的完整生命周期管理，从上传到删除的全流程操作。

#### 2.2 设计思路
```
文件状态流转：
选择文件 → 上传中 → 解析中 → 解析成功/失败
    ↓         ↓        ↓         ↓
  验证检查   进度显示  状态轮询   操作按钮
```

#### 2.3 具体开发功能

**2.3.1 文件上传系统**
```javascript
// 文件上传核心逻辑
const uploadFiles = async () => {
  for (const file of selectedFiles.value) {
    try {
      // 1. 文件验证
      validateFile(file);

      // 2. 创建上传任务
      const uploadTask = createUploadTask(file);
      fileList.value.push(uploadTask);

      // 3. 执行上传
      const formData = new FormData();
      formData.append('file', file);

      const response = await upFile(formData, (progress) => {
        updateUploadProgress(uploadTask.id, progress);
      }, repoIds.value, filePermissionType.value);

      // 4. 处理上传结果
      handleUploadSuccess(uploadTask, response);

    } catch (error) {
      handleUploadError(uploadTask, error);
    }
  }
};

// 文件验证
const validateFile = (file) => {
  const allowedTypes = ['pdf', 'doc', 'docx', 'txt', 'jpg', 'png'];
  const maxSize = 50 * 1024 * 1024; // 50MB

  const extension = file.name.split('.').pop().toLowerCase();
  if (!allowedTypes.includes(extension)) {
    throw new Error(`不支持的文件类型: ${extension}`);
  }

  if (file.size > maxSize) {
    throw new Error('文件大小超出限制');
  }
};
```

**2.3.2 文件列表管理**
```javascript
// 文件列表加载
const loadKnowledgeBaseFiles = async (repoId) => {
  loading.value = true;
  try {
    const params = {
      repoId: repoId || null,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      searchKey: searchQuery.value
    };

    const response = await getFileList(params);

    if (response.code === 0) {
      // 首次加载替换，后续加载追加（无限滚动）
      if (currentPage.value === 1) {
        fileList.value = response.data.list;
      } else {
        fileList.value.push(...response.data.list);
      }

      totalFiles.value = response.data.total;
      noMoreFiles.value = fileList.value.length >= totalFiles.value;
    }
  } catch (error) {
    handleError('加载文件列表失败', error);
  } finally {
    loading.value = false;
  }
};

// 无限滚动加载更多
const loadMoreFiles = () => {
  if (!loading.value && !noMoreFiles.value) {
    currentPage.value++;
    loadKnowledgeBaseFiles(repoIds.value);
  }
};
```

**2.3.3 文件状态轮询**
```javascript
// 文件状态轮询机制
const startFileStatusPolling = () => {
  if (fileStatusPollingInterval) {
    clearInterval(fileStatusPollingInterval);
  }

  fileStatusPollingInterval = setInterval(async () => {
    if (hasProcessingFiles()) {
      try {
        const response = await getFileList({
          repoId: repoIds.value,
          pageNum: 1,
          pageSize: 50
        });

        if (response.code === 0) {
          updateFileStatuses(response.data.list);
        }
      } catch (error) {
        console.error('轮询文件状态失败:', error);
      }
    } else {
      clearInterval(fileStatusPollingInterval);
      fileStatusPollingInterval = null;
    }
  }, 5000);
};

// 更新文件状态
const updateFileStatuses = (newFiles) => {
  fileList.value.forEach((currentFile, index) => {
    const newFile = newFiles.find(f => f.fileId === currentFile.fileId);
    if (newFile && newFile.status !== currentFile.status) {
      fileList.value[index] = { ...currentFile, ...newFile };

      // 状态变更通知
      if (currentFile.status === 'PARSING' && newFile.status === 'PARSE_SUCCESS') {
        showToast(`文件 ${currentFile.fileName} 处理完成`, 'success');
      }
    }
  });
};
```

**2.3.4 批量操作功能**
```javascript
// 批量选择管理
const selectableFiles = computed(() => {
  return fileList.value.filter(file =>
    file.status === 'PARSE_SUCCESS' ||
    (file.status === 'PARSE_FAILED' && canDeleteFile(file))
  );
});

const toggleSelectAllFiles = () => {
  const shouldSelectAll = selectAllFiles.value;
  selectableFiles.value.forEach(file => {
    file.selected = shouldSelectAll;
  });
};

// 批量删除
const deleteSelectedFiles = async () => {
  const selectedFiles = fileList.value.filter(file =>
    file.selected && canDeleteFile(file)
  );

  if (selectedFiles.length === 0) {
    showToast('没有可删除的文件', 'error');
    return;
  }

  const confirmed = await showConfirmDialog(
    `确认删除选中的 ${selectedFiles.length} 个文件吗？`
  );

  if (confirmed) {
    try {
      const fileIds = selectedFiles.map(file => file.fileId);
      await RemoveRagFile({ ids: fileIds });

      // 从列表中移除已删除的文件
      selectedFiles.forEach(file => {
        const index = fileList.value.findIndex(f => f.fileId === file.fileId);
        if (index !== -1) {
          fileList.value.splice(index, 1);
        }
      });

      showToast(`成功删除 ${selectedFiles.length} 个文件`);
    } catch (error) {
      handleError('批量删除失败', error);
    }
  }
};
```

### 3. 智能问答系统

#### 3.1 功能概述
智能问答系统提供两种模式的AI对话服务，支持流式响应、参考文档展示和历史对话管理。

#### 3.2 设计思路
```
对话流程：
用户输入 → 会话创建 → 消息发送 → 流式响应 → 结果展示
    ↓         ↓         ↓         ↓         ↓
  输入验证   会话管理   API调用   实时更新   参考文档
```

#### 3.3 具体开发功能

**3.3.1 会话管理**
```javascript
// 创建新会话
const createSession = async (clearMessages = true) => {
  try {
    // 获取当前知识库配置
    let kbId = null;
    if (repoIds.value) {
      const selectedRepo = repoList.value.find(repo => repo.repoId == repoIds.value);
      kbId = selectedRepo?.isJoined ? selectedRepo.repoId : selectedRepo.sharedRepoId;
    }

    const requestData = {
      name: "新对话",
      role: "user",
      kbId: kbId ? String(kbId) : null
    };

    // 根据模式选择API端点
    const url = selectedMode.value.value === 'v2'
      ? `${getApiBaseUrl()}/api/v1/agents/${AGENT_ID}/sessions`
      : `${getApiBaseUrl()}/api/v1/agents/${CHAT_ID}/sessions`;

    const response = await axios.post(url, requestData, {
      headers: {
        'Authorization': getApiToken(),
        'Content-Type': 'application/json'
      }
    });

    sessionId.value = response.data.data?.id || '';

    if (clearMessages) {
      messages.value = [createWelcomeMessage(response.data)];
    }

    return true;
  } catch (error) {
    console.error('创建会话失败:', error);
    return false;
  }
};
```

**3.3.2 流式响应处理**
```javascript
// 发送消息并处理流式响应
const sendMessageToAPI = async (question, isSuggestedQuestion = false) => {
  if (!sessionId.value) {
    showToast('会话ID不存在，请重新开始对话', 'error');
    return false;
  }

  try {
    // 添加用户消息
    if (!isSuggestedQuestion) {
      messages.value.push({
        type: 'user',
        content: question,
        typingCompleted: true
      });
    }

    // 添加系统思考消息
    const thinkingIndex = messages.value.length;
    messages.value.push({
      type: 'system',
      content: '',
      thinking: false,
      typingCompleted: false,
      showReferences: false
    });

    // 准备请求数据
    const requestData = {
      question: question,
      stream: true,
      session_id: sessionId.value,
      role: "user",
      kbId: getCurrentKbId()
    };

    // 处理流式响应
    const response = await fetch(getApiUrl(), {
      method: 'POST',
      headers: {
        'Authorization': getApiToken(),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    });

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let accumulatedText = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6));
            handleStreamData(data, thinkingIndex);
          } catch (e) {
            console.warn('解析流式数据失败:', e);
          }
        }
      }
    }

  } catch (error) {
    console.error('发送消息失败:', error);
    handleSendError(error);
  }
};

// 处理流式数据
const handleStreamData = (data, messageIndex) => {
  if (data.answer) {
    // 更新消息内容
    messages.value[messageIndex].content = data.answer;

    // 滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
  }

  if (data.docs) {
    // 更新参考文档
    messages.value[messageIndex].docAggs = data.docs;
    messages.value[messageIndex].showReferences = true;
  }

  if (data.finished) {
    // 标记完成
    messages.value[messageIndex].typingCompleted = true;
    isProcessing.value = false;
  }
};
```

**3.3.3 历史对话管理**
```javascript
// 保存当前对话到历史记录
const saveCurrentConversation = async () => {
  if (messages.value.length <= 1) return;

  try {
    const conversationData = {
      sessionId: sessionId.value,
      messages: messages.value,
      mode: selectedMode.value,
      kbId: repoIds.value,
      createTime: new Date().toISOString()
    };

    await addHistory(conversationData);

    // 刷新历史记录列表
    await loadChatHistory();
  } catch (error) {
    console.error('保存对话历史失败:', error);
  }
};

// 加载历史对话
const loadHistoryConversation = async (history) => {
  try {
    selectedHistoryIndex.value = getHistoryFullIndex(history);

    // 获取历史消息
    const response = await getMessage({
      sessionId: history.sessionId,
      pageNum: 1,
      pageSize: 100
    });

    if (response.code === 0) {
      messages.value = response.data.list.map(msg => ({
        type: msg.role === 'user' ? 'user' : 'system',
        content: msg.content,
        typingCompleted: true,
        docAggs: msg.docAggs || []
      }));

      // 恢复会话状态
      sessionId.value = history.sessionId;
      selectedMode.value = history.mode || selectedMode.value;
    }
  } catch (error) {
    console.error('加载历史对话失败:', error);
    showToast('加载历史对话失败', 'error');
  }
};
```

### 4. 用户权限系统

#### 4.1 权限设计架构
```
权限层级：
├── 系统管理员 (ai:knowledge:member)
│   ├── 管理所有知识库
│   ├── 操作所有文件
│   └── 管理所有用户
├── 知识库管理员 (operationPermission: EDIT)
│   ├── 管理指定知识库
│   ├── 操作知识库内文件
│   └── 管理知识库成员
└── 普通用户 (operationPermission: VIEW)
    ├── 查看有权限的知识库
    ├── 操作自己的文件
    └── 使用智能问答
```

#### 4.2 权限检查实现
```javascript
// 权限检查工具函数
const PermissionChecker = {
  // 检查是否为系统管理员
  isSystemAdmin(user) {
    return user.permissions?.includes('ai:knowledge:member');
  },

  // 检查是否为知识库管理员
  isRepoAdmin(user, repoId) {
    const repo = repoList.value.find(r => r.repoId === repoId);
    return repo?.operationPermission === 'EDIT';
  },

  // 检查文件操作权限
  canOperateFile(user, file, operation) {
    // 系统管理员可以操作所有文件
    if (this.isSystemAdmin(user)) return true;

    // 知识库管理员可以操作知识库内文件
    if (this.isRepoAdmin(user, file.repoId)) return true;

    // 文件所有者可以操作自己的文件
    if (file.userId === user.id) return true;

    // 查看权限：解析成功的文件所有人都可以查看
    if (operation === 'view' && file.status === 'PARSE_SUCCESS') return true;

    return false;
  },

  // 检查上传权限
  canUploadFile(user, repoId) {
    if (this.isSystemAdmin(user)) return true;
    if (this.isRepoAdmin(user, repoId)) return true;

    // 普通用户只能在已加入的知识库上传
    const repo = repoList.value.find(r => r.repoId === repoId);
    return repo?.isJoined;
  }
};
```

## 开发流程指南

### 1. 环境搭建

#### 1.1 项目初始化
```bash
# 创建Vue 3项目
npm create vue@latest knowledge-base-system
cd knowledge-base-system

# 安装依赖
npm install

# 安装UI组件库
npm install vuetify@next @mdi/font
npm install element-plus

# 安装工具库
npm install axios pinia
npm install jsencrypt highlight.js
npm install vue-amazing-ui
```

#### 1.2 项目配置
```javascript
// vite.config.js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vuetify from 'vite-plugin-vuetify'

export default defineConfig({
  plugins: [
    vue(),
    vuetify({ autoImport: true })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  }
})
```

### 2. 组件开发步骤

#### 2.1 创建基础结构
```vue
<!-- src/views/HomeView.vue -->
<template>
  <div class="home-view">
    <!-- 1. 左侧边栏 -->
    <aside class="left-sidebar">
      <!-- 知识库导航 -->
      <KnowledgeBaseNav />
      <!-- 历史记录 -->
      <HistoryPanel />
    </aside>

    <!-- 2. 右侧内容 -->
    <main class="right-content">
      <!-- 文件管理区域 -->
      <FileManagement />
      <!-- 智能问答区域 -->
      <ChatInterface />
    </main>

    <!-- 3. 弹窗组件 -->
    <Teleport to="body">
      <FileUploadDialog />
      <FilePreviewModal />
      <MemberManagementDrawer />
    </Teleport>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useFileStore } from '@/stores/file'
import { useChatStore } from '@/stores/chat'

// 组件导入
import KnowledgeBaseNav from '@/components/KnowledgeBaseNav.vue'
import HistoryPanel from '@/components/HistoryPanel.vue'
import FileManagement from '@/components/FileManagement.vue'
import ChatInterface from '@/components/ChatInterface.vue'

// Store实例
const userStore = useUserStore()
const fileStore = useFileStore()
const chatStore = useChatStore()

// 响应式数据
const loading = ref(false)
const error = ref(null)

// 生命周期
onMounted(async () => {
  await initializeApp()
})

// 初始化应用
const initializeApp = async () => {
  try {
    loading.value = true

    // 1. 检查用户认证
    await userStore.checkAuth()

    // 2. 加载知识库列表
    await fileStore.loadRepositories()

    // 3. 加载历史记录
    await chatStore.loadHistory()

  } catch (err) {
    error.value = err.message
  } finally {
    loading.value = false
  }
}
</script>
```

#### 2.2 状态管理设计
```javascript
// stores/user.js
import { defineStore } from 'pinia'
import { userApi } from '@/api/user'

export const useUserStore = defineStore('user', {
  state: () => ({
    user: null,
    permissions: [],
    isAuthenticated: false,
    loading: false
  }),

  getters: {
    isSystemAdmin: (state) => {
      return state.permissions.includes('ai:knowledge:member')
    },

    canManageRepository: (state) => (repoId) => {
      // 权限检查逻辑
      return state.isSystemAdmin || state.user?.repositories?.some(
        repo => repo.id === repoId && repo.permission === 'EDIT'
      )
    }
  },

  actions: {
    async checkAuth() {
      try {
        this.loading = true
        const response = await userApi.getCurrentUser()

        if (response.code === 0) {
          this.user = response.data
          this.permissions = response.data.permissions || []
          this.isAuthenticated = true
        }
      } catch (error) {
        this.isAuthenticated = false
        throw error
      } finally {
        this.loading = false
      }
    },

    async logout() {
      await userApi.logout()
      this.$reset()
      this.router.push('/login')
    }
  }
})
```

### 3. API接口设计

#### 3.1 接口规范
```javascript
// api/base.js
import axios from 'axios'
import { useUserStore } from '@/stores/user'

// 创建axios实例
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    // 统一处理响应数据
    if (response.data.code === 0) {
      return response.data
    } else {
      throw new Error(response.data.message || '请求失败')
    }
  },
  (error) => {
    // 统一错误处理
    if (error.response?.status === 401) {
      const userStore = useUserStore()
      userStore.logout()
    }
    return Promise.reject(error)
  }
)

export default apiClient
```

#### 3.2 具体API实现
```javascript
// api/file.js
import apiClient from './base'

export const fileApi = {
  // 获取文件列表
  async getFileList(params) {
    return await apiClient.get('/api/file/list', { params })
  },

  // 上传文件
  async uploadFile(formData, onProgress, repoId, permissionType) {
    return await apiClient.post('/api/file/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
      params: { repoId, permissionType },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.lengthComputable) {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          )
          onProgress(progress)
        }
      }
    })
  },

  // 删除文件
  async deleteFiles(fileIds) {
    return await apiClient.delete('/api/file/delete', {
      data: { ids: fileIds }
    })
  },

  // 下载文件
  async downloadFile(fileId) {
    return await apiClient.get(`/api/file/download/${fileId}`, {
      responseType: 'blob'
    })
  },

  // 预览文件
  async previewFile(fileId) {
    return await apiClient.get(`/api/file/preview/${fileId}`, {
      responseType: 'blob'
    })
  },

  // 文件共享设置
  async shareFile(fileId, permissionType) {
    return await apiClient.put(`/api/file/${fileId}/share`, {
      permissionType
    })
  }
}
```

## 组件结构详解

### 1. 模板结构设计
```vue
<template>
  <div class="app-container">
    <!-- 返回顶部按钮 -->
    <BackTop />

    <!-- 主要布局容器 -->
    <div class="main-layout">
      <!-- 左侧边栏 -->
      <aside class="left-sidebar" :class="{ 'open': isLeftSidebarOpen }">
        <!-- 知识库导航菜单 -->
        <nav class="sidebar-menu">
          <KnowledgeBaseMenu
            :repositories="repoList"
            :active-item="activeMenuItem"
            @select="selectMenuItem"
          />
        </nav>

        <!-- 历史记录面板 -->
        <section class="history-sidebar">
          <HistoryPanel
            :history="chatHistory"
            :selected-index="selectedHistoryIndex"
            @load-conversation="loadHistoryConversation"
            @delete-conversation="deleteHistory"
          />
        </section>
      </aside>

      <!-- 右侧内容区域 -->
      <main class="right-content">
        <!-- 移动端菜单按钮 -->
        <button class="mobile-menu-btn" @click="toggleLeftSidebar">
          <v-icon>mdi-menu</v-icon>
        </button>

        <!-- 文件管理区域 -->
        <section class="file-management">
          <FileManagementHeader
            :current-path="currentKnowledgeBasePath"
            :can-upload="canShowUploadButton"
            @upload="openUploadDialog"
            @scroll-to-chat="scrollToChatContainer"
          />

          <FileList
            :files="fileList"
            :loading="loading"
            :selected-files="selectedFiles"
            @file-select="handleFileSelection"
            @file-preview="viewFileInline"
            @file-download="downloadFile"
            @file-delete="deleteFile"
            @file-share="shareFile"
            @load-more="loadMoreFiles"
          />
        </section>

        <!-- 智能问答区域 -->
        <section class="chat-container">
          <ChatInterface
            :messages="messages"
            :is-processing="isProcessing"
            :selected-mode="selectedMode"
            :knowledge-base-options="knowledgeBaseOptions"
            @send-message="sendMessage"
            @mode-change="handleModeChange"
            @new-session="createNewSession"
          />
        </section>
      </main>
    </div>

    <!-- 弹窗组件区域 -->
    <Teleport to="body">
      <!-- 文件上传对话框 -->
      <FileUploadDialog
        v-model="showUploadDialog"
        :permission-type="filePermissionType"
        @upload="uploadFiles"
      />

      <!-- 文件预览器 -->
      <FilePreviewModal
        v-model="showFileViewer"
        :file="currentViewingFile"
        :pdf-source="jsPdf"
        @download="downloadFile"
      />

      <!-- 成员管理抽屉 -->
      <MemberManagementDrawer
        v-model="memberDrawerOpen"
        :repository="currentKnowledgeBase"
        @add-member="addNewMember"
        @remove-member="removeMember"
      />

      <!-- 确认对话框 -->
      <ConfirmDialog
        v-model="showDeleteConfirmDialog"
        title="确认删除"
        message="是否确认删除该历史记录？此操作不可恢复。"
        @confirm="confirmDelete"
      />
    </Teleport>

    <!-- 移动端遮罩层 -->
    <div
      v-if="isLeftSidebarOpen"
      class="sidebar-overlay"
      @click="toggleLeftSidebar"
    ></div>
  </div>
</template>
```

### 2. 脚本逻辑结构
```javascript
<script setup>
// 1. 导入依赖
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useFileStore } from '@/stores/file'
import { useChatStore } from '@/stores/chat'

// 2. Store实例
const userStore = useUserStore()
const fileStore = useFileStore()
const chatStore = useChatStore()
const router = useRouter()

// 3. 响应式数据定义
const state = reactive({
  // 界面状态
  loading: false,
  isLeftSidebarOpen: false,
  showUploadDialog: false,
  showFileViewer: false,

  // 知识库相关
  repoList: [],
  activeMenuGroup: 'unit',
  activeMenuItem: 'all',
  repoIds: '',

  // 文件管理
  fileList: [],
  selectedFiles: [],
  currentPage: 1,
  pageSize: 20,
  totalFiles: 0,

  // 智能问答
  messages: [],
  sessionId: '',
  isProcessing: false,
  selectedMode: { value: 'r1', text: '日常问答' },

  // 历史记录
  chatHistory: {
    recent7Days: [],
    recent30Days: [],
    before30Days: []
  },
  selectedHistoryIndex: -1
})

// 4. 计算属性
const computedProperties = {
  // 权限相关
  isHighLevelAdmin: computed(() =>
    userStore.permissions?.includes('ai:knowledge:member')
  ),

  isCurrentRepoAdmin: computed(() => {
    if (!state.activeMenuItem?.startsWith('repo_')) return false
    const currentRepoId = state.activeMenuItem.split('_')[1]
    const currentRepo = state.repoList.find(repo => repo.repoId == currentRepoId)
    return currentRepo?.operationPermission === 'EDIT'
  }),

  // 文件操作权限
  canShowUploadButton: computed(() => {
    if (state.activeMenuGroup === 'unit' && state.activeMenuItem === 'all') {
      return false
    }
    return computedProperties.isHighLevelAdmin.value ||
           computedProperties.isCurrentRepoAdmin.value ||
           isJoinedCurrentRepo.value
  }),

  // 可选择的文件
  selectableFiles: computed(() =>
    state.fileList.filter(file =>
      file.status === 'PARSE_SUCCESS' ||
      (file.status === 'PARSE_FAILED' && canDeleteFile(file))
    )
  ),

  // 是否有选中文件
  hasSelectedFiles: computed(() =>
    state.fileList.some(file => file.selected)
  )
}

// 5. 生命周期钩子
onMounted(async () => {
  await initializeComponent()
})

onUnmounted(() => {
  cleanup()
})

// 6. 方法定义
const methods = {
  // 初始化组件
  async initializeComponent() {
    try {
      state.loading = true

      // 检查用户认证
      await userStore.checkAuth()

      // 加载知识库列表
      await loadRepositoryList()

      // 加载历史记录
      await loadChatHistory()

      // 创建初始会话
      await createSession()

    } catch (error) {
      handleError('初始化失败', error)
    } finally {
      state.loading = false
    }
  },

  // 清理资源
  cleanup() {
    // 清除定时器
    if (fileStatusPollingInterval) {
      clearInterval(fileStatusPollingInterval)
    }

    // 清除事件监听
    window.removeEventListener('scroll', handleScroll)
    window.removeEventListener('beforeunload', handleBeforeUnload)
  }
}

// 7. 事件监听
watch(() => state.activeMenuItem, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    handleMenuItemChange(newValue)
  }
})

watch(() => state.selectedMode, (newMode) => {
  handleModeChange(newMode)
})
</script>
```

### 3. 样式结构设计
```css
<style scoped>
/* 1. 全局变量定义 */
:root {
  --primary-color: #1677ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --text-color: #262626;
  --border-color: rgba(0, 0, 0, 0.08);
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 2. 布局样式 */
.app-container {
  display: flex;
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
}

.left-sidebar {
  position: relative;
  width: 240px;
  background-color: #fff;
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
}

.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: -34px;
}

/* 3. 组件样式 */
.sidebar-menu {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.file-management {
  flex: 1;
  padding: 24px;
  min-height: 600px;
}

.chat-container {
  margin: 24px;
  border-radius: 8px;
  box-shadow: var(--shadow);
}

/* 4. 响应式设计 */
@media (max-width: 768px) {
  .left-sidebar {
    position: fixed;
    left: -240px;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transition: left 0.3s ease;
  }

  .left-sidebar.open {
    left: 0;
  }

  .right-content {
    margin-left: 0;
  }

  .mobile-menu-btn {
    display: block;
  }
}

@media (min-width: 769px) {
  .mobile-menu-btn {
    display: none;
  }

  .sidebar-overlay {
    display: none;
  }
}
</style>
```

## 主要数据结构详解

### 1. 文件对象结构
```typescript
interface FileItem {
  fileId: string;           // 文件唯一标识
  fileName: string;         // 文件名称
  fileSize: number;         // 文件大小（字节）
  fileType?: string;        // 文件类型
  filePath?: string;        // 文件路径
  fileMd5?: string;         // 文件MD5值

  // 状态相关
  status: FileStatus;       // 文件状态
  progress: number;         // 上传/处理进度 (0-100)

  // 权限相关
  permissionType: 'ALL' | 'REPO'; // 权限类型：公共/私有
  operationPermission?: 'EDIT' | 'VIEW'; // 操作权限

  // 用户信息
  userId: string;           // 上传用户ID
  createBy: string;         // 创建者名称
  createTime: string;       // 创建时间
  updateTime?: string;      // 更新时间

  // 内容相关
  documentSummary?: string; // 文档摘要
  keywords?: string;        // 关键词标签
  structuredData?: string;  // 结构化数据

  // UI状态
  selected: boolean;        // 是否选中
  originalFile?: File;      // 原始文件对象（上传时）

  // 知识库相关
  repoId?: string;         // 所属知识库ID
  deptId?: string;         // 所属部门ID
  section?: string;        // 切片方式
}

// 文件状态枚举
type FileStatus =
  | 'UPLOADING'      // 上传中
  | 'PARSING'        // 解析中
  | 'PROCESSING'     // 处理中
  | 'PARSE_SUCCESS'  // 解析成功
  | 'PARSE_FAILED'   // 解析失败
  | 'UPLOAD_ERROR'   // 上传失败
  | 'UPLOAD_TIMEOUT' // 上传超时
  | 'UNPARSED';      // 未解析
```

### 2. 消息对象结构
```typescript
interface ChatMessage {
  type: 'user' | 'system';     // 消息类型
  content: string;             // 消息内容

  // 状态控制
  thinking?: boolean;          // 是否为思考过程
  typingCompleted: boolean;    // 打字完成状态
  responseCompleted?: boolean; // 响应完成状态
  welcome?: boolean;           // 是否为欢迎消息

  // 扩展内容
  docAggs?: DocumentReference[]; // 参考文档
  suggestedQuestions?: string[]; // 建议问题
  table?: boolean;             // 是否包含表格

  // 时间信息
  timestamp?: number;          // 消息时间戳
  thinkingTime?: number;       // 思考开始时间

  // 显示控制
  showReferences?: boolean;    // 是否显示参考文档
  preserveThinking?: boolean;  // 是否保留思考过程
}

// 参考文档结构
interface DocumentReference {
  doc_id: string;             // 文档ID
  doc_name: string;           // 文档名称
  fileId?: string;            // 文件ID
  fileName?: string;          // 文件名称
  relevance?: number;         // 相关度分数
  snippet?: string;           // 文档片段
}
```

### 3. 知识库对象结构
```typescript
interface Repository {
  repoId: string;             // 知识库ID
  repoDesc: string;           // 知识库描述/名称
  remark?: string;            // 备注信息（用作datasetId）

  // 权限相关
  isJoined: boolean;          // 是否已加入
  operationPermission: 'EDIT' | 'VIEW'; // 操作权限

  // 共享相关
  sharedRepoId?: string;      // 共享知识库ID
  permissionType?: 'ALL' | 'REPO'; // 权限类型

  // 时间信息
  createTime: string;         // 创建时间
  updateTime?: string;        // 更新时间
  createBy?: string;          // 创建者
  updateBy?: string;          // 更新者

  // 统计信息
  fileCount?: number;         // 文件数量
  memberCount?: number;       // 成员数量
}
```

### 4. 用户对象结构
```typescript
interface User {
  id: string;                 // 用户ID
  userId: string;             // 用户账号
  userName: string;           // 用户名
  nickName: string;           // 昵称
  name: string;               // 姓名

  // 联系信息
  email?: string;             // 邮箱
  phonenumber?: string;       // 手机号

  // 组织信息
  deptId?: string;            // 部门ID
  deptName?: string;          // 部门名称

  // 权限信息
  permissions: string[];      // 权限列表
  roles?: string[];           // 角色列表

  // 状态信息
  status: '0' | '1';          // 用户状态：0正常，1停用

  // API相关
  apiKey?: string;            // 加密的API密钥
  apiUrl?: string;            // 加密的API地址
  publicKey?: string;         // RSA公钥
  decryptedApiKey?: string;   // 解密后的API密钥
  decryptedApiUrl?: string;   // 解密后的API地址
  publicBucketName?: string;  // 公共存储桶名称

  // 知识库相关
  repositories?: Repository[]; // 用户的知识库列表
  knowledgeBaseNames?: string; // 已加入的知识库名称
}
```

### 5. 会话对象结构
```typescript
interface ChatSession {
  sessionId: string;          // 会话ID
  name: string;               // 会话名称

  // 模式信息
  mode: {
    value: 'r1' | 'v2';       // 模式值
    text: string;             // 模式显示名称
    icon: string;             // 模式图标
  };

  // 知识库信息
  kbId?: string;              // 知识库ID
  repoId?: string;            // 仓库ID

  // 时间信息
  createTime: string;         // 创建时间
  lastMessageTime?: string;   // 最后消息时间

  // 消息相关
  messages: ChatMessage[];    // 消息列表
  messageCount?: number;      // 消息数量

  // 状态信息
  isActive: boolean;          // 是否为当前活跃会话
}
```

## 核心方法详解

### 1. 文件管理核心方法

#### 1.1 文件上传方法 `uploadFiles()`
```javascript
/**
 * 文件上传核心方法
 * 支持多文件上传、进度监控、错误处理
 */
const uploadFiles = async () => {
  // 验证上传权限
  if (!canShowUploadButton.value) {
    showToast('您没有上传文件的权限', 'error');
    return;
  }

  // 验证文件选择
  if (selectedFiles.value.length === 0) {
    showToast('请先选择要上传的文件', 'error');
    return;
  }

  try {
    // 批量处理文件上传
    for (const file of selectedFiles.value) {
      await uploadSingleFile(file);
    }

    // 清空选择的文件
    selectedFiles.value = [];
    showUploadDialog.value = false;

    // 刷新文件列表
    await loadKnowledgeBaseFiles(repoIds.value);

  } catch (error) {
    console.error('批量上传失败:', error);
    showToast('部分文件上传失败', 'error');
  }
};

/**
 * 单文件上传处理
 */
const uploadSingleFile = async (file) => {
  // 1. 文件验证
  validateFile(file);

  // 2. 创建上传任务
  const uploadTask = {
    id: generateId(),
    fileName: file.name,
    fileSize: file.size,
    status: 'UPLOADING',
    progress: 0,
    originalFile: file,
    permissionType: filePermissionType.value
  };

  // 3. 添加到文件列表
  fileList.value.unshift(uploadTask);

  try {
    // 4. 准备上传数据
    const formData = new FormData();
    formData.append('file', file);

    // 5. 执行上传
    const response = await upFile(formData, (progress) => {
      updateUploadProgress(uploadTask.id, progress);
    }, repoIds.value, filePermissionType.value);

    // 6. 处理上传结果
    if (response.code === 0) {
      await handleUploadSuccess(uploadTask, response);
    } else {
      throw new Error(response.msg || '上传失败');
    }

  } catch (error) {
    handleUploadError(uploadTask, error);
  }
};

/**
 * 上传进度更新
 */
const updateUploadProgress = (taskId, progress) => {
  const fileIndex = fileList.value.findIndex(f => f.id === taskId);
  if (fileIndex !== -1) {
    // 限制进度在70%以内，为后续处理预留空间
    const displayProgress = Math.min(Math.round(progress * 0.7), 70);
    fileList.value[fileIndex].progress = displayProgress;
  }
};
```

#### 1.2 文件删除方法 `deleteFile(file)`
```javascript
/**
 * 删除文件方法
 * 包含权限检查、确认对话框、API调用
 */
const deleteFile = async (file) => {
  // 1. 特殊处理：上传中的文件直接取消
  if (file.status === 'UPLOADING') {
    cancelUpload(file);
    return;
  }

  // 2. 权限检查
  if (!canDeleteFile(file)) {
    showToast('您没有删除此文件的权限', 'error');
    return;
  }

  // 3. 确认对话框
  const confirmed = await showConfirmDialog(
    `确认删除文件 "${file.fileName}" 吗？`,
    '此操作不可恢复'
  );

  if (!confirmed) return;

  try {
    // 4. 调用删除API
    const response = await RemoveRagFile({
      ids: [file.fileId]
    });

    if (response.code === 0) {
      // 5. 从列表中移除
      const index = fileList.value.findIndex(f => f.fileId === file.fileId);
      if (index !== -1) {
        fileList.value.splice(index, 1);
      }

      showToast('文件删除成功');

      // 6. 刷新文件列表
      setTimeout(() => {
        loadKnowledgeBaseFiles(repoIds.value);
      }, 300);

    } else {
      throw new Error(response.msg || '删除失败');
    }

  } catch (error) {
    console.error('删除文件失败:', error);
    showToast('删除文件失败，请重试', 'error');
  }
};

/**
 * 权限检查：是否可以删除文件
 */
const canDeleteFile = (file) => {
  // 系统管理员可以删除所有文件
  if (isHighLevelAdmin.value) return true;

  // 知识库管理员可以删除知识库内文件
  if (isCurrentRepoAdmin.value && isJoinedCurrentRepo.value) return true;

  // 文件所有者可以删除自己的文件
  return file.userId === userStore.id;
};
```

#### 1.3 文件共享方法 `shareFile(file)`
```javascript
/**
 * 文件共享状态切换
 * 支持私有/公共状态切换
 */
const shareFile = async (file) => {
  // 1. 权限检查
  const hasPermission = isHighLevelAdmin.value ||
                       (isCurrentRepoAdmin.value && isJoinedCurrentRepo.value) ||
                       file.userId === userStore.id;

  if (!hasPermission) {
    showToast('您没有更改此文件共享状态的权限', 'error');
    return;
  }

  // 2. 确定操作类型
  const currentPermission = file.permissionType;
  const newPermission = currentPermission === 'ALL' ? 'REPO' : 'ALL';
  const actionText = currentPermission === 'ALL' ? '取消共享' : '设为共享';

  // 3. 确认对话框
  const message = currentPermission === 'ALL'
    ? '确定要取消共享此文件吗？取消后其他用户将无法查看此文件。'
    : '确定要将此文件设为共享吗？设置后所有用户都可以查看此文件。';

  const confirmed = await showConfirmDialog(message);
  if (!confirmed) return;

  try {
    // 4. 获取数据集ID
    let datasetId = userStore.publicBucketName;
    if (repoIds.value) {
      const selectedRepo = repoList.value.find(repo => repo.repoId == repoIds.value);
      if (selectedRepo?.remark) {
        datasetId = selectedRepo.remark;
      }
    }

    // 5. 调用共享API
    const params = {
      datasetId: datasetId,
      docId: file.fileId,
      permissionType: newPermission,
      repoId: file.repoId,
      fileName: file.fileName
    };

    await docMove(params);
    showToast(`${actionText}成功`);

    // 6. 刷新文件列表
    await loadKnowledgeBaseFiles(repoIds.value);

  } catch (error) {
    console.error('共享操作失败:', error);
    showToast(`${actionText}失败`, 'error');
  }
};
```

### 2. 智能问答核心方法

#### 2.1 消息发送方法 `sendMessage()`
```javascript
/**
 * 发送用户消息
 * 统一的消息发送入口
 */
const sendMessage = async () => {
  const message = userMessage.value.trim();

  // 1. 输入验证
  if (!message) {
    showToast('请输入消息内容', 'error');
    return;
  }

  if (isProcessing.value) {
    showToast('正在处理中，请稍候', 'warning');
    return;
  }

  try {
    // 2. 设置处理状态
    isProcessing.value = true;

    // 3. 检查会话状态
    if (!sessionId.value) {
      await createSession(false);
    }

    // 4. 发送消息到API
    await sendMessageToAPI(message);

    // 5. 清空输入框
    userMessage.value = '';

  } catch (error) {
    console.error('发送消息失败:', error);
    showToast('发送消息失败，请重试', 'error');
  } finally {
    isProcessing.value = false;
  }
};

/**
 * 建议问题快速发送
 */
const askSuggestedQuestion = async (question) => {
  if (isProcessing.value) return;

  try {
    isProcessing.value = true;

    // 添加用户消息到对话
    messages.value.push({
      type: 'user',
      content: question,
      typingCompleted: true
    });

    // 发送到API
    await sendMessageToAPI(question, true);

  } catch (error) {
    console.error('发送建议问题失败:', error);
    showToast('发送失败，请重试', 'error');
  } finally {
    isProcessing.value = false;
  }
};
```

#### 2.2 流式响应处理 `sendMessageToAPI(question, isSuggestedQuestion)`
```javascript
/**
 * 流式响应处理核心方法
 * 支持实时更新、思考过程显示、参考文档
 */
const sendMessageToAPI = async (question, isSuggestedQuestion = false) => {
  // 1. 会话检查
  if (!sessionId.value) {
    throw new Error('会话ID不存在');
  }

  // 2. 重置参考数据
  if (!isSuggestedQuestion) {
    latestReferenceChunks.value = [];
    latestDocAggs.value = [];
  }

  try {
    // 3. 添加思考状态消息
    const thinkingIndex = messages.value.length;
    messages.value.push({
      type: 'system',
      content: '',
      thinking: false,
      typingCompleted: false,
      showReferences: false,
      thinkingTime: Date.now()
    });

    // 4. 准备请求数据
    const requestData = {
      question: question,
      stream: true,
      session_id: sessionId.value,
      role: "user",
      kbId: getCurrentKbId()
    };

    // 5. 创建流式请求
    const controller = new AbortController();
    activeController.value = controller;

    const response = await fetch(getApiUrl(), {
      method: 'POST',
      headers: {
        'Authorization': getApiToken(),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData),
      signal: controller.signal
    });

    // 6. 处理流式响应
    await handleStreamResponse(response, thinkingIndex);

  } catch (error) {
    if (error.name === 'AbortError') {
      console.log('请求被用户取消');
    } else {
      throw error;
    }
  }
};

/**
 * 处理流式响应数据
 */
const handleStreamResponse = async (response, messageIndex) => {
  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  let accumulatedText = '';

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6));
            await processStreamData(data, messageIndex);
          } catch (parseError) {
            console.warn('解析流式数据失败:', parseError);
          }
        }
      }
    }
  } finally {
    reader.releaseLock();
  }
};

/**
 * 处理单条流式数据
 */
const processStreamData = async (data, messageIndex) => {
  const message = messages.value[messageIndex];
  if (!message) return;

  // 更新消息内容
  if (data.answer) {
    message.content = data.answer;

    // 滚动到底部
    await nextTick();
    scrollToBottom();
  }

  // 更新参考文档
  if (data.docs && data.docs.length > 0) {
    message.docAggs = data.docs;
    message.showReferences = true;
    latestDocAggs.value = data.docs;
  }

  // 标记完成
  if (data.finished) {
    message.typingCompleted = true;
    isProcessing.value = false;

    // 保存到历史记录
    if (messages.value.length > 1) {
      await saveCurrentConversation();
    }
  }
};
```

### 3. 历史记录管理方法

#### 3.1 历史记录加载 `loadChatHistory()`
```javascript
/**
 * 加载聊天历史记录
 * 按时间分组显示
 */
const loadChatHistory = async () => {
  try {
    const response = await getMessage({
      pageNum: 1,
      pageSize: 100,
      orderBy: 'createTime',
      orderDirection: 'desc'
    });

    if (response.code === 0) {
      // 按时间分组
      const groupedHistory = groupHistoryByTime(response.data.list);
      chatHistory.value = groupedHistory;
    }
  } catch (error) {
    console.error('加载历史记录失败:', error);
  }
};

/**
 * 按时间分组历史记录
 */
const groupHistoryByTime = (historyList) => {
  const now = new Date();
  const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  return {
    recent7Days: historyList.filter(item =>
      new Date(item.lastMessageTime || item.createTime) >= sevenDaysAgo
    ),
    recent30Days: historyList.filter(item => {
      const date = new Date(item.lastMessageTime || item.createTime);
      return date < sevenDaysAgo && date >= thirtyDaysAgo;
    }),
    before30Days: historyList.filter(item =>
      new Date(item.lastMessageTime || item.createTime) < thirtyDaysAgo
    )
  };
};
```

#### 3.2 历史对话恢复 `loadHistoryConversation(history)`
```javascript
/**
 * 加载历史对话
 * 恢复对话状态和消息列表
 */
const loadHistoryConversation = async (history) => {
  try {
    // 1. 设置选中状态
    selectedHistoryIndex.value = getHistoryFullIndex(history);

    // 2. 获取对话消息
    const response = await getMessage({
      sessionId: history.sessionId,
      pageNum: 1,
      pageSize: 100
    });

    if (response.code === 0) {
      // 3. 转换消息格式
      messages.value = response.data.list.map(msg => ({
        type: msg.role === 'user' ? 'user' : 'system',
        content: msg.content,
        typingCompleted: true,
        docAggs: msg.docAggs || [],
        showReferences: msg.docAggs && msg.docAggs.length > 0
      }));

      // 4. 恢复会话状态
      sessionId.value = history.sessionId;
      selectedMode.value = history.mode || selectedMode.value;

      // 5. 滚动到底部
      await nextTick();
      scrollToBottom();

      showToast('历史对话加载成功');
    }
  } catch (error) {
    console.error('加载历史对话失败:', error);
    showToast('加载历史对话失败', 'error');
  }
};
```

## 模块化开发指南

### 1. 组件拆分策略

#### 1.1 按功能拆分
```
HomeView (主组件)
├── KnowledgeBaseNav (知识库导航)
│   ├── RepoMenuItem (知识库菜单项)
│   └── PermissionBadge (权限标识)
├── FileManagement (文件管理)
│   ├── FileList (文件列表)
│   ├── FileItem (文件项)
│   ├── FileUploadDialog (上传对话框)
│   └── FilePreviewModal (预览模态框)
├── ChatInterface (聊天界面)
│   ├── MessageList (消息列表)
│   ├── MessageItem (消息项)
│   ├── InputArea (输入区域)
│   └── SuggestedQuestions (建议问题)
└── HistoryPanel (历史面板)
    ├── HistoryGroup (历史分组)
    └── HistoryItem (历史项)
```

#### 1.2 组件开发模板
```vue
<!-- components/FileItem.vue -->
<template>
  <div class="file-item" :class="fileItemClasses">
    <!-- 文件图标 -->
    <FileIcon :file-name="file.fileName" :size="iconSize" />

    <!-- 文件信息 -->
    <div class="file-info">
      <div class="file-name" @click="handleFileClick">
        {{ file.fileName }}
        <StatusBadge :status="file.status" />
      </div>

      <div class="file-meta">
        <span>{{ formatFileSize(file.fileSize) }}</span>
        <span>{{ formatDate(file.createTime) }}</span>
        <span>{{ file.createBy }}</span>
      </div>

      <!-- 文件描述 -->
      <div class="file-description">
        <Ellipsis :line="2" :content="file.documentSummary" />
      </div>

      <!-- 文件标签 -->
      <div class="file-tags" v-if="file.keywords">
        <Tag v-for="tag in fileTags" :key="tag">{{ tag }}</Tag>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="file-actions">
      <ActionButton
        v-for="action in availableActions"
        :key="action.name"
        :icon="action.icon"
        :tooltip="action.tooltip"
        @click="handleAction(action.name)"
      />
    </div>

    <!-- 选择框 -->
    <Checkbox
      v-if="selectable"
      v-model="file.selected"
      @change="$emit('selection-change', file)"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useFileActions } from '@/composables/useFileActions'
import { usePermissions } from '@/composables/usePermissions'

// Props定义
const props = defineProps({
  file: {
    type: Object,
    required: true
  },
  selectable: {
    type: Boolean,
    default: false
  },
  iconSize: {
    type: String,
    default: 'medium'
  }
})

// Emits定义
const emit = defineEmits([
  'file-click',
  'action-click',
  'selection-change'
])

// Composables
const { getAvailableActions } = useFileActions()
const { canOperateFile } = usePermissions()

// 计算属性
const fileItemClasses = computed(() => ({
  'file-item--selected': props.file.selected,
  'file-item--processing': ['UPLOADING', 'PARSING'].includes(props.file.status),
  'file-item--error': ['UPLOAD_ERROR', 'PARSE_FAILED'].includes(props.file.status)
}))

const fileTags = computed(() => {
  if (!props.file.keywords) return []
  return typeof props.file.keywords === 'string'
    ? props.file.keywords.split(',').slice(0, 5)
    : props.file.keywords
})

const availableActions = computed(() => {
  return getAvailableActions(props.file).filter(action =>
    canOperateFile(props.file, action.permission)
  )
})

// 方法
const handleFileClick = () => {
  if (props.file.status === 'PARSE_SUCCESS') {
    emit('file-click', props.file)
  }
}

const handleAction = (actionName) => {
  emit('action-click', { action: actionName, file: props.file })
}
</script>
```

### 2. Composables设计

#### 2.1 文件操作Composable
```javascript
// composables/useFileActions.js
import { computed } from 'vue'
import { useUserStore } from '@/stores/user'

export function useFileActions() {
  const userStore = useUserStore()

  // 获取可用操作
  const getAvailableActions = (file) => {
    const actions = []

    // 基础操作
    if (file.status === 'PARSE_SUCCESS') {
      actions.push(
        { name: 'preview', icon: 'mdi-eye', tooltip: '预览', permission: 'view' },
        { name: 'download', icon: 'mdi-download', tooltip: '下载', permission: 'view' }
      )
    }

    // 管理操作
    if (canManageFile(file)) {
      actions.push(
        { name: 'share', icon: getShareIcon(file), tooltip: getShareTooltip(file), permission: 'edit' },
        { name: 'delete', icon: 'mdi-delete', tooltip: '删除', permission: 'delete' }
      )
    }

    // 状态相关操作
    if (file.status === 'PARSE_FAILED' && canManageFile(file)) {
      actions.push(
        { name: 'reprocess', icon: 'mdi-refresh', tooltip: '重新处理', permission: 'edit' }
      )
    }

    if (file.status === 'UNPARSED' && canManageFile(file)) {
      actions.push(
        { name: 'parse', icon: 'mdi-vector-triangle', tooltip: '向量解析', permission: 'edit' },
        { name: 'slice', icon: 'mdi-file-document', tooltip: '文档切片', permission: 'edit' }
      )
    }

    return actions
  }

  // 权限检查
  const canManageFile = (file) => {
    // 系统管理员
    if (userStore.isSystemAdmin) return true

    // 知识库管理员
    if (userStore.isRepoAdmin(file.repoId)) return true

    // 文件所有者
    return file.userId === userStore.user?.id
  }

  const getShareIcon = (file) => {
    return file.permissionType === 'ALL' ? 'mdi-link-off' : 'mdi-share-variant'
  }

  const getShareTooltip = (file) => {
    return file.permissionType === 'ALL' ? '取消共享' : '设为共享'
  }

  return {
    getAvailableActions,
    canManageFile
  }
}
```

#### 2.2 权限管理Composable
```javascript
// composables/usePermissions.js
import { computed } from 'vue'
import { useUserStore } from '@/stores/user'

export function usePermissions() {
  const userStore = useUserStore()

  // 系统级权限
  const isSystemAdmin = computed(() =>
    userStore.permissions?.includes('ai:knowledge:member')
  )

  // 知识库权限
  const isRepoAdmin = (repoId) => {
    const repo = userStore.repositories?.find(r => r.repoId === repoId)
    return repo?.operationPermission === 'EDIT'
  }

  const isRepoMember = (repoId) => {
    const repo = userStore.repositories?.find(r => r.repoId === repoId)
    return repo?.isJoined
  }

  // 文件操作权限
  const canOperateFile = (file, operation) => {
    switch (operation) {
      case 'view':
        return file.status === 'PARSE_SUCCESS'

      case 'download':
        return file.status === 'PARSE_SUCCESS'

      case 'edit':
      case 'delete':
        return isSystemAdmin.value ||
               isRepoAdmin(file.repoId) ||
               file.userId === userStore.user?.id

      case 'share':
        return (isSystemAdmin.value ||
                isRepoAdmin(file.repoId) ||
                file.userId === userStore.user?.id) &&
               file.status === 'PARSE_SUCCESS'

      default:
        return false
    }
  }

  // 上传权限
  const canUploadToRepo = (repoId) => {
    return isSystemAdmin.value ||
           isRepoAdmin(repoId) ||
           isRepoMember(repoId)
  }

  return {
    isSystemAdmin,
    isRepoAdmin,
    isRepoMember,
    canOperateFile,
    canUploadToRepo
  }
}
```

### 3. 状态管理设计

#### 3.1 文件Store
```javascript
// stores/file.js
import { defineStore } from 'pinia'
import { fileApi } from '@/api/file'

export const useFileStore = defineStore('file', {
  state: () => ({
    // 文件列表
    files: [],
    totalFiles: 0,
    currentPage: 1,
    pageSize: 20,

    // 加载状态
    loading: false,
    uploading: false,

    // 选择状态
    selectedFiles: [],
    selectAll: false,

    // 筛选条件
    searchQuery: '',
    statusFilter: 'all',
    typeFilter: 'all',

    // 当前知识库
    currentRepoId: null,

    // 错误状态
    error: null
  }),

  getters: {
    // 可选择的文件
    selectableFiles: (state) => {
      return state.files.filter(file =>
        ['PARSE_SUCCESS', 'PARSE_FAILED', 'UNPARSED'].includes(file.status)
      )
    },

    // 选中文件数量
    selectedCount: (state) => {
      return state.files.filter(file => file.selected).length
    },

    // 是否有选中文件
    hasSelectedFiles: (state) => {
      return state.files.some(file => file.selected)
    },

    // 筛选后的文件
    filteredFiles: (state) => {
      let filtered = state.files

      // 搜索筛选
      if (state.searchQuery) {
        filtered = filtered.filter(file =>
          file.fileName.toLowerCase().includes(state.searchQuery.toLowerCase())
        )
      }

      // 状态筛选
      if (state.statusFilter !== 'all') {
        filtered = filtered.filter(file => file.status === state.statusFilter)
      }

      // 类型筛选
      if (state.typeFilter !== 'all') {
        filtered = filtered.filter(file => {
          const ext = file.fileName.split('.').pop().toLowerCase()
          return getFileType(ext) === state.typeFilter
        })
      }

      return filtered
    }
  },

  actions: {
    // 加载文件列表
    async loadFiles(repoId, options = {}) {
      try {
        this.loading = true
        this.error = null

        const params = {
          repoId: repoId || null,
          pageNum: options.page || this.currentPage,
          pageSize: options.pageSize || this.pageSize,
          searchKey: this.searchQuery || undefined
        }

        const response = await fileApi.getFileList(params)

        if (options.append) {
          this.files.push(...response.data.list)
        } else {
          this.files = response.data.list
        }

        this.totalFiles = response.data.total
        this.currentRepoId = repoId

      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },

    // 上传文件
    async uploadFile(file, options = {}) {
      try {
        this.uploading = true

        // 创建上传任务
        const uploadTask = {
          id: generateId(),
          fileName: file.name,
          fileSize: file.size,
          status: 'UPLOADING',
          progress: 0,
          originalFile: file
        }

        // 添加到列表
        this.files.unshift(uploadTask)

        // 执行上传
        const formData = new FormData()
        formData.append('file', file)

        const response = await fileApi.uploadFile(
          formData,
          (progress) => this.updateUploadProgress(uploadTask.id, progress),
          options.repoId,
          options.permissionType
        )

        // 更新上传结果
        this.updateUploadResult(uploadTask.id, response)

        return response

      } catch (error) {
        this.handleUploadError(uploadTask.id, error)
        throw error
      } finally {
        this.uploading = false
      }
    },

    // 删除文件
    async deleteFiles(fileIds) {
      try {
        await fileApi.deleteFiles(fileIds)

        // 从列表中移除
        this.files = this.files.filter(file =>
          !fileIds.includes(file.fileId)
        )

      } catch (error) {
        throw error
      }
    },

    // 更新上传进度
    updateUploadProgress(taskId, progress) {
      const file = this.files.find(f => f.id === taskId)
      if (file) {
        file.progress = Math.min(progress * 0.7, 70)
      }
    },

    // 切换文件选择
    toggleFileSelection(fileId) {
      const file = this.files.find(f => f.fileId === fileId)
      if (file) {
        file.selected = !file.selected
      }
    },

    // 全选/取消全选
    toggleSelectAll() {
      const shouldSelectAll = !this.selectAll
      this.selectableFiles.forEach(file => {
        file.selected = shouldSelectAll
      })
      this.selectAll = shouldSelectAll
    },

    // 重置状态
    reset() {
      this.$reset()
    }
  }
})
```

## AI 生成代码提示词大全

### 1. 文件上传功能提示词
```
请帮我生成一个Vue 3文件上传组件，要求：

**核心功能：**
1. 支持拖拽上传和点击上传
2. 多文件批量上传支持
3. 实时上传进度显示
4. 文件类型和大小验证
5. 上传过程中可以取消
6. 上传失败重试机制
7. 上传完成后文件列表展示

**技术要求：**
- 使用Vue 3 Composition API
- 集成Element Plus上传组件
- 支持TypeScript类型定义
- 响应式设计（移动端适配）
- 无障碍访问支持

**高级特性：**
- 文件预览功能（图片、PDF等）
- 上传队列管理
- 断点续传支持
- 文件去重检查
- 上传统计信息

**权限控制：**
- 基于角色的上传权限
- 文件权限设置（公开/私有）
- 上传文件数量限制
- 存储空间检查

**用户体验：**
- 拖拽区域高亮效果
- 上传状态动画
- 错误提示和处理
- 成功提示和反馈
- 键盘快捷键支持

请提供完整的组件代码、样式文件、类型定义和使用示例。
```

### 2. 智能问答界面提示词
```
请帮我创建一个完整的智能问答聊天界面，包含以下特性：

**界面布局：**
1. 消息列表区域（支持虚拟滚动）
2. 输入区域（多行文本框 + 发送按钮）
3. 侧边栏（历史对话、设置面板）
4. 顶部工具栏（模式切换、新建会话）
5. 底部状态栏（连接状态、字数统计）

**消息功能：**
1. 用户消息和AI回复的差异化显示
2. 流式响应处理（实时打字机效果）
3. 思考过程显示（可折叠）
4. 参考文档展示（可点击预览）
5. 建议问题快捷输入
6. 消息复制、删除、重新生成
7. 消息搜索和定位

**高级特性：**
1. 多模式支持（日常问答、专业问答）
2. 知识库切换和绑定
3. 会话管理（创建、删除、重命名）
4. 历史对话加载和恢复
5. 对话导出（Markdown、PDF）
6. 消息收藏和标签

**技术实现：**
- Vue 3 + Composition API
- 支持SSE流式响应
- Markdown渲染（代码高亮）
- 图片和文件消息支持
- 消息状态管理（发送中、成功、失败）
- 错误重试机制

**用户体验：**
- 响应式设计（桌面端、移动端）
- 键盘快捷键（Ctrl+Enter发送）
- 自动滚动到最新消息
- 消息加载动画
- 网络状态提示
- 无障碍访问支持

**性能优化：**
- 虚拟滚动（大量消息）
- 消息懒加载
- 图片懒加载
- 防抖输入处理
- 内存泄漏防护

请提供完整的组件代码、样式文件、类型定义、API接口和使用文档。
```

### 3. 文件预览组件提示词
```
请生成一个通用文件预览组件，支持多种文件格式：

**支持格式：**
1. PDF文件（使用PDF.js）
2. Office文档（Word、Excel、PowerPoint）
3. 图片文件（JPG、PNG、GIF、WebP）
4. 文本文件（TXT、MD、JSON、XML）
5. 代码文件（JS、TS、Vue、CSS等）
6. 视频文件（MP4、WebM）
7. 音频文件（MP3、WAV）

**预览功能：**
1. 全屏预览模式
2. 缩放控制（放大、缩小、适应窗口）
3. 页面导航（上一页、下一页、跳转）
4. 旋转功能（图片）
5. 搜索功能（文本内容）
6. 下载原文件
7. 打印功能

**界面设计：**
1. 工具栏（缩放、导航、下载等）
2. 侧边栏（目录、缩略图）
3. 状态栏（页码、缩放比例）
4. 加载进度指示器
5. 错误状态显示
6. 键盘快捷键支持

**技术要求：**
- Vue 3 + Composition API
- TypeScript类型定义
- 响应式设计
- 懒加载优化
- 内存管理
- 错误边界处理

**高级特性：**
1. 文件缓存机制
2. 预览历史记录
3. 批量预览（上一个/下一个文件）
4. 预览设置（主题、字体大小）
5. 注释和标记功能
6. 文件信息面板

**安全考虑：**
1. 文件类型验证
2. 文件大小限制
3. 恶意文件检测
4. 跨域资源处理
5. 内容安全策略

请提供完整的组件实现、样式文件、工具函数和使用示例。
```

### 4. 权限管理系统提示词
```
请帮我实现一个完整的基于角色的权限管理系统：

**权限架构：**
1. 用户管理（用户信息、状态、角色分配）
2. 角色管理（角色定义、权限分配）
3. 权限管理（权限定义、资源绑定）
4. 资源管理（菜单、按钮、API接口）

**权限层级：**
```
系统管理员
├── 管理所有资源
├── 用户和角色管理
└── 系统配置管理

知识库管理员
├── 管理指定知识库
├── 文件和成员管理
└── 知识库配置

普通用户
├── 查看有权限的资源
├── 操作自己的文件
└── 使用基础功能
```

**功能实现：**
1. 权限检查函数（组件级、方法级）
2. 路由守卫（页面访问控制）
3. 动态菜单生成
4. 按钮权限控制
5. API接口权限验证
6. 数据权限过滤

**技术实现：**
- Vue 3 + Pinia状态管理
- 路由权限守卫
- 自定义指令（v-permission）
- 权限装饰器
- JWT token验证
- 权限缓存机制

**权限配置：**
```javascript
// 权限配置示例
const permissions = {
  'system:admin': {
    name: '系统管理',
    resources: ['*']
  },
  'knowledge:admin': {
    name: '知识库管理',
    resources: ['knowledge:*', 'file:*']
  },
  'file:upload': {
    name: '文件上传',
    resources: ['file:upload']
  }
}
```

**使用方式：**
1. 组件权限：`v-permission="'file:upload'"`
2. 方法权限：`hasPermission('file:delete')`
3. 路由权限：`meta: { permission: 'admin' }`
4. 菜单权限：动态生成有权限的菜单

**高级特性：**
1. 权限继承和组合
2. 临时权限授权
3. 权限变更实时更新
4. 权限审计日志
5. 细粒度数据权限
6. 权限缓存和预加载

请提供完整的权限系统实现、配置文件、使用示例和最佳实践。
```

### 5. 状态管理提示词
```
请使用Pinia创建一个完整的状态管理方案：

**Store模块设计：**
1. UserStore - 用户信息和认证
2. FileStore - 文件管理状态
3. ChatStore - 对话和消息管理
4. AppStore - 全局配置和UI状态
5. PermissionStore - 权限和角色管理

**每个Store包含：**
```typescript
interface StoreStructure {
  // 状态定义
  state: {
    data: any[]
    loading: boolean
    error: string | null
    cache: Map<string, any>
  }

  // 计算属性
  getters: {
    filteredData: (state) => any[]
    isLoading: (state) => boolean
    hasError: (state) => boolean
  }

  // 异步操作
  actions: {
    fetchData(): Promise<void>
    createItem(item: any): Promise<void>
    updateItem(id: string, data: any): Promise<void>
    deleteItem(id: string): Promise<void>
  }
}
```

**高级特性：**
1. 数据持久化（localStorage、sessionStorage）
2. 乐观更新策略
3. 错误状态管理
4. 加载状态管理
5. 数据同步机制
6. 缓存策略
7. 状态重置和清理

**性能优化：**
1. 状态分片（按模块拆分）
2. 懒加载Store
3. 状态订阅优化
4. 内存泄漏防护
5. 大数据处理

**开发工具：**
1. Pinia DevTools集成
2. 状态调试工具
3. 时间旅行调试
4. 状态快照
5. 性能监控

**示例Store实现：**
```typescript
export const useFileStore = defineStore('file', {
  state: () => ({
    files: [] as FileItem[],
    loading: false,
    error: null as string | null,
    selectedFiles: [] as string[],
    uploadProgress: {} as Record<string, number>,
    filters: {
      search: '',
      status: 'all',
      type: 'all'
    }
  }),

  getters: {
    filteredFiles: (state) => {
      // 复杂筛选逻辑
    },
    selectedCount: (state) => state.selectedFiles.length,
    uploadingFiles: (state) => {
      // 上传中的文件
    }
  },

  actions: {
    async fetchFiles(params: FileListParams) {
      // 获取文件列表
    },
    async uploadFile(file: File, options: UploadOptions) {
      // 文件上传
    },
    async deleteFiles(fileIds: string[]) {
      // 批量删除
    }
  },

  // 持久化配置
  persist: {
    key: 'file-store',
    storage: localStorage,
    paths: ['filters', 'selectedFiles']
  }
})
```

请提供完整的Store实现、类型定义、使用示例和最佳实践指南。
```

### 6. 性能优化方案提示词
```
请为大型Vue应用提供完整的性能优化方案：

**优化目标：**
- 首屏加载时间 < 2秒
- 交互响应时间 < 100ms
- 内存使用稳定（无内存泄漏）
- 支持1000+文件列表流畅滚动
- 移动端性能优化

**代码分割优化：**
1. 路由级代码分割
2. 组件级懒加载
3. 第三方库按需加载
4. 动态导入优化
5. Bundle分析和优化

**渲染性能优化：**
1. 虚拟滚动实现
2. 组件缓存策略
3. 计算属性优化
4. 事件处理优化
5. DOM操作减少

**网络性能优化：**
1. API请求优化
2. 资源预加载
3. 缓存策略
4. CDN配置
5. 图片优化

**内存管理：**
1. 组件销毁清理
2. 事件监听器清理
3. 定时器清理
4. 大对象释放
5. 内存泄漏检测

**具体实现方案：**

**1. 虚拟滚动组件：**
```vue
<template>
  <div class="virtual-scroll" @scroll="handleScroll">
    <div :style="{ height: totalHeight + 'px' }">
      <div
        v-for="item in visibleItems"
        :key="item.id"
        :style="{
          position: 'absolute',
          top: item.top + 'px',
          height: itemHeight + 'px'
        }"
      >
        <slot :item="item.data" />
      </div>
    </div>
  </div>
</template>
```

**2. 图片懒加载指令：**
```javascript
const lazyLoad = {
  mounted(el, binding) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          el.src = binding.value
          observer.unobserve(el)
        }
      })
    })
    observer.observe(el)
  }
}
```

**3. 防抖和节流：**
```javascript
// 防抖搜索
const debouncedSearch = debounce((query) => {
  searchFiles(query)
}, 300)

// 节流滚动
const throttledScroll = throttle((event) => {
  handleScroll(event)
}, 16)
```

**4. 缓存策略：**
```javascript
// API响应缓存
const apiCache = new Map()
const cachedRequest = async (url, params) => {
  const key = `${url}:${JSON.stringify(params)}`
  if (apiCache.has(key)) {
    return apiCache.get(key)
  }

  const response = await api.get(url, { params })
  apiCache.set(key, response)
  return response
}
```

**监控指标：**
1. Core Web Vitals（LCP、FID、CLS）
2. 内存使用情况
3. 网络请求性能
4. 用户交互延迟
5. 错误率统计

请提供具体的优化代码实现、配置方案和监控工具。
```

### 3. 文件预览组件提示词
```
请生成一个通用文件预览组件，支持：
1. PDF文件预览（使用PDF.js）
2. Office文档预览
3. 图片预览（支持缩放、旋转）
4. 文本文件预览
5. 全屏预览模式
6. 下载功能
7. 预览失败时的错误处理
8. 加载状态显示
9. 键盘快捷键支持
10. 预览历史记录
```

### 4. 权限管理系统提示词
```
请帮我实现一个基于角色的权限管理系统：
1. 用户角色定义（管理员、普通用户）
2. 权限检查函数
3. 动态菜单显示
4. 操作按钮权限控制
5. 路由守卫
6. 权限缓存机制
7. 权限变更实时更新
8. 权限不足时的友好提示
9. 权限日志记录
10. 支持细粒度权限控制
```

### 5. 状态管理提示词
```
请使用Pinia创建一个状态管理store，包含：
1. 用户信息管理
2. 文件列表状态
3. 对话历史状态
4. 全局配置管理
5. 异步操作处理
6. 状态持久化
7. 状态重置功能
8. 错误状态管理
9. 加载状态管理
10. 状态变更监听
```

## 样式设计规范

### 颜色主题
- 主色调：#1677ff（蓝色）
- 成功色：#52c41a（绿色）
- 警告色：#faad14（橙色）
- 错误色：#ff4d4f（红色）
- 文字色：#262626（深灰）

### 间距规范
- 基础间距：8px
- 组件间距：16px
- 区块间距：24px
- 页面边距：32px

### 响应式断点
- 移动端：< 768px
- 平板端：768px - 1024px
- 桌面端：> 1024px

## 性能优化建议

1. **虚拟滚动**：文件列表使用虚拟滚动处理大量数据
2. **懒加载**：图片和文件预览采用懒加载
3. **防抖处理**：搜索和输入操作添加防抖
4. **缓存策略**：API响应和文件数据缓存
5. **代码分割**：路由级别的代码分割

## 错误处理机制

1. **网络错误**：自动重试和用户提示
2. **文件上传错误**：详细错误信息和重试选项
3. **权限错误**：友好的权限不足提示
4. **数据格式错误**：数据验证和容错处理

## 测试建议

1. **单元测试**：核心业务逻辑方法
2. **组件测试**：UI组件交互测试
3. **集成测试**：API接口集成测试
4. **E2E测试**：完整用户流程测试

## 部署注意事项

1. **环境变量**：API地址和密钥配置
2. **路由配置**：History模式路由配置
3. **静态资源**：CDN配置和缓存策略
4. **安全配置**：CSP和CORS设置

## API 接口设计

### 文件管理接口

#### 上传文件
```javascript
// POST /api/file/upload
const uploadFile = async (formData, onProgress, repoId, permissionType) => {
  return await axios.post('/api/file/upload', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: onProgress,
    params: { repoId, permissionType }
  });
};
```

#### 获取文件列表
```javascript
// GET /api/file/list
const getFileList = async (params) => {
  return await axios.get('/api/file/list', { params });
};
```

#### 删除文件
```javascript
// DELETE /api/file/delete
const deleteFile = async (fileIds) => {
  return await axios.delete('/api/file/delete', { data: { ids: fileIds } });
};
```

### 智能问答接口

#### 创建会话
```javascript
// POST /api/v1/agents/{agentId}/sessions
const createSession = async (data) => {
  return await axios.post(`/api/v1/agents/${agentId}/sessions`, data);
};
```

#### 发送消息
```javascript
// POST /api/v1/agents/{agentId}/completions
const sendMessage = async (data) => {
  return await axios.post(`/api/v1/agents/${agentId}/completions`, data, {
    responseType: 'stream'
  });
};
```

## 高级功能实现

### 1. 流式响应处理
```javascript
const handleStreamResponse = async (response) => {
  const reader = response.body.getReader();
  const decoder = new TextDecoder();

  while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    const chunk = decoder.decode(value);
    const lines = chunk.split('\n');

    for (const line of lines) {
      if (line.startsWith('data: ')) {
        const data = JSON.parse(line.slice(6));
        // 处理流式数据
        updateMessageContent(data);
      }
    }
  }
};
```

### 2. 文件状态轮询
```javascript
const startFileStatusPolling = () => {
  const interval = setInterval(async () => {
    if (hasProcessingFiles()) {
      const updatedFiles = await getFileList(currentParams);
      updateFileStatuses(updatedFiles);
    } else {
      clearInterval(interval);
    }
  }, 5000);
};
```

### 3. 权限检查系统
```javascript
const checkPermission = (action, resource, user) => {
  const permissions = user.permissions || [];

  // 最高级管理员
  if (permissions.includes('ai:knowledge:member')) {
    return true;
  }

  // 知识库管理员
  if (action === 'edit' && resource.type === 'repository') {
    return resource.operationPermission === 'EDIT';
  }

  // 文件所有者
  if (resource.userId === user.id) {
    return true;
  }

  return false;
};
```

## 组件复用设计

### 1. 文件图标组件
```vue
<template>
  <v-icon :size="size" :color="getFileIconColor(fileName)">
    {{ getFileIconByExtension(fileName) }}
  </v-icon>
</template>

<script setup>
const props = defineProps({
  fileName: String,
  size: { type: String, default: 'default' }
});

const getFileIconByExtension = (filename) => {
  // 根据文件扩展名返回对应图标
};
</script>
```

### 2. 状态标签组件
```vue
<template>
  <div class="status-tag" :style="statusStyle">
    {{ getStatusText(status) }}
  </div>
</template>

<script setup>
const props = defineProps({
  status: String
});

const statusStyle = computed(() => ({
  backgroundColor: getStatusBgColor(props.status),
  color: getStatusTextColor(props.status)
}));
</script>
```

## 错误边界处理

### 全局错误处理
```javascript
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err);
  console.error('错误信息:', info);

  // 发送错误报告
  reportError(err, info);

  // 显示用户友好的错误提示
  showErrorToast('系统出现异常，请刷新页面重试');
};
```

### API错误拦截
```javascript
axios.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      // 未授权，跳转登录
      router.push('/login');
    } else if (error.response?.status === 403) {
      // 权限不足
      showErrorToast('权限不足');
    } else if (error.response?.status >= 500) {
      // 服务器错误
      showErrorToast('服务器异常，请稍后重试');
    }

    return Promise.reject(error);
  }
);
```

## 性能监控

### 组件性能监控
```javascript
const performanceMonitor = {
  startTime: 0,

  start(componentName) {
    this.startTime = performance.now();
    console.log(`${componentName} 开始渲染`);
  },

  end(componentName) {
    const endTime = performance.now();
    const duration = endTime - this.startTime;
    console.log(`${componentName} 渲染耗时: ${duration}ms`);

    // 如果渲染时间过长，发送性能报告
    if (duration > 1000) {
      reportPerformanceIssue(componentName, duration);
    }
  }
};
```

## 国际化支持

### 多语言配置
```javascript
const messages = {
  zh: {
    file: {
      upload: '上传文件',
      delete: '删除文件',
      download: '下载文件',
      preview: '预览文件'
    },
    chat: {
      send: '发送',
      thinking: '思考中...',
      newSession: '新建会话'
    }
  },
  en: {
    file: {
      upload: 'Upload File',
      delete: 'Delete File',
      download: 'Download File',
      preview: 'Preview File'
    },
    chat: {
      send: 'Send',
      thinking: 'Thinking...',
      newSession: 'New Session'
    }
  }
};
```

## 安全最佳实践

### 1. XSS 防护
```javascript
// 内容过滤
const sanitizeContent = (content) => {
  return DOMPurify.sanitize(content, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'code', 'pre'],
    ALLOWED_ATTR: ['class']
  });
};
```

### 2. CSRF 防护
```javascript
// 请求头添加 CSRF Token
axios.defaults.headers.common['X-CSRF-Token'] = getCsrfToken();
```

### 3. 文件上传安全
```javascript
const validateFile = (file) => {
  // 文件类型检查
  const allowedTypes = ['pdf', 'doc', 'docx', 'txt', 'jpg', 'png'];
  const fileExt = file.name.split('.').pop().toLowerCase();

  if (!allowedTypes.includes(fileExt)) {
    throw new Error('不支持的文件类型');
  }

  // 文件大小检查
  if (file.size > 50 * 1024 * 1024) { // 50MB
    throw new Error('文件大小超出限制');
  }

  return true;
};
```

## 测试用例示例

### 单元测试
```javascript
describe('FileUpload Component', () => {
  test('should validate file type correctly', () => {
    const validFile = new File(['content'], 'test.pdf', { type: 'application/pdf' });
    const invalidFile = new File(['content'], 'test.exe', { type: 'application/exe' });

    expect(validateFile(validFile)).toBe(true);
    expect(() => validateFile(invalidFile)).toThrow('不支持的文件类型');
  });

  test('should handle upload progress', async () => {
    const mockProgress = jest.fn();
    const file = new File(['content'], 'test.pdf');

    await uploadFile(file, mockProgress);

    expect(mockProgress).toHaveBeenCalled();
  });
});
```

### 集成测试
```javascript
describe('Chat Integration', () => {
  test('should create session and send message', async () => {
    // 创建会话
    const session = await createSession({ name: '测试会话' });
    expect(session.data.id).toBeDefined();

    // 发送消息
    const response = await sendMessage({
      question: '你好',
      session_id: session.data.id
    });

    expect(response.status).toBe(200);
  });
});
```

## 开发工具配置

### ESLint 配置
```javascript
module.exports = {
  extends: [
    '@vue/eslint-config-typescript',
    'plugin:vue/vue3-recommended'
  ],
  rules: {
    'vue/multi-word-component-names': 'off',
    'vue/no-v-html': 'warn',
    '@typescript-eslint/no-unused-vars': 'error'
  }
};
```

### Prettier 配置
```javascript
module.exports = {
  semi: true,
  singleQuote: true,
  tabWidth: 2,
  trailingComma: 'es5',
  printWidth: 100,
  vueIndentScriptAndStyle: true
};
```

## 部署脚本

### Docker 配置
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

### Nginx 配置
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://backend:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## AI 代码生成最佳实践

### 1. 组件生成提示词模板

#### 基础组件提示词
```
请帮我生成一个Vue 3组件，要求：

**功能需求：**
- [具体功能描述]
- [交互行为说明]
- [数据处理逻辑]

**技术要求：**
- 使用Vue 3 Composition API
- 使用TypeScript类型定义
- 集成Vuetify/Element Plus组件
- 响应式设计支持
- 无障碍访问支持

**代码规范：**
- 遵循Vue 3最佳实践
- 添加详细的JSDoc注释
- 包含错误处理
- 添加单元测试用例
- 性能优化考虑

**示例输入/输出：**
- 输入数据格式：[数据结构]
- 输出事件：[事件列表]
- 预期行为：[行为描述]
```

#### 复杂业务组件提示词
```
请生成一个知识库文件管理组件，包含以下特性：

**核心功能：**
1. 文件列表展示（表格/卡片视图切换）
2. 文件上传（拖拽+点击，支持多文件）
3. 文件预览（PDF、Office、图片）
4. 文件操作（下载、删除、重命名、移动）
5. 批量操作（选择、删除、下载）
6. 搜索和筛选功能
7. 分页或虚拟滚动

**状态管理：**
- 文件列表状态
- 上传进度状态
- 选择状态管理
- 加载状态管理

**权限控制：**
- 基于角色的操作权限
- 文件所有者权限
- 动态按钮显示/隐藏

**用户体验：**
- 加载状态指示
- 操作确认对话框
- 错误提示和重试
- 快捷键支持
- 拖拽排序

**技术实现：**
- Vue 3 + Composition API
- Pinia状态管理
- Axios文件上传
- 虚拟滚动优化
- 防抖搜索
- 错误边界处理

请提供完整的组件代码、类型定义、测试用例和使用文档。
```

### 2. API集成提示词

#### RESTful API集成
```
请帮我生成一个完整的API服务层，包含：

**API模块：**
1. 用户认证API（登录、注册、刷新token）
2. 文件管理API（上传、下载、删除、列表）
3. 知识库API（创建、查询、更新、删除）
4. 智能问答API（会话管理、消息发送）

**技术要求：**
- 使用Axios作为HTTP客户端
- 统一的请求/响应拦截器
- 错误处理和重试机制
- 请求取消支持
- 上传进度监控
- 响应数据类型定义

**安全特性：**
- JWT token自动刷新
- 请求签名验证
- CSRF防护
- 请求频率限制

**代码结构：**
```typescript
// api/types.ts - 类型定义
// api/client.ts - Axios配置
// api/auth.ts - 认证相关
// api/file.ts - 文件管理
// api/chat.ts - 智能问答
// api/index.ts - 统一导出
```

请提供完整的API服务代码和使用示例。
```

### 3. 状态管理提示词

#### Pinia Store生成
```
请使用Pinia创建一个完整的状态管理方案：

**Store模块：**
1. UserStore - 用户信息和权限
2. FileStore - 文件列表和操作状态
3. ChatStore - 对话历史和会话管理
4. AppStore - 全局配置和UI状态

**每个Store需要包含：**
- State定义（响应式数据）
- Getters（计算属性）
- Actions（异步操作）
- 持久化配置
- 类型定义

**特殊要求：**
- 支持数据持久化（localStorage）
- 乐观更新策略
- 错误状态管理
- 加载状态管理
- 数据同步机制

**示例Store结构：**
```typescript
export const useFileStore = defineStore('file', {
  state: () => ({
    files: [] as FileItem[],
    loading: false,
    error: null as string | null,
    selectedFiles: [] as string[],
    uploadProgress: {} as Record<string, number>
  }),

  getters: {
    selectedFileCount: (state) => state.selectedFiles.length,
    hasSelectedFiles: (state) => state.selectedFiles.length > 0
  },

  actions: {
    async fetchFiles(params: FileListParams) {
      // 实现文件列表获取
    },

    async uploadFile(file: File, onProgress?: ProgressCallback) {
      // 实现文件上传
    }
  }
});
```

请提供完整的Store实现和使用示例。
```

### 4. 测试代码生成提示词

#### 组件测试生成
```
请为以下Vue组件生成完整的测试套件：

**组件信息：**
- 组件名：FileUploadComponent
- 主要功能：文件上传、进度显示、错误处理
- Props：accept, multiple, maxSize, disabled
- Events：upload-success, upload-error, upload-progress

**测试要求：**
1. 单元测试（Jest + Vue Test Utils）
   - Props验证测试
   - 事件触发测试
   - 方法调用测试
   - 计算属性测试

2. 集成测试
   - 文件选择流程
   - 上传进度更新
   - 错误处理流程
   - 成功回调执行

3. 用户交互测试
   - 拖拽上传测试
   - 点击上传测试
   - 取消上传测试
   - 重试机制测试

**测试覆盖率要求：**
- 语句覆盖率 > 90%
- 分支覆盖率 > 85%
- 函数覆盖率 > 95%

**Mock要求：**
- API请求Mock
- 文件对象Mock
- 进度回调Mock
- 错误场景Mock

请提供完整的测试代码和配置文件。
```

### 5. 性能优化提示词

#### 性能优化方案生成
```
请为大型Vue应用提供完整的性能优化方案：

**优化目标：**
- 首屏加载时间 < 2秒
- 交互响应时间 < 100ms
- 内存使用稳定
- 支持1000+文件列表流畅滚动

**优化策略：**
1. 代码分割和懒加载
2. 虚拟滚动实现
3. 图片懒加载
4. 防抖和节流
5. 缓存策略
6. Bundle优化

**具体实现：**
- 路由级代码分割
- 组件级懒加载
- 虚拟列表组件
- 图片懒加载指令
- API响应缓存
- 状态管理优化

**监控指标：**
- Core Web Vitals
- 内存使用情况
- 网络请求性能
- 用户交互延迟

请提供具体的优化代码实现和配置方案。
```

### 6. 移动端适配提示词

#### 响应式设计生成
```
请为知识库管理系统创建完整的移动端适配方案：

**适配要求：**
1. 响应式布局（320px - 1920px）
2. 触摸友好的交互设计
3. 移动端特有功能（拍照上传、手势操作）
4. 性能优化（减少重绘、优化动画）

**设计规范：**
- 最小触摸目标：44px
- 字体大小：最小14px
- 间距：基于8px网格
- 颜色对比度：符合WCAG标准

**技术实现：**
- CSS Grid + Flexbox布局
- CSS媒体查询
- Viewport meta标签
- Touch事件处理
- 手势库集成

**组件适配：**
- 导航菜单（汉堡菜单）
- 文件列表（卡片布局）
- 上传组件（拖拽替代）
- 对话界面（全屏模式）

请提供完整的移动端适配代码和样式文件。
```

## 代码质量保证

### 1. 代码审查清单
```markdown
**功能性检查：**
- [ ] 功能是否按需求实现
- [ ] 边界条件是否处理
- [ ] 错误场景是否覆盖
- [ ] 性能是否满足要求

**代码质量：**
- [ ] 代码结构是否清晰
- [ ] 命名是否规范
- [ ] 注释是否充分
- [ ] 是否遵循最佳实践

**安全性：**
- [ ] 输入验证是否完整
- [ ] XSS防护是否到位
- [ ] 权限检查是否正确
- [ ] 敏感信息是否保护

**可维护性：**
- [ ] 代码是否易于理解
- [ ] 组件是否可复用
- [ ] 依赖关系是否合理
- [ ] 测试覆盖是否充分
```

### 2. 自动化检查工具
```javascript
// package.json scripts
{
  "scripts": {
    "lint": "eslint src --ext .vue,.js,.ts",
    "lint:fix": "eslint src --ext .vue,.js,.ts --fix",
    "type-check": "vue-tsc --noEmit",
    "test": "jest",
    "test:coverage": "jest --coverage",
    "build:analyze": "vite build --mode analyze"
  }
}
```

## 文档生成建议

### 1. 组件文档模板
```markdown
# ComponentName 组件

## 概述
[组件功能描述]

## 使用示例
```vue
<template>
  <ComponentName
    :prop1="value1"
    @event1="handler1"
  />
</template>
```

## API

### Props
| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| prop1  | String | '' | 属性说明 |

### Events
| 事件名 | 参数 | 说明 |
|--------|------|------|
| event1 | (value: string) | 事件说明 |

### Slots
| 插槽名 | 说明 |
|--------|------|
| default | 默认插槽 |

## 注意事项
[使用注意事项]
```

### 2. API文档生成
```javascript
// 使用JSDoc生成API文档
/**
 * 上传文件到服务器
 * @param {File} file - 要上传的文件
 * @param {Function} onProgress - 进度回调函数
 * @param {string} repoId - 知识库ID
 * @returns {Promise<UploadResponse>} 上传结果
 * @example
 * const result = await uploadFile(file, (progress) => {
 *   console.log(`上传进度: ${progress}%`);
 * }, 'repo123');
 */
export const uploadFile = async (file, onProgress, repoId) => {
  // 实现代码
};
```

### 7. 移动端适配提示词
```
请为知识库管理系统创建完整的移动端适配方案：

**响应式设计要求：**
1. 支持设备尺寸：320px - 1920px
2. 断点设计：手机（<768px）、平板（768-1024px）、桌面（>1024px）
3. 触摸友好的交互设计
4. 移动端特有功能集成

**布局适配：**
1. 汉堡菜单导航（移动端）
2. 底部标签栏（主要功能）
3. 全屏对话界面
4. 卡片式文件列表
5. 抽屉式侧边栏

**交互优化：**
1. 触摸手势支持（滑动、长按、双击）
2. 最小触摸目标：44px
3. 拖拽上传替代方案
4. 虚拟键盘适配
5. 横竖屏切换

**性能优化：**
1. 移动端资源压缩
2. 图片自适应加载
3. 减少重绘和回流
4. 电池优化考虑
5. 网络状态适配

**移动端特有功能：**
1. 拍照上传文件
2. 语音输入支持
3. 分享功能集成
4. 推送通知
5. 离线缓存

**技术实现：**
```css
/* 响应式断点 */
@media (max-width: 767px) {
  /* 手机端样式 */
}

@media (min-width: 768px) and (max-width: 1023px) {
  /* 平板端样式 */
}

@media (min-width: 1024px) {
  /* 桌面端样式 */
}
```

```javascript
// 设备检测
const isMobile = () => {
  return /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

// 触摸事件处理
const handleTouch = {
  start: (e) => { /* 触摸开始 */ },
  move: (e) => { /* 触摸移动 */ },
  end: (e) => { /* 触摸结束 */ }
}
```

请提供完整的移动端适配代码、样式文件和交互逻辑。
```

### 8. 测试方案提示词
```
请为Vue组件创建完整的测试套件：

**测试类型：**
1. 单元测试（Jest + Vue Test Utils）
2. 组件测试（用户交互测试）
3. 集成测试（API集成测试）
4. E2E测试（Cypress/Playwright）
5. 性能测试（Lighthouse CI）

**测试覆盖范围：**
1. 组件渲染测试
2. Props和Events测试
3. 用户交互测试
4. 状态管理测试
5. API调用测试
6. 错误处理测试

**具体测试用例：**

**1. 文件上传组件测试：**
```javascript
describe('FileUpload Component', () => {
  test('should render upload area', () => {
    const wrapper = mount(FileUpload)
    expect(wrapper.find('.upload-area')).toBeTruthy()
  })

  test('should handle file selection', async () => {
    const wrapper = mount(FileUpload)
    const file = new File(['content'], 'test.pdf', { type: 'application/pdf' })

    await wrapper.vm.handleFileSelect([file])

    expect(wrapper.emitted('file-selected')).toBeTruthy()
    expect(wrapper.emitted('file-selected')[0][0]).toEqual([file])
  })

  test('should validate file type', () => {
    const wrapper = mount(FileUpload)
    const invalidFile = new File(['content'], 'test.exe', { type: 'application/exe' })

    expect(() => wrapper.vm.validateFile(invalidFile)).toThrow('不支持的文件类型')
  })

  test('should show upload progress', async () => {
    const wrapper = mount(FileUpload)
    const mockProgress = jest.fn()

    wrapper.vm.uploadFile(mockFile, mockProgress)

    // 模拟进度更新
    await wrapper.vm.$nextTick()
    expect(mockProgress).toHaveBeenCalled()
  })
})
```

**2. 聊天组件测试：**
```javascript
describe('ChatInterface Component', () => {
  test('should send message on enter key', async () => {
    const wrapper = mount(ChatInterface)
    const input = wrapper.find('input[type="text"]')

    await input.setValue('Hello')
    await input.trigger('keydown.enter')

    expect(wrapper.emitted('send-message')).toBeTruthy()
    expect(wrapper.emitted('send-message')[0][0]).toBe('Hello')
  })

  test('should display streaming response', async () => {
    const wrapper = mount(ChatInterface)

    // 模拟流式响应
    wrapper.vm.handleStreamData({ answer: 'Hello' })
    await wrapper.vm.$nextTick()

    expect(wrapper.text()).toContain('Hello')
  })

  test('should show reference documents', async () => {
    const wrapper = mount(ChatInterface)
    const mockDocs = [{ doc_name: 'test.pdf', doc_id: '123' }]

    wrapper.vm.handleStreamData({ docs: mockDocs })
    await wrapper.vm.$nextTick()

    expect(wrapper.find('.reference-docs')).toBeTruthy()
  })
})
```

**3. 状态管理测试：**
```javascript
describe('File Store', () => {
  test('should load files', async () => {
    const store = useFileStore()
    const mockFiles = [{ fileId: '1', fileName: 'test.pdf' }]

    // Mock API
    jest.spyOn(fileApi, 'getFileList').mockResolvedValue({
      code: 0,
      data: { list: mockFiles, total: 1 }
    })

    await store.loadFiles('repo123')

    expect(store.files).toEqual(mockFiles)
    expect(store.totalFiles).toBe(1)
  })

  test('should handle upload error', async () => {
    const store = useFileStore()
    const mockError = new Error('Upload failed')

    jest.spyOn(fileApi, 'uploadFile').mockRejectedValue(mockError)

    await expect(store.uploadFile(mockFile)).rejects.toThrow('Upload failed')
  })
})
```

**4. E2E测试：**
```javascript
// cypress/integration/file-management.spec.js
describe('File Management', () => {
  beforeEach(() => {
    cy.login('admin', 'password')
    cy.visit('/knowledge-base')
  })

  it('should upload file successfully', () => {
    cy.get('[data-cy="upload-button"]').click()
    cy.get('input[type="file"]').selectFile('fixtures/test.pdf')
    cy.get('[data-cy="confirm-upload"]').click()

    cy.contains('上传成功').should('be.visible')
    cy.get('[data-cy="file-list"]').should('contain', 'test.pdf')
  })

  it('should preview file', () => {
    cy.get('[data-cy="file-item"]').first().click()
    cy.get('[data-cy="preview-modal"]').should('be.visible')
    cy.get('[data-cy="pdf-viewer"]').should('exist')
  })

  it('should delete file', () => {
    cy.get('[data-cy="file-actions"]').first().click()
    cy.get('[data-cy="delete-action"]').click()
    cy.get('[data-cy="confirm-delete"]').click()

    cy.contains('删除成功').should('be.visible')
  })
})
```

**测试配置：**
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  collectCoverageFrom: [
    'src/**/*.{js,vue}',
    '!src/main.js',
    '!**/node_modules/**'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
}
```

请提供完整的测试代码、配置文件和CI/CD集成方案。
```

## 开发最佳实践

### 1. 代码规范

#### 1.1 命名规范
```javascript
// 组件命名：PascalCase
const FileUploadDialog = defineComponent({})

// 变量命名：camelCase
const fileList = ref([])
const isLoading = ref(false)

// 常量命名：UPPER_SNAKE_CASE
const MAX_FILE_SIZE = 50 * 1024 * 1024
const ALLOWED_FILE_TYPES = ['pdf', 'doc', 'docx']

// 方法命名：动词开头
const handleFileUpload = () => {}
const validateFileType = () => {}
const fetchFileList = () => {}

// 事件命名：kebab-case
emit('file-uploaded', file)
emit('upload-progress', progress)
```

#### 1.2 文件组织
```
src/
├── components/           # 通用组件
│   ├── base/            # 基础组件
│   ├── business/        # 业务组件
│   └── layout/          # 布局组件
├── views/               # 页面组件
├── stores/              # 状态管理
├── composables/         # 组合式函数
├── utils/               # 工具函数
├── api/                 # API接口
├── types/               # 类型定义
├── styles/              # 样式文件
└── assets/              # 静态资源
```

#### 1.3 注释规范
```javascript
/**
 * 文件上传处理函数
 * @param {File} file - 要上传的文件
 * @param {Function} onProgress - 进度回调函数
 * @param {string} repoId - 知识库ID
 * @returns {Promise<UploadResponse>} 上传结果
 * @throws {Error} 当文件验证失败时抛出错误
 *
 * @example
 * const result = await uploadFile(file, (progress) => {
 *   console.log(`上传进度: ${progress}%`)
 * }, 'repo123')
 */
const uploadFile = async (file, onProgress, repoId) => {
  // 实现代码
}
```

### 2. 性能优化实践

#### 2.1 组件优化
```vue
<template>
  <!-- 使用v-memo缓存复杂计算 -->
  <div v-memo="[file.id, file.status]">
    <FileItem :file="file" />
  </div>

  <!-- 使用v-once避免重复渲染 -->
  <div v-once>{{ expensiveCalculation() }}</div>

  <!-- 条件渲染优化 -->
  <template v-if="showFileList">
    <FileList :files="files" />
  </template>
</template>

<script setup>
// 使用shallowRef减少响应式开销
const fileList = shallowRef([])

// 使用computed缓存计算结果
const filteredFiles = computed(() => {
  return fileList.value.filter(file => file.status === 'PARSE_SUCCESS')
})

// 使用watchEffect自动追踪依赖
watchEffect(() => {
  if (selectedRepoId.value) {
    loadFiles(selectedRepoId.value)
  }
})
</script>
```

#### 2.2 内存管理
```javascript
// 组件销毁时清理资源
onUnmounted(() => {
  // 清除定时器
  if (pollingTimer) {
    clearInterval(pollingTimer)
  }

  // 取消网络请求
  if (abortController) {
    abortController.abort()
  }

  // 清除事件监听
  window.removeEventListener('scroll', handleScroll)

  // 清理大对象引用
  fileList.value = []
  messageList.value = []
})
```

### 3. 错误处理策略

#### 3.1 全局错误处理
```javascript
// main.js
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err)
  console.error('错误信息:', info)

  // 发送错误报告
  errorReporting.report(err, {
    component: vm?.$options.name,
    info: info,
    url: window.location.href,
    userAgent: navigator.userAgent
  })

  // 显示用户友好的错误提示
  showErrorToast('系统出现异常，请刷新页面重试')
}
```

#### 3.2 API错误处理
```javascript
// api/interceptors.js
axios.interceptors.response.use(
  response => response,
  error => {
    const { response } = error

    switch (response?.status) {
      case 401:
        // 未授权，跳转登录
        userStore.logout()
        router.push('/login')
        break

      case 403:
        // 权限不足
        showErrorToast('权限不足，请联系管理员')
        break

      case 404:
        // 资源不存在
        showErrorToast('请求的资源不存在')
        break

      case 500:
        // 服务器错误
        showErrorToast('服务器异常，请稍后重试')
        break

      default:
        // 其他错误
        showErrorToast(response?.data?.message || '网络异常，请检查网络连接')
    }

    return Promise.reject(error)
  }
)
```

### 4. 安全最佳实践

#### 4.1 输入验证
```javascript
// 文件上传安全检查
const validateFile = (file) => {
  // 1. 文件类型检查
  const allowedTypes = ['pdf', 'doc', 'docx', 'txt', 'jpg', 'png']
  const fileExt = file.name.split('.').pop().toLowerCase()

  if (!allowedTypes.includes(fileExt)) {
    throw new Error('不支持的文件类型')
  }

  // 2. 文件大小检查
  const maxSize = 50 * 1024 * 1024 // 50MB
  if (file.size > maxSize) {
    throw new Error('文件大小超出限制')
  }

  // 3. 文件名安全检查
  const dangerousChars = /[<>:"/\\|?*]/
  if (dangerousChars.test(file.name)) {
    throw new Error('文件名包含非法字符')
  }

  return true
}

// 内容过滤
const sanitizeContent = (content) => {
  return DOMPurify.sanitize(content, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'code', 'pre'],
    ALLOWED_ATTR: ['class']
  })
}
```

#### 4.2 权限验证
```javascript
// 操作前权限检查
const checkOperationPermission = (operation, resource) => {
  const user = userStore.user

  // 系统管理员拥有所有权限
  if (user.permissions.includes('system:admin')) {
    return true
  }

  // 检查具体权限
  const requiredPermission = `${resource}:${operation}`
  if (!user.permissions.includes(requiredPermission)) {
    throw new Error('权限不足')
  }

  return true
}
```

## 部署和运维

### 1. 构建配置
```javascript
// vite.config.js
export default defineConfig({
  build: {
    // 代码分割
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['vuetify', 'element-plus'],
          utils: ['axios', 'lodash']
        }
      }
    },

    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },

  // 环境变量
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version)
  }
})
```

### 2. Docker部署
```dockerfile
# Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 3. 监控配置
```javascript
// 性能监控
const performanceObserver = new PerformanceObserver((list) => {
  list.getEntries().forEach((entry) => {
    if (entry.entryType === 'navigation') {
      // 页面加载性能
      console.log('页面加载时间:', entry.loadEventEnd - entry.fetchStart)
    }

    if (entry.entryType === 'measure') {
      // 自定义性能指标
      console.log('自定义指标:', entry.name, entry.duration)
    }
  })
})

performanceObserver.observe({ entryTypes: ['navigation', 'measure'] })
```

## 总结

这份全面的HomeView组件开发文档涵盖了：

### 📚 **文档内容**
1. **系统架构设计** - 整体架构图、数据流设计、模块划分
2. **详细功能实现** - 每个模块的具体开发功能和代码实现
3. **开发流程指南** - 从环境搭建到组件开发的完整流程
4. **AI代码生成** - 8大类专业提示词模板，涵盖所有主要功能
5. **最佳实践** - 代码规范、性能优化、安全策略、错误处理
6. **测试和部署** - 完整的测试方案和部署配置

### 🎯 **实用价值**
- **新手指南**：详细的开发步骤和代码示例
- **AI助手**：专业的提示词模板，提高AI代码生成质量
- **架构参考**：可复用的设计模式和组件结构
- **维护手册**：现有代码的维护和扩展指导
- **最佳实践**：行业标准的开发规范和优化策略

### 🚀 **技术特色**
- 基于Vue 3 + Composition API的现代化开发
- 模块化设计，高内聚低耦合
- 完整的权限管理和安全策略
- 性能优化和移动端适配
- 全面的测试覆盖和错误处理

这份文档可以作为大型Vue项目开发的完整指南，帮助团队快速上手并保持代码质量！
