<template>
  <div></div>
</template>
 
<script setup>
// import { Loading } from 'element-plus'
import useUserStore from "@/store/modules/user";

const userStore = useUserStore();
const router = useRouter();
const { proxy } = getCurrentInstance();
let loadingInstance = ref();
const redirect = ref(undefined);
const source = ref();
const code = ref();
const state = ref();
function created(){
  loadingInstance = Loading.service({
    lock: true,
    text: "正在验证第三方应用账户数据，请稍候",
    spinner: "el-icon-loading",
    background: "rgba(0, 0, 0, 0.7)",
  })
  // 第三方登录回调参数
  source.value = proxy.$route.query.source;
  code.value = proxy.$route.query.code;
  state.value = proxy.$route.query.state;
  userStore.login({
    code: code.value,
    state: state.value,
    source: source.value}).then(()=>{

  })
  this.$store.dispatch("SocialLogin", {
    code: code,
    state: state,
    source: source
  }).then(() => {
    loadingInstance.close();
    router.push({ path: redirect.value || "/", query: otherQueryParams });
    // proxy.$router.push({ path: redirect.value || "/" }).catch(()=>{});
  }).catch(() => {
    loadingInstance.close();
  });
}

created();


// export default {
//   data() {
//     return {
//       redirect: undefined,
//     };
//   },
//   created() {
//     loadingInstance = Loading.service({
//       lock: true,
//       text: "正在验证第三方应用账户数据，请稍候",
//       spinner: "el-icon-loading",
//       background: "rgba(0, 0, 0, 0.7)",
//     })
//      // 第三方登录回调参数
//     this.source = this.$route.query.source;
//     this.code = this.$route.query.code;
//     this.state = this.$route.query.state;
//     this.$store.dispatch("SocialLogin", {
//       code: this.code,
//       state: this.state,
//       source: this.source
//     }).then(() => {
//       loadingInstance.close();
//       this.$router.push({ path: this.redirect || "/" }).catch(()=>{});
//     }).catch(() => {
//       loadingInstance.close();
//     });
//   },
//   methods: {
//   },
// };
</script>
 
<style rel="stylesheet/scss" lang="scss">
</style>