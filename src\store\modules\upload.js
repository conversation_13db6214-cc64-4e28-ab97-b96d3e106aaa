import { defineStore } from 'pinia';
import { upFile, addFile } from '@/api/file/file';
import { getToken } from '@/utils/auth';
import useUserStore from './user';

// 上传状态管理store
const useUploadStore = defineStore('upload', {
  state: () => ({
    // 上传中的文件列表
    uploadingFiles: [],
    // 是否有上传任务正在进行
    isUploading: false,
    // 上传完成的文件数量
    uploadedCount: 0,
    // 上传进度更新的回调函数
    progressCallbacks: {},
    // 上传完成的回调函数
    completeCallbacks: {}
  }),
  
  getters: {
    // 获取上传中的文件数量
    uploadingCount: (state) => state.uploadingFiles.length,
    
    // 获取上传进度
    getProgress: (state) => (fileId) => {
      const file = state.uploadingFiles.find(f => f.id === fileId);
      return file ? file.progress : 0;
    },
    
    // 获取文件状态
    getStatus: (state) => (fileId) => {
      const file = state.uploadingFiles.find(f => f.id === fileId);
      return file ? file.status : null;
    }
  },
  
  actions: {
    // 判断是否有上传或处理中的文件
    isFileProcessing() {
      // 只有状态为UPLOADING或PARSING的文件才算处理中
      return this.uploadingFiles.some(f => 
        f.status === 'UPLOADING' || f.status === 'PARSING'
      );
    },
    
    // 添加上传文件
    addUploadingFile(file) {
      // 创建上传中的文件项
      const uploadingFile = {
        id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        fileName: file.name,
        name: file.name,
        size: file.size,
        date: new Date().toLocaleString(),
        uploader: '当前用户',
        description: '',
        tags: [],
        selected: false,
        status: 'UPLOADING',
        progress: 0,
        originalFile: file
      };
      
      this.uploadingFiles.push(uploadingFile);
      this.isUploading = true; // 添加新文件时，总是将状态设为上传中
      
      return uploadingFile.id;
    },
    
    // 更新文件进度
    updateProgress(fileId, progress) {
      const fileIndex = this.uploadingFiles.findIndex(f => f.id === fileId);
      if (fileIndex !== -1) {
        this.uploadingFiles[fileIndex].progress = progress;
        
        // 调用进度回调
        if (this.progressCallbacks[fileId]) {
          this.progressCallbacks[fileId](progress);
        }
      }
    },
    
    // 更新文件状态
    updateStatus(fileId, status) {
      const fileIndex = this.uploadingFiles.findIndex(f => f.id === fileId);
      if (fileIndex !== -1) {
        this.uploadingFiles[fileIndex].status = status;
        
        // 更新isUploading状态 - 只有状态为UPLOADING或PARSING的文件才算正在上传中
        // 其他状态（包括UPLOAD_ERROR, PARSE_SUCCESS等）都不算上传中
        this.isUploading = this.uploadingFiles.some(f => 
          f.status === 'UPLOADING' || f.status === 'PARSING'
        );
      }
    },
    
    // 移除上传文件
    removeUploadingFile(fileId) {
      const fileIndex = this.uploadingFiles.findIndex(f => f.id === fileId);
      if (fileIndex !== -1) {
        this.uploadingFiles.splice(fileIndex, 1);
      }
      
      // 重新检查是否还有上传中或解析中的文件
      this.isUploading = this.uploadingFiles.some(f => 
        f.status === 'UPLOADING' || f.status === 'PARSING'
      );
      
      // 清除回调
      delete this.progressCallbacks[fileId];
      delete this.completeCallbacks[fileId];
    },
    
    // 注册进度回调
    registerProgressCallback(fileId, callback) {
      this.progressCallbacks[fileId] = callback;
    },
    
    // 注册完成回调
    registerCompleteCallback(fileId, callback) {
      this.completeCallbacks[fileId] = callback;
    },
    
    // 上传文件
    async uploadFile(file, repoId = null, isUnitKnowledgeBase = false, datasetId = null) {
      // 添加到上传列表
      const fileId = this.addUploadingFile(file);
      
      try {
        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', file);
        
        // 使用upFile接口上传文件，并添加进度回调
        const response = await upFile(formData, (progressEvent) => {
          if (progressEvent.lengthComputable) {
            // 更新上传进度 - 限制在0-70%范围内，留出30%给后续处理
            const actualPercent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            const displayPercent = Math.min(Math.round(actualPercent * 0.7), 70);
            
            // 更新进度
            this.updateProgress(fileId, displayPercent);
          }
        }, repoId, window.filePermissionType || 'REPO');
        
        // 检查upFile接口返回的code是否为0，如果不是则直接显示上传失败并返回
        if (response.code !== 0) {
          console.error('upFile接口返回错误:', response.msg || '上传失败');
          throw new Error(response.msg || '文件上传失败');
        }
        
        // 检查并提取需要的数据
        let responseData = {};
        if (response.data && response.data.data) {
          if (Array.isArray(response.data.data) && response.data.data.length > 0) {
            responseData = response.data.data[0];
          } else {
            responseData = response.data.data;
          }
        }
        
        // 获取文件扩展名
        const fileExt = file.name.split('.').pop() || '';
        
        // 获取dataset_id 和 docId - 直接使用upFile接口返回的值
        const dataset_id = responseData.dataset_id || '';
        const datasetId = response.data && response.data.datasetId ? 
          response.data.datasetId : dataset_id;
        const docId = response.data && response.data.docId ? 
          response.data.docId : responseData.id;
        
        // 构建文件路径
        const filePath = `https://ragflow-4029862aab3ad76af3e3a564499a320b.zcznbj.com/document/${dataset_id}?ext=${fileExt}&prefix=document`;
        
        // 更新文件状态为解析中
        this.updateStatus(fileId, 'PARSING');
        
        // 模拟解析进度
        let progress = 70;
        const progressInterval = setInterval(() => {
          if (progress < 90) {
            progress += 0.5;
            this.updateProgress(fileId, progress);
          } else {
            clearInterval(progressInterval);
          }
        }, 300);
        
        // 获取datasetId - 从上传响应中获取
        const chunkDatasetId = response.data && response.data.datasetId ?
          response.data.datasetId : responseData.datasetId;

        // 调用chunksDoc接口处理文件，而不是chunks接口
        if (docId && chunkDatasetId) {
          try {
            // 调用chunksDoc接口处理文件
            const chunksData = {
              ids: [docId],
              datasetId: chunkDatasetId
            };

            // 导入chunksDoc函数
            const { chunksDoc } = await import('@/api/file/file.js');

            // 发送请求
            const chunksResponse = await chunksDoc(chunksData);
            
            // 清除进度定时器
            clearInterval(progressInterval);
            
            // chunksDoc接口成功，更新文件状态
            if (chunksResponse && chunksResponse.code === 0) {
              // 更新进度为100%并更新状态
              this.updateProgress(fileId, 100);
              this.updateStatus(fileId, 'PARSE_SUCCESS');
              this.uploadedCount++;
              
              // 确保立即更新isUploading状态
              this.isUploading = this.uploadingFiles.some(f => 
                f.status === 'UPLOADING' || f.status === 'PARSING'
              );
              
              // 调用完成回调
              if (this.completeCallbacks[fileId]) {
                this.completeCallbacks[fileId](true, chunksResponse);
              }
              
              // 立即移除上传文件，确保不阻止切换知识库
              this.removeUploadingFile(fileId);
              
              return {
                success: true,
                fileId: docId,
                fileName: file.name,
                filePath: filePath
              };
            } else {
              // 更新文件状态为处理失败
              this.updateProgress(fileId, 100);
              this.updateStatus(fileId, 'PARSE_FAILED');
              
              // 调用完成回调
              if (this.completeCallbacks[fileId]) {
                this.completeCallbacks[fileId](false, { message: chunksResponse.msg || '文件解析失败' });
              }
              
              // 延迟移除失败的文件
              setTimeout(() => {
                this.removeUploadingFile(fileId);
              }, 5000); // 失败状态显示5秒后移除
              
              throw new Error(chunksResponse.msg || '文件解析失败');
            }
          } catch (error) {
            console.error('chunksDoc接口处理文件失败:', error);
            
            // 更新文件状态为处理失败
            this.updateProgress(fileId, 100);
            this.updateStatus(fileId, 'PARSE_FAILED');
            
            // 调用完成回调
            if (this.completeCallbacks[fileId]) {
              this.completeCallbacks[fileId](false, { message: error.message || '文件解析失败' });
            }
            
            // 延迟移除失败的文件
            setTimeout(() => {
              this.removeUploadingFile(fileId);
            }, 5000); // 失败状态显示5秒后移除
            
            throw error;
          }
        } else {
          console.error('缺少必要的docId或datasetId参数');
          console.log('docId:', docId, 'chunkDatasetId:', chunkDatasetId);
          throw new Error('缺少必要的docId或datasetId参数');
        }
      } catch (error) {
        console.error('上传文件失败:', error);
        
        // 更新文件状态为上传失败
        this.updateStatus(fileId, 'UPLOAD_ERROR');
        
        // 调用完成回调
        if (this.completeCallbacks[fileId]) {
          this.completeCallbacks[fileId](false, error);
        }
        
        // 延迟移除上传失败的文件
        setTimeout(() => {
          this.removeUploadingFile(fileId);
        }, 5000); // 失败状态显示5秒后移除
        
        return {
          success: false,
          error: error.message || '上传失败'
        };
      }
    }
  }
});

export default useUploadStore; 