<template>
  <div>
    <!-- 清除对话确认对话框 -->
    <v-dialog v-model="showClearConfirmDialog" :max-width="$vuetify.display.smAndDown ? '95%' : '400'">
      <v-card>
        <v-card-title class="bg-warning text-white">确认清除</v-card-title>
        <v-card-text class="pa-4 pt-5">
          是否确认清除当前对话？这将删除所有聊天记录，但会保留到历史记录中。
        </v-card-text>
        <v-card-actions class="pa-4 pt-0">
          <v-spacer></v-spacer>
          <v-btn color="grey" variant="text" @click="showClearConfirmDialog = false">取消</v-btn>
          <v-btn color="warning" @click="confirmClearConversation">确认清除</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 重命名对话框 -->
    <v-overlay v-model="showRenameDialog" class="align-center justify-center" scrim="rgba(0,0,0,0.5)">
      <div class="rename-dialog">
        <v-card min-width="400" max-width="90vw">
          <v-card-title class="bg-primary text-white d-flex align-center">
            <v-icon color="white" class="mr-2">mdi-pencil</v-icon>
            重命名对话
          </v-card-title>

          <v-card-text class="pa-4">
            <v-text-field v-model="newHistoryTitle" label="请输入新名称" variant="outlined" density="comfortable" hide-details
              autofocus @keyup.enter="newHistoryTitle.trim() && confirmRename()"
              @keyup.esc="showRenameDialog = false"></v-text-field>
          </v-card-text>

          <v-card-actions class="pa-4 pt-0">
            <v-spacer></v-spacer>
            <v-btn variant="text" @click="showRenameDialog = false">取消</v-btn>
            <v-btn color="primary" @click="confirmRename" :disabled="!newHistoryTitle.trim()">确认</v-btn>
          </v-card-actions>
        </v-card>
      </div>
    </v-overlay>

    <!-- 删除确认对话框 -->
    <v-dialog v-model="showDeleteConfirmDialog" class="delete-dialog align-center justify-center" max-width="400"
      max-height="170" persistent>
      <v-card class="delete-dialog-card">
        <v-card-title class="bg-error text-white">确认删除</v-card-title>
        <v-card-text class="pa-4 pt-5">
          是否确认删除该历史记录？此操作不可恢复。
        </v-card-text>
        <v-card-actions class="pa-4 pt-0">
          <v-spacer></v-spacer>
          <v-btn variant="text" @click="showDeleteConfirmDialog = false">取消</v-btn>
          <v-btn color="error" @click="confirmDelete">确认删除</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 清空历史记录确认对话框 -->
    <v-dialog v-model="showClearHistoryConfirmDialog" class="delete-dialog align-center justify-center" max-width="400"
      max-height="170" persistent>
      <v-card class="delete-dialog-card">
        <v-card-title class="bg-warning text-white">确认清空</v-card-title>
        <v-card-text class="pa-4 pt-5">
          是否确认清空所有历史记录？此操作不可恢复。
        </v-card-text>
        <v-card-actions class="pa-4 pt-0">
          <v-spacer></v-spacer>
          <v-btn variant="text" @click="showClearHistoryConfirmDialog = false">取消</v-btn>
          <v-btn color="warning" @click="confirmClearHistory">确认清空</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 上传对话框 -->
    <v-dialog v-model="showUploadDialog" max-width="600px" persistent>
      <v-card class="upload-dialog">
        <div class="dialog-header">
          <div class="dialog-title">上传文件</div>
          <v-btn icon variant="text" @click="showUploadDialog = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </div>
        <div class="dialog-content">
          <div class="file-upload-area" :class="{ 'drag-over': isDragOver }" @dragover="handleDragOver"
            @dragleave="handleDragLeave" @drop="handleDrop" @click="triggerFileInput">
            <input ref="fileInput" type="file" style="display: none" @change="handleFileInputChange" />
            <v-icon size="64" color="grey">mdi-cloud-upload-outline</v-icon>
            <div class="upload-text">
              <p>点击或拖拽文件到此处上传</p>
              <p class="upload-hint">支持单个文件上传</p>
              <p class="upload-hint" style="font-size: 12px; color: #666;">
                支持的文件格式：docx, xlsx, xls, ppt, pptx, pdf, txt, jpeg, jpg, png, tif, gif, csv, json, eml, html, doc
              </p>
            </div>
          </div>

          <!-- 已选文件信息 -->
          <div v-if="selectedFiles.length > 0" class="selected-file-info">
            <div class="selected-file">
              <v-icon color="primary" size="small">mdi-file-outline</v-icon>
              <span class="file-name">{{ selectedFiles[0].name }}</span>
              <span class="file-size">{{ formatFileSize(selectedFiles[0].size) }}</span>
              <v-btn icon variant="text" size="small" @click="clearSelectedFiles()">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </div>
          </div>
          
          <!-- 添加文件权限选择 -->
          <div class="permission-selection" style="margin-top: 16px;margin-left: 20px;">
            <div class="permission-title" style="margin-bottom: 10px; font-weight: 600; color: #333;">文件权限</div>
            <div class="permission-options" style="display: flex; gap: 24px;">
              <!-- 只有当用户有部门ID且已加入知识库时才显示"私密"选项 -->
              <label v-if="userStore.users && userStore.users.deptId && isJoin" style="display: flex; align-items: center; cursor: pointer;">
                <input type="radio" v-model="filePermissionType" value="REPO" style="margin-right: 10px; accent-color: #F34343;">
                <span style="color: #F34343; font-weight: 500;">私密（仅部门可见）</span>
              </label>
              <label style="display: flex; align-items: center; cursor: pointer;">
                <input type="radio" v-model="filePermissionType" value="ALL" style="margin-right: 10px; accent-color: #0288D1;">
                <span style="color: #0288D1; font-weight: 500;">共享（单位内可见）</span>
              </label>
            </div>
          </div>
        </div>

        <div class="dialog-footer">
          <v-btn variant="outlined" @click="showUploadDialog = false" class="cancel-btn">取消</v-btn>
          <v-btn color="#f5f5f5" @click="uploadFiles" class="confirm-btn"
            :disabled="selectedFiles.length == 0">开始上传处理</v-btn>
        </div>
      </v-card>
    </v-dialog>

    <!-- 文档切片对话框 -->
    <el-dialog
      v-model="showSlicingDialog"
      :title="`文档切片(${sectionValname})`"
      width="500px"
      :close-on-click-modal="false"
      :append-to-body="true"
      :destroy-on-close="true"
      class="slicing-dialog-container"
      align-center
      :modal-class="'slicing-dialog-modal'"
      top="40vh"
    >
      <div class="dialog-content">
        <p class="mb-2">选择切片解析器：</p>
        <el-select
          v-model="selectedParser"
          placeholder="请选择解析器"
          size="large"
          style="width: 350px"
          @change="handleParserChange"
        >
          <el-option
            v-for="item in parserOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <div v-if="loadingParsers" class="loading-text">加载中...</div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showSlicingDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmSlicing" :disabled="selectedParser === '' || processingSlicing">
            {{ processingSlicing ? '处理中...' : '确定' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- Toast通知 -->
    <div v-if="toast.show" class="toast-container">
      <div class="toast" :class="toast.type">
        <v-icon size="small" color="white" class="mr-2">
          {{ 
            toast.type === 'success' ? 'mdi-check-circle' : 
            toast.type === 'error' ? 'mdi-alert-circle' : 
            toast.type === 'warning' ? 'mdi-alert' : 
            'mdi-information' 
          }}
        </v-icon>
        {{ toast.message }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { formatFileSize } from '@/utils/fileUtils'

// Props
const props = defineProps({
  showClearConfirmDialog: {
    type: Boolean,
    default: false
  },
  showRenameDialog: {
    type: Boolean,
    default: false
  },
  newHistoryTitle: {
    type: String,
    default: ''
  },
  showDeleteConfirmDialog: {
    type: Boolean,
    default: false
  },
  showClearHistoryConfirmDialog: {
    type: Boolean,
    default: false
  },
  showUploadDialog: {
    type: Boolean,
    default: false
  },
  isDragOver: {
    type: Boolean,
    default: false
  },
  selectedFiles: {
    type: Array,
    default: () => []
  },
  filePermissionType: {
    type: String,
    default: 'ALL'
  },
  userStore: {
    type: Object,
    default: () => ({})
  },
  isJoin: {
    type: Boolean,
    default: false
  },
  showSlicingDialog: {
    type: Boolean,
    default: false
  },
  sectionValname: {
    type: String,
    default: ''
  },
  selectedParser: {
    type: String,
    default: ''
  },
  parserOptions: {
    type: Array,
    default: () => []
  },
  loadingParsers: {
    type: Boolean,
    default: false
  },
  processingSlicing: {
    type: Boolean,
    default: false
  },
  toast: {
    type: Object,
    default: () => ({ show: false, message: '', type: 'success' })
  }
})

// Emits
const emit = defineEmits([
  'update:showClearConfirmDialog',
  'update:showRenameDialog',
  'update:newHistoryTitle',
  'update:showDeleteConfirmDialog',
  'update:showClearHistoryConfirmDialog',
  'update:showUploadDialog',
  'update:isDragOver',
  'update:selectedFiles',
  'update:filePermissionType',
  'update:showSlicingDialog',
  'update:selectedParser',
  'confirmClearConversation',
  'confirmRename',
  'confirmDelete',
  'confirmClearHistory',
  'handleDragOver',
  'handleDragLeave',
  'handleDrop',
  'triggerFileInput',
  'handleFileInputChange',
  'clearSelectedFiles',
  'uploadFiles',
  'handleParserChange',
  'confirmSlicing'
])

// Methods
const confirmClearConversation = () => {
  emit('confirmClearConversation')
}

const confirmRename = () => {
  emit('confirmRename')
}

const confirmDelete = () => {
  emit('confirmDelete')
}

const confirmClearHistory = () => {
  emit('confirmClearHistory')
}

const handleDragOver = (event) => {
  emit('handleDragOver', event)
}

const handleDragLeave = (event) => {
  emit('handleDragLeave', event)
}

const handleDrop = (event) => {
  emit('handleDrop', event)
}

const triggerFileInput = () => {
  emit('triggerFileInput')
}

const handleFileInputChange = (event) => {
  emit('handleFileInputChange', event)
}

const clearSelectedFiles = () => {
  emit('clearSelectedFiles')
}

const uploadFiles = () => {
  emit('uploadFiles')
}

const handleParserChange = () => {
  emit('handleParserChange')
}

const confirmSlicing = () => {
  emit('confirmSlicing')
}
</script>

<style scoped>
/* 对话框通用样式 */
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.dialog-content {
  padding: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

/* 重命名对话框样式 */
.rename-dialog {
  width: 100%;
  max-width: 400px;
}

/* 删除确认对话框样式 */
.delete-dialog-card {
  border-radius: 8px;
}

/* 上传对话框样式 */
.upload-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.file-upload-area {
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  margin-bottom: 16px;
  transition: all 0.3s;
}

.file-upload-area:hover {
  border-color: #409eff;
  background-color: #f5f7fa;
}

.file-upload-area.drag-over {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.upload-text {
  margin-top: 16px;
}

.upload-text p {
  margin: 4px 0;
  color: #666;
}

.upload-hint {
  font-size: 12px;
  color: #909399;
}

/* 已选文件信息样式 */
.selected-file-info {
  margin-top: 16px;
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 12px;
}

.selected-file {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-name {
  flex: 1;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #333;
}

.file-size {
  color: #909399;
  font-size: 12px;
  margin-right: 8px;
}

/* 权限选择样式 */
.permission-selection {
  margin-top: 16px;
  margin-left: 20px;
}

.permission-title {
  margin-bottom: 10px;
  font-weight: 600;
  color: #333;
}

.permission-options {
  display: flex;
  gap: 24px;
}

.permission-options label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.permission-options input[type="radio"] {
  margin-right: 10px;
}

/* 文档切片对话框样式 */
.slicing-dialog-container {
  z-index: 3000 !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slicing-dialog-container .el-dialog {
  margin: 15vh auto !important;
  position: relative;
  top: 0 !important;
  transform: none !important;
  border-radius: 8px;
}

.slicing-dialog-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.loading-text {
  color: #909399;
  font-size: 14px;
  margin-top: 8px;
  text-align: center;
}

/* Toast通知样式 */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
}

.toast {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 6px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideInRight 0.3s ease-out;
}

.toast.success {
  background-color: #4caf50;
}

.toast.error {
  background-color: #f44336;
}

.toast.warning {
  background-color: #ff9800;
}

.toast.info {
  background-color: #2196f3;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 按钮样式 */
.cancel-btn {
  color: #666 !important;
  border-color: #ddd !important;
}

.confirm-btn {
  background-color: #1677ff !important;
  color: white !important;
}

.confirm-btn:disabled {
  background-color: #ccc !important;
  color: #999 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dialog-header,
  .dialog-content,
  .dialog-footer {
    padding: 12px 16px;
  }

  .file-upload-area {
    padding: 20px 16px;
  }

  .permission-options {
    flex-direction: column;
    gap: 12px;
  }

  .toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
  }

  .toast {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .rename-dialog {
    margin: 0 16px;
  }

  .upload-text p {
    font-size: 14px;
  }

  .upload-hint {
    font-size: 11px;
  }
}
</style>
