import axios from 'axios'
import { ElNotification, ElMessageBox, ElMessage, ElLoading } from 'element-plus'
import { getToken } from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import { tansParams, blobValidate } from '@/utils/ruoyi'
import cache from '@/plugins/cache'
import { saveAs } from 'file-saver'
import useUserStore from '@/store/modules/user'

let downloadLoadingInstance;
// 是否显示重新登录
export let isRelogin = { show: false };

// 导出 baseURL，供其他页面使用
export const baseURL = 'https://kb.zcznbj.cn/api/';

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  // baseURL: 'https://ordos.zcznbj.cn/api/',
  baseURL: baseURL,  //知识库后台接口地址
  // 超时
  timeout: 5000000,
  withCredentials: true
})

// request拦截器
service.interceptors.request.use(config => {
  // 是否需要设置 token
  const isToken = (config.headers || {}).isToken === false
  // 是否需要防止数据重复提交
  const isRepeatSubmit = (config.headers || {}).repeatSubmit === false
  if (getToken() && !isToken) {
    config.headers['tenant'] = '0'
    config.headers['Authorization'] = 'Bearer ' + getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
  }
  // get请求映射params参数
  if (config.method === 'get' && config.params) {
    let url = config.url + '?' + tansParams(config.params);
    url = url.slice(0, -1);
    config.params = {};
    config.url = url;
  }
  if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
    const requestObj = {
      url: config.url,
      data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
      time: new Date().getTime()
    }
    const requestSize = Object.keys(JSON.stringify(requestObj)).length; // 请求数据大小
    const limitSize = 5 * 1024 * 1024; // 限制存放数据5M
    if (requestSize >= limitSize) {
      console.warn(`[${config.url}]: ` + '请求数据大小超出允许的5M限制，无法进行防重复提交验证。')
      return config;
    }
    const sessionObj = cache.session.getJSON('sessionObj')
    if (sessionObj === undefined || sessionObj === null || sessionObj === '') {
      cache.session.setJSON('sessionObj', requestObj)
    } else {
      const s_url = sessionObj.url;                // 请求地址
      const s_data = sessionObj.data;              // 请求数据
      const s_time = sessionObj.time;              // 请求时间
      const interval = 1000;                       // 间隔时间(ms)，小于此时间视为重复提交
      if (s_data === requestObj.data && requestObj.time - s_time < interval && s_url === requestObj.url) {
        // const message = '数据正在处理，请勿重复提交';
        // console.warn(`[${s_url}]: ` + message)
        // return Promise.reject(new Error(message))
      } else {
        cache.session.setJSON('sessionObj', requestObj)
      }
    }
  }
  return config
}, error => {
  console.log(error)
  Promise.reject(error)
})

// 响应拦截器
service.interceptors.response.use(res => {
  // 未设置状态码则默认成功状态
  const code = res.data.code || 200;
  // 获取错误信息
  const msg = errorCode[code] || res.data.msg || errorCode['default']
  // 二进制数据则直接返回
  if (res.request.responseType === 'blob' || res.request.responseType === 'arraybuffer') {
    return res.data
  }
  if (code == 401) {
    // 检查当前路径，如果已经在登录页面就不再显示提示
    const isLoginPage = location.pathname == '/' || location.pathname == '/login' || location.pathname == '/index';
    
    if (!isRelogin.show && !isLoginPage) {
      isRelogin.show = true;
      // 创建并分发自定义的未授权事件
      window.dispatchEvent(new CustomEvent('unauthorized'));
      
      // 直接清除登录状态，不显示弹窗
      useUserStore().logOut().then(() => {
        location.href = '/';
        isRelogin.show = false;
      }).catch(() => {
        isRelogin.show = false;
      });
    } else {
      // 已经在登录页面或弹窗已显示，直接清除登录状态
      useUserStore().logOut();
    }
    return Promise.reject('无效的会话，或者会话已过期，请重新登录。')
  } else if (code === 500) {
    ElMessage({ message: msg, type: 'error' })
    return Promise.reject(new Error(msg))
  } else if (code === 601) {
    ElMessage({ message: msg, type: 'warning' })
    return Promise.reject(new Error(msg))
  } else if (code === 10002) {
    // 第三方登录错误提示，原有弹窗已移除，避免与前面页面冲突
    useUserStore().logOut().then(() => {
      location.href = '/index';
    });
    return Promise.reject(new Error(msg))
  } else if (code === -1) {
    // code为-1时不弹出提示
    return Promise.reject('error')
  } else if (code !== 200) {
    // ElNotification.error({ title: msg })
    return Promise.reject('error')
  } else {
    return Promise.resolve(res.data)
  }
},
  error => {
    console.log('err' + error)
    let { message } = error;
    if (message == "Network Error") {
      message = "后端接口连接异常";
    } else if (message.includes("timeout")) {
      message = "系统接口请求超时";
    } else if (message.includes("Request failed with status code")) {
      const statusCode = message.substr(message.length - 3);
      message = "系统接口" + statusCode + "异常";

      // 处理401未授权响应错误
      if (statusCode === "401") {
        // 检查当前路径，如果不是登录页面才分发事件
        const isLoginPage = location.pathname === '/' || location.pathname === '/login' || location.pathname === '/index';
        if (!isRelogin.show && !isLoginPage) {
          isRelogin.show = true;
          window.dispatchEvent(new CustomEvent('unauthorized'));
          // 直接清除登录状态并重定向
          useUserStore().logOut().then(() => {
            location.href = '/';
            isRelogin.show = false;
          }).catch(() => {
            isRelogin.show = false;
          });
          return Promise.reject(error); // 提前返回，不显示错误消息
        } else {
          // 已经在登录页面或弹窗已显示，直接清除登录状态
          useUserStore().logOut();
          return Promise.reject(error); // 提前返回，不显示错误消息
        }
      }
    }
    ElMessage({ message: message, type: 'error', duration: 5 * 1000 })
    return Promise.reject(error)
  }
)

// 通用下载方法
export function download(url, params, filename, config) {
  if (!params || !filename) {
    //此处是为了适配若依框架以前的导出下载方法，以前下载方法内容见上面注释内容：
    var fileName = url;
    this.$download.name(fileName);
    return null;
  }
  downloadLoadingInstance = ElLoading.service({ text: "正在下载数据，请稍候", background: "rgba(0, 0, 0, 0.7)", })
  return service.post(url, params, {
    transformRequest: [(params) => { return tansParams(params) }],
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    responseType: 'blob',
    ...config
  }).then(async (data) => {
    const isBlob = blobValidate(data);
    if (isBlob) {
      const blob = new Blob([data])
      saveAs(blob, filename)
    } else {
      const resText = await data.text();
      const rspObj = JSON.parse(resText);
      const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
      ElMessage.error(errMsg);
    }
    downloadLoadingInstance.close();
  }).catch((r) => {
    console.error(r)
    ElMessage.error('下载文件出现错误，请联系管理员！')
    downloadLoadingInstance.close();
  })
}

export default service
