<template>
  <div class="footer">
    <div class="footer-content">
      <div class="copyright">© 2025 北京潮星信息技术有限公司 All rights reserved.</div>
      <div class="contact-links">
        <a href="mailto:<EMAIL>" class="contact-link">
          <v-icon size="small">mdi-email-outline</v-icon>
          <span>Email</span>
        </a>
        <a href="tel:+8610123456" class="contact-link">
          <v-icon size="small">mdi-phone</v-icon>
          <span>电话</span>
        </a>
        <a href="#" class="contact-link">
          <v-icon size="small">mdi-map-marker</v-icon>
          <span>地址</span>
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
// 无需额外逻辑
</script>

<style scoped>
.footer {
  width: 100%;
  background-color: var(--card-background);
  border-top: 1px solid var(--border-color);
  padding: 24px 0;
  position: relative;
  z-index: 10;
  transition: all 0.3s ease;
  box-shadow: 0 -1px 6px rgba(0, 0, 0, 0.05);
}

.footer-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  font-size: 14px;
  color: var(--text-secondary);
}

.contact-links {
  display: flex;
  gap: 36px;
}

.contact-link {
  display: flex;
  align-items: center;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 14px;
  padding: 6px 10px;
  border-radius: 4px;
}

.contact-link:hover {
  color: var(--accent-color);
  background-color: rgba(0, 0, 0, 0.03);
  transform: translateY(-2px);
}

.contact-link .v-icon {
  margin-right: 8px;
  opacity: 0.85;
  font-size: 18px;
}

.copyright {
  font-weight: 500;
}

@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    gap: 20px;
    padding: 0 16px;
  }
  
  .copyright {
    text-align: center;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: 20px 0;
  }
  
  .contact-links {
    flex-direction: column;
    gap: 12px;
    align-items: center;
  }
  
  .contact-link {
    font-size: 13px;
  }
}
</style> 