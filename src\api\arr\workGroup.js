import request from '@/utils/request'

// 查询排班护理组列表
export function listWorkGroup(query) {
  return request({
    url: '/arr/workGroup/list',
    method: 'get',
    params: query
  })
}

// 查询排班护理组详细
export function getWorkGroup(id) {
  return request({
    url: '/arr/workGroup/' + id,
    method: 'get'
  })
}

// 新增排班护理组
export function addWorkGroup(data) {
  return request({
    url: '/arr/workGroup',
    method: 'post',
    data: data
  })
}

// 修改排班护理组
export function updateWorkGroup(data) {
  return request({
    url: '/arr/workGroup/edit',
    method: 'post',
    data: data
  })
}

// 删除排班护理组
export function delWorkGroup(id) {
  return request({
    url: '/arr/workGroup/del/' + id,
    method: 'post'
  })
}

// 查询添加人员弹窗列表接口
export function listPermi(query) {
  return request({
    url: '/hr/staffInfo/listPermi',
    method: 'get',
    params: query
  })
}

// 选择用户后点击确定保存接口（保存用户小组信息
export function addGroup(data) {
  return request({
    url: '/hr/staffInfo/addGroup',
    method: 'post',
    data: data
  })
}


// 删除用户
export function emptyGroup(data) {
  return request({
    url: '/hr/staffInfo/emptyGroup',
    method: 'post',
    data: data
  })
}