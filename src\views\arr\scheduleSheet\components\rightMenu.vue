<template>
  <div class="app_content">
    <!-- <div class="menu_title">班种列表</div> -->
    <div class="app_box">
      <div v-for="(item, index) in tree" :key="index">
        <div class="ban_content" @click="selButtonItem(item)" v-if="item.flag">
          {{ item.label }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { watch } from "vue";

const { proxy } = getCurrentInstance();
const emits = defineEmits(["rightMenuFun"]);
const props = defineProps({
  dates: {
    type: String,
  },
  showPaste: {
    type: Boolean,
    value: false,
  },
  showPasteAll: {
    type: Boolean,
    value: false,
  },
  classesList:{
    type: Array,
  }
});
// 右键之后显示的菜单
const tree = ref([
  {
    key: "copy",
    label: "复制",
    flag: true,
  },
  {
    key: "paste",
    label: "粘贴",
    flag: false,
  },
  {
    key: "copyAll",
    label: "复制整行",
    flag: true,
  },
  {
    key: "pasteAll",
    label: "粘贴整行",
    flag: false,
  },
  {
    key: "rest",
    label: "休",
    flag: true,
  },
  {
    key: "holiday",
    label: "公休",
    flag: true,
  },
]);


// 监听时间范围(dates)的变化，有变化则计算遍历始末日期间的所有日期
watch(
  () => [props.showPaste, props.showPasteAll,props.classesList],
  (newValue, oldValue) => {
    if (newValue) {
      tree.value[1].flag = newValue[0];
      tree.value[3].flag = newValue[1];

      if(newValue[2]){
        tree.value.map(item=>{
          newValue[2].map(child=>{
            if(item.label == child.shiftsName){
              item.shiftsName = child.shiftsName
              item.shiftsId = child.id
              item.shiftsColor = child.color
              item.duration = child.duration
              item.endTime = child.endTime
              item.startTime = child.startTime
            }
          })
        })
      }
    }
  }
);

const selButtonItem = (item) => {
  emits("rightMenuFun", item);
};
</script>

<style lang="scss" scoped>
.app_content {
  width: 100px;
  height: auto;
  padding: 0px 8px;
  background-color: #fff;
  border-radius: 10px;
  z-index: 9;
  box-shadow: 0px 2px 10px 2px rgba(0, 0, 0, 0.15), 0px 4px 5px 0px rgba(0, 0, 0, 0.12);
  overflow-y: auto;
  .app_box {
    width: 100%;
    .app_box:hover {
      // cursor: pointer;
      background-color: #f5f7fa;
    }
  }

  .ban_content {
    width: 100%;
    height: auto;
    font-size: 16px;
    padding: 2px 6px;
    margin: 14px 0;
    border-radius: 4px;
  }
  .ban_content:hover {
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.08);
  }
}
</style>
