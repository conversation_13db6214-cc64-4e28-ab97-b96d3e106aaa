<template>
  <div class="left-sidebar" :class="{ 'open': isOpen }">
    <!-- 知识库导航菜单 -->
    <div class="sidebar-menu">
      <div class="menu-item" :class="{ 'active': activeMenuGroup == 'unit' && activeMenuItem == 'all' }">
        <div class="menu-header">
          <div class="menu-header-content" @click="selectMenuItem('unit', 'all')">
            <v-icon size="small" :color="activeMenuGroup == 'unit' && activeMenuItem == 'all' ? 'orange' : 'grey-darken-1'"
              class="mr-2">mdi-folder</v-icon>
            <span :class="{ 'orange-text': activeMenuGroup == 'unit' && activeMenuItem == 'all' }">单位知识库(全部)</span>
          </div>
          <v-icon size="small" :color="activeMenuGroup == 'unit' && activeMenuItem == 'all' ? 'orange' : 'grey-darken-1'"
            class="ml-auto collapse-icon" :class="{ 'collapsed': !menuExpanded.unit }"
            @click.stop="toggleMenu('unit')">
            mdi-chevron-down
          </v-icon>
        </div>
        <div class="submenu" v-show="menuExpanded.unit">
          <!-- 使用v-for循环动态渲染从API获取的知识库列表 -->
          <div v-for="repo in repoList" :key="repo.repoId" class="submenu-item"
            :class="{ 'active': activeMenuGroup == 'unit' && activeMenuItem == 'repo_' + repo.repoId }"
            @click="selectMenuItem('unit', 'repo_' + repo.repoId, repo)"
            @contextmenu.prevent="printRepoDetails(repo)">
            <div class="submenu-item-content">
              <span style="font-size: 15px;">{{ repo.repoDesc }} 
                <span style="font-size: 15px;" :style="{ color: activeMenuGroup == 'unit' && activeMenuItem == 'repo_' + repo.repoId ? 'orange' : '#999' }">
                  {{ repo.isJoined ? '(我加入的)' : '' }}
                </span>
              </span>
              <v-icon size="small" v-if="hasKnowledgeMemberPermission || repo.operationPermission == 'EDIT'"
                :color="activeMenuGroup == 'unit' && activeMenuItem == 'repo_' + repo.repoId ? 'orange' : 'grey-darken-1'"
                @click.stop="openMemberDrawer(repo.repoDesc)">
                mdi-account-settings-outline
              </v-icon>
            </div>
          </div>

          <!-- 静态菜单项作为备用，如果API暂未返回数据 -->
          <div v-if="repoList.length == 0" class="submenu-item"
            :class="{ 'active': activeMenuGroup == 'unit' && activeMenuItem === 'create' }">
            <div class="submenu-item-content">
              <span>暂无知识库</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 历史记录面板 -->
    <div class="history-sidebar">
      <div class="history-header">
        <div class="d-flex align-center justify-space-between w-100">
          <div class="d-flex align-center">
            <v-icon color="primary" class="mr-2">mdi-history</v-icon>
            <span class="font-weight-medium">历史记录</span>
          </div>
          <div class="d-flex">
            <v-btn variant="text" density="comfortable" icon size="small" color="error"
              @click="!isProcessing && clearHistory()"
              :disabled="chatHistory.length == 0 || isProcessing"
              :class="{ 'unselectable-btn': isProcessing }">
              <v-icon>mdi-delete-sweep</v-icon>
            </v-btn>
          </div>
        </div>
      </div>

      <div v-if="chatHistory.length == 0" class="text-center pa-4 text-body-1 text-medium-emphasis">
        暂无历史记录
      </div>

      <div v-else class="history-groups">
        <!-- 7天内的历史 -->
        <div class="history-group" v-if="chatHistory.recent7Days && chatHistory.recent7Days.length > 0">
          <div class="history-group-title" @click="toggleGroup('recent')">
            <div class="d-flex align-center flex-grow-1">
              <v-icon size="x-small" color="grey-darken-1" class="mr-2">mdi-calendar-range</v-icon>
              <div style="width: 50px;">近7天内</div>
              <div class="history-count">({{ chatHistory.recent7Days.length }})</div>
            </div>
            <v-icon size="small" color="grey-darken-1" class="collapse-icon"
              :class="{ 'collapsed': !groupExpanded.recent }">
              mdi-chevron-down
            </v-icon>
          </div>
          <div class="history-items" v-show="groupExpanded.recent">
            <div v-for="(history, index) in chatHistory.recent7Days" :key="index"
              @click="!isProcessing && loadHistoryConversation(history)"
              class="history-item"
              :class="{
                'selected-history': (selectedHistoryIndex === 0 && index === 0) || selectedHistoryIndex == getHistoryFullIndex(history),
                'disabled-history': isProcessing
              }">
              <!-- 左侧内容区域，点击时触发加载历史记录 -->
              <div class="history-item-main" @click="!isProcessing && loadHistoryConversation(history)">
                <v-avatar size="28" :color="history.mode?.value == 'v2' ? 'green-lighten-5' : 'blue-lighten-5'"
                  class="history-avatar">
                  <v-icon size="small" :color="history.mode?.value == 'v2' ? 'green-darken-1' : 'blue-darken-1'">
                    {{ history.mode?.value == 'v2' ? 'mdi-chart-bar' : 'mdi-message-text-outline' }}
                  </v-icon>
                </v-avatar>
                <div class="history-text">
                  <div class="history-title">{{ getHistoryTitle(history) }}</div>
                  <div class="history-date">
                    <v-icon size="x-small" color="grey-darken-1">mdi-clock-outline</v-icon>
                    <span>{{ formatHistoryDate(history.lastMessageTime || history.createTime) }}</span>
                  </div>
                </div>
              </div>
              <!-- 右侧三点菜单按钮 -->
              <el-dropdown size="large" trigger="click" @command="handleCommand" :disabled="isProcessing">
                <button class="history-menu-btn"
                  @click.stop="!isProcessing && openHistoryMenu($event, history)"
                  :class="{ 'unselectable-btn': isProcessing }">
                  <v-icon size="small">mdi-dots-vertical</v-icon>
                </button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{ type: 'delete', history }">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>

        <!-- 30天内的历史 -->
        <div class="history-group" v-if="chatHistory.recent30Days && chatHistory.recent30Days.length > 0">
          <div class="history-group-title" @click="toggleGroup('older')">
            <div class="d-flex align-center flex-grow-1">
              <v-icon size="x-small" color="grey-darken-1" class="mr-2">mdi-calendar-month</v-icon>
              <span>30天内</span>
              <span class="history-count">({{ chatHistory.recent30Days.length }})</span>
            </div>
            <v-icon size="small" color="grey-darken-1" class="collapse-icon"
              :class="{ 'collapsed': !groupExpanded.older }">
              mdi-chevron-down
            </v-icon>
          </div>
          <div class="history-items" v-show="groupExpanded.older">
            <div v-for="(history, index) in chatHistory.recent30Days" :key="index"
              @click="!isProcessing && loadHistoryConversation(history)"
              class="history-item"
              :class="{
                'selected-history': selectedHistoryIndex == getHistoryFullIndex(history),
                'disabled-history': isProcessing
              }">
              <!-- 左侧内容区域，点击时触发加载历史记录 -->
              <div class="history-item-main" @click="!isProcessing && loadHistoryConversation(history)">
                <v-avatar size="28" :color="history.mode?.value === 'v2' ? 'green-lighten-5' : 'blue-lighten-5'"
                  class="history-avatar">
                  <v-icon size="small" :color="history.mode?.value === 'v2' ? 'green-darken-1' : 'blue-darken-1'">
                    {{ history.mode?.value === 'v2' ? 'mdi-chart-bar' : 'mdi-message-text-outline' }}
                  </v-icon>
                </v-avatar>
                <div class="history-text">
                  <div class="history-title">{{ getHistoryTitle(history) }}</div>
                  <div class="history-date">
                    <v-icon size="x-small" color="grey-darken-1">mdi-clock-outline</v-icon>
                    <span>{{ formatHistoryDate(history.lastMessageTime || history.createTime) }}</span>
                  </div>
                </div>
              </div>
              <!-- 右侧三点菜单按钮 -->
              <el-dropdown size="large" trigger="click" @command="handleCommand">
                <button class="history-menu-btn" @click.stop="openHistoryMenu($event, history)">
                  <v-icon size="small">mdi-dots-vertical</v-icon>
                </button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{ type: 'delete', history }">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
        
        <!-- 30天前的历史 -->
        <div class="history-group" v-if="chatHistory.before30Days && chatHistory.before30Days.length > 0">
          <div class="history-group-title" @click="toggleGroup('oldest')">
            <div class="d-flex align-center flex-grow-1">
              <v-icon size="x-small" color="grey-darken-1" class="mr-2">mdi-calendar-clock</v-icon>
              <span>30天前</span>
              <span class="history-count">({{ chatHistory.before30Days.length }})</span>
            </div>
            <v-icon size="small" color="grey-darken-1" class="collapse-icon"
              :class="{ 'collapsed': !groupExpanded.oldest }">
              mdi-chevron-down
            </v-icon>
          </div>
          <div class="history-items" v-show="groupExpanded.oldest">
            <div v-for="(history, index) in chatHistory.before30Days" :key="index"
              @click="!isProcessing && loadHistoryConversation(history)"
              class="history-item"
              :class="{
                'selected-history': selectedHistoryIndex == getHistoryFullIndex(history),
                'disabled-history': isProcessing
              }">
              <!-- 左侧内容区域，点击时触发加载历史记录 -->
              <div class="history-item-main" @click="!isProcessing && loadHistoryConversation(history)">
                <v-avatar size="28" :color="history.mode?.value === 'v2' ? 'green-lighten-5' : 'blue-lighten-5'"
                  class="history-avatar">
                  <v-icon size="small" :color="history.mode?.value === 'v2' ? 'green-darken-1' : 'blue-darken-1'">
                    {{ history.mode?.value === 'v2' ? 'mdi-chart-bar' : 'mdi-message-text-outline' }}
                  </v-icon>
                </v-avatar>
                <div class="history-text">
                  <div class="history-title">{{ getHistoryTitle(history) }}</div>
                  <div class="history-date">
                    <v-icon size="x-small" color="grey-darken-1">mdi-clock-outline</v-icon>
                    <span>{{ formatHistoryDate(history.lastMessageTime || history.createTime) }}</span>
                  </div>
                </div>
              </div>
              <!-- 右侧三点菜单按钮 -->
              <el-dropdown size="large" trigger="click" @command="handleCommand">
                <button class="history-menu-btn" @click.stop="openHistoryMenu($event, history)">
                  <v-icon size="small">mdi-dots-vertical</v-icon>
                </button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{ type: 'delete', history }">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { formatHistoryDate, getHistoryTitle, getHistoryFullIndex } from '@/utils/chatUtils'

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  repoList: {
    type: Array,
    default: () => []
  },
  activeMenuGroup: {
    type: String,
    default: ''
  },
  activeMenuItem: {
    type: String,
    default: ''
  },
  menuExpanded: {
    type: Object,
    default: () => ({ unit: true })
  },
  chatHistory: {
    type: Object,
    default: () => ({
      recent7Days: [],
      recent30Days: [],
      before30Days: []
    })
  },
  groupExpanded: {
    type: Object,
    default: () => ({
      recent: true,
      older: true,
      oldest: true
    })
  },
  selectedHistoryIndex: {
    type: Number,
    default: -1
  },
  isProcessing: {
    type: Boolean,
    default: false
  },
  hasKnowledgeMemberPermission: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'selectMenuItem',
  'toggleMenu',
  'printRepoDetails',
  'openMemberDrawer',
  'clearHistory',
  'toggleGroup',
  'loadHistoryConversation',
  'handleCommand',
  'openHistoryMenu'
])

// Methods
const selectMenuItem = (group, item, repo) => {
  emit('selectMenuItem', group, item, repo)
}

const toggleMenu = (menu) => {
  emit('toggleMenu', menu)
}

const printRepoDetails = (repo) => {
  emit('printRepoDetails', repo)
}

const openMemberDrawer = (repoDesc) => {
  emit('openMemberDrawer', repoDesc)
}

const clearHistory = () => {
  emit('clearHistory')
}

const toggleGroup = (group) => {
  emit('toggleGroup', group)
}

const loadHistoryConversation = (history) => {
  emit('loadHistoryConversation', history)
}

const handleCommand = (command) => {
  emit('handleCommand', command)
}

const openHistoryMenu = (event, history) => {
  emit('openHistoryMenu', event, history)
}
</script>

<style scoped>
/* 左侧边栏样式 */
.left-sidebar {
  width: 300px;
  height: 100vh;
  background-color: #f8f9fa;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  position: fixed;
  left: -300px;
  top: 0;
  z-index: 1000;
  transition: left 0.3s ease;
}

.left-sidebar.open {
  left: 0;
}

/* 知识库导航菜单样式 */
.sidebar-menu {
  flex: 0 0 auto;
  border-bottom: 1px solid #e0e0e0;
  background-color: #fff;
}

.menu-item {
  border-bottom: 1px solid #f0f0f0;
}

.menu-item.active {
  background-color: #fff3e0;
}

.menu-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.menu-header:hover {
  background-color: #f5f5f5;
}

.menu-header-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.orange-text {
  color: #ff9800 !important;
}

.collapse-icon {
  transition: transform 0.3s ease;
}

.collapse-icon.collapsed {
  transform: rotate(-90deg);
}

.submenu {
  background-color: #fafafa;
  border-top: 1px solid #e0e0e0;
}

.submenu-item {
  padding: 8px 16px 8px 40px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f0f0f0;
}

.submenu-item:hover {
  background-color: #f0f0f0;
}

.submenu-item.active {
  background-color: #fff3e0;
}

.submenu-item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 历史记录面板样式 */
.history-sidebar {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.history-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #fff;
}

.history-groups {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.history-group {
  margin-bottom: 8px;
}

.history-group-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  cursor: pointer;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  font-size: 13px;
  color: #666;
}

.history-group-title:hover {
  background-color: #eeeeee;
}

.history-count {
  margin-left: 4px;
  color: #999;
}

.history-items {
  background-color: #fff;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.history-item:hover {
  background-color: #f8f9fa;
}

.history-item.selected-history {
  background-color: #e3f2fd;
  border-left: 3px solid #2196f3;
}

.history-item.disabled-history {
  opacity: 0.6;
  cursor: not-allowed;
}

.history-item-main {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 8px;
}

.history-avatar {
  flex-shrink: 0;
}

.history-text {
  flex: 1;
  min-width: 0;
}

.history-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.history-date {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #666;
}

.history-menu-btn {
  background: none;
  border: none;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  opacity: 0.7;
  transition: all 0.2s;
}

.history-menu-btn:hover {
  background-color: #f0f0f0;
  opacity: 1;
}

.history-menu-btn.unselectable-btn {
  opacity: 0.4;
  cursor: not-allowed;
}

.unselectable-btn {
  opacity: 0.4;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .left-sidebar {
    position: relative;
    left: 0;
  }
}

@media (max-width: 767px) {
  .left-sidebar {
    width: 280px;
    left: -280px;
  }

  .left-sidebar.open {
    left: 0;
  }
}
</style>
