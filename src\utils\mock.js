// 模拟数据
export const mockUsers = [
  {
    userId: 1,
    userName: 'admin',
    nickName: '管理员',
    email: '<EMAIL>',
    phonenumber: '13800138000',
    status: '0',
    createTime: '2023-01-01',
    dept: { deptName: '研发部门' },
    roles: [{ roleId: 1, roleName: '超级管理员', roleKey: 'admin' }]
  },
  {
    userId: 2,
    userName: 'zhangsan',
    nickName: '张三',
    email: '<EMAIL>',
    phonenumber: '13800138001',
    status: '0',
    createTime: '2023-01-02',
    dept: { deptName: '市场部门' },
    roles: [{ roleId: 2, roleName: '普通角色', roleKey: 'common' }]
  },
  {
    userId: 3,
    userName: 'lisi',
    nickName: '李四',
    email: '<EMAIL>',
    phonenumber: '13800138002',
    status: '0',
    createTime: '2023-01-03',
    dept: { deptName: '测试部门' },
    roles: [{ roleId: 2, roleName: '普通角色', roleKey: 'common' }]
  },
  {
    userId: 4,
    userName: 'wangwu',
    nickName: '王五',
    email: '<EMAIL>',
    phonenumber: '13800138003',
    status: '1',
    createTime: '2023-01-04',
    dept: { deptName: '财务部门' },
    roles: [{ roleId: 2, roleName: '普通角色', roleKey: 'common' }]
  },
  {
    userId: 5,
    userName: 'zhaoliu',
    nickName: '赵六',
    email: '<EMAIL>',
    phonenumber: '13800138004',
    status: '0',
    createTime: '2023-01-05',
    dept: { deptName: '运维部门' },
    roles: [{ roleId: 2, roleName: '普通角色', roleKey: 'common' }]
  }
];

export const mockRoles = [
  {
    roleId: 1,
    roleName: '超级管理员',
    roleKey: 'admin',
    roleSort: 1,
    status: '0',
    createTime: '2023-01-01',
    remark: '超级管理员'
  },
  {
    roleId: 2,
    roleName: '普通角色',
    roleKey: 'common',
    roleSort: 2,
    status: '0',
    createTime: '2023-01-01',
    remark: '普通角色'
  },
  {
    roleId: 3,
    roleName: '系统监控员',
    roleKey: 'monitor',
    roleSort: 3,
    status: '0',
    createTime: '2023-01-02',
    remark: '系统监控员'
  },
  {
    roleId: 4,
    roleName: '游客',
    roleKey: 'visitor',
    roleSort: 4,
    status: '0',
    createTime: '2023-01-03',
    remark: '游客'
  },
  {
    roleId: 5,
    roleName: '运维人员',
    roleKey: 'operation',
    roleSort: 5,
    status: '0',
    createTime: '2023-01-04',
    remark: '运维人员'
  }
];

export const mockMenus = [
  {
    menuId: 1,
    menuName: '系统管理',
    parentId: 0,
    orderNum: 1,
    path: '/system',
    component: null,
    isFrame: 1,
    isCache: 0,
    menuType: 'M',
    visible: '0',
    status: '0',
    perms: '',
    icon: 'system',
    createTime: '2023-01-01',
    children: [
      {
        menuId: 2,
        menuName: '用户管理',
        parentId: 1,
        orderNum: 1,
        path: 'user',
        component: 'system/user/index',
        isFrame: 1,
        isCache: 0,
        menuType: 'C',
        visible: '0',
        status: '0',
        perms: 'system:user:list',
        icon: 'user',
        createTime: '2023-01-01',
        children: [
          {
            menuId: 6,
            menuName: '用户查询',
            parentId: 2,
            orderNum: 1,
            path: '',
            component: '',
            isFrame: 1,
            isCache: 0,
            menuType: 'F',
            visible: '0',
            status: '0',
            perms: 'system:user:query',
            icon: '#',
            createTime: '2023-01-01'
          },
          {
            menuId: 7,
            menuName: '用户新增',
            parentId: 2,
            orderNum: 2,
            path: '',
            component: '',
            isFrame: 1,
            isCache: 0,
            menuType: 'F',
            visible: '0',
            status: '0',
            perms: 'system:user:add',
            icon: '#',
            createTime: '2023-01-01'
          },
          {
            menuId: 8,
            menuName: '用户修改',
            parentId: 2,
            orderNum: 3,
            path: '',
            component: '',
            isFrame: 1,
            isCache: 0,
            menuType: 'F',
            visible: '0',
            status: '0',
            perms: 'system:user:edit',
            icon: '#',
            createTime: '2023-01-01'
          },
          {
            menuId: 9,
            menuName: '用户删除',
            parentId: 2,
            orderNum: 4,
            path: '',
            component: '',
            isFrame: 1,
            isCache: 0,
            menuType: 'F',
            visible: '0',
            status: '0',
            perms: 'system:user:remove',
            icon: '#',
            createTime: '2023-01-01'
          }
        ]
      },
      {
        menuId: 3,
        menuName: '角色管理',
        parentId: 1,
        orderNum: 2,
        path: 'role',
        component: 'system/role/index',
        isFrame: 1,
        isCache: 0,
        menuType: 'C',
        visible: '0',
        status: '0',
        perms: 'system:role:list',
        icon: 'peoples',
        createTime: '2023-01-01',
        children: [
          {
            menuId: 10,
            menuName: '角色查询',
            parentId: 3,
            orderNum: 1,
            path: '',
            component: '',
            isFrame: 1,
            isCache: 0,
            menuType: 'F',
            visible: '0',
            status: '0',
            perms: 'system:role:query',
            icon: '#',
            createTime: '2023-01-01'
          },
          {
            menuId: 11,
            menuName: '角色新增',
            parentId: 3,
            orderNum: 2,
            path: '',
            component: '',
            isFrame: 1,
            isCache: 0,
            menuType: 'F',
            visible: '0',
            status: '0',
            perms: 'system:role:add',
            icon: '#',
            createTime: '2023-01-01'
          }
        ]
      },
      {
        menuId: 4,
        menuName: '菜单管理',
        parentId: 1,
        orderNum: 3,
        path: 'menu',
        component: 'system/menu/index',
        isFrame: 1,
        isCache: 0,
        menuType: 'C',
        visible: '0',
        status: '0',
        perms: 'system:menu:list',
        icon: 'tree-table',
        createTime: '2023-01-01'
      },
      {
        menuId: 5,
        menuName: '部门管理',
        parentId: 1,
        orderNum: 4,
        path: 'dept',
        component: 'system/dept/index',
        isFrame: 1,
        isCache: 0,
        menuType: 'C',
        visible: '0',
        status: '0',
        perms: 'system:dept:list',
        icon: 'tree',
        createTime: '2023-01-01'
      }
    ]
  },
  {
    menuId: 20,
    menuName: '系统监控',
    parentId: 0,
    orderNum: 2,
    path: '/monitor',
    component: null,
    isFrame: 1,
    isCache: 0,
    menuType: 'M',
    visible: '0',
    status: '0',
    perms: '',
    icon: 'monitor',
    createTime: '2023-01-01',
    children: [
      {
        menuId: 21,
        menuName: '在线用户',
        parentId: 20,
        orderNum: 1,
        path: 'online',
        component: 'monitor/online/index',
        isFrame: 1,
        isCache: 0,
        menuType: 'C',
        visible: '0',
        status: '0',
        perms: 'monitor:online:list',
        icon: 'online',
        createTime: '2023-01-01'
      },
      {
        menuId: 22,
        menuName: '服务监控',
        parentId: 20,
        orderNum: 2,
        path: 'server',
        component: 'monitor/server/index',
        isFrame: 1,
        isCache: 0,
        menuType: 'C',
        visible: '0',
        status: '0',
        perms: 'monitor:server:list',
        icon: 'server',
        createTime: '2023-01-01'
      }
    ]
  }
];

export const mockDepts = [
  {
    deptId: 1,
    parentId: 0,
    deptName: '总公司',
    orderNum: 1,
    leader: '张三',
    phone: '13800138000',
    email: '<EMAIL>',
    status: '0',
    createTime: '2023-01-01',
    children: [
      {
        deptId: 2,
        parentId: 1,
        deptName: '研发部门',
        orderNum: 1,
        leader: '李四',
        phone: '13800138001',
        email: '<EMAIL>',
        status: '0',
        createTime: '2023-01-01'
      },
      {
        deptId: 3,
        parentId: 1,
        deptName: '市场部门',
        orderNum: 2,
        leader: '王五',
        phone: '13800138002',
        email: '<EMAIL>',
        status: '0',
        createTime: '2023-01-01'
      },
      {
        deptId: 4,
        parentId: 1,
        deptName: '测试部门',
        orderNum: 3,
        leader: '赵六',
        phone: '13800138003',
        email: '<EMAIL>',
        status: '0',
        createTime: '2023-01-01'
      },
      {
        deptId: 5,
        parentId: 1,
        deptName: '财务部门',
        orderNum: 4,
        leader: '钱七',
        phone: '13800138004',
        email: '<EMAIL>',
        status: '0',
        createTime: '2023-01-01'
      },
      {
        deptId: 6,
        parentId: 1,
        deptName: '运维部门',
        orderNum: 5,
        leader: '孙八',
        phone: '13800138005',
        email: '<EMAIL>',
        status: '0',
        createTime: '2023-01-01'
      }
    ]
  }
];

// 模拟API响应
export function mockResponse(data, message = '操作成功', code = 200) {
  return {
    code,
    message,
    data
  };
}

// 模拟分页响应
export function mockPageResponse(data, total, message = '查询成功', code = 200) {
  return {
    code,
    message,
    data: {
      rows: data,
      total
    }
  };
}

// 模拟API请求延迟
export function delay(ms = 300) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 模拟用户API
export const userApi = {
  async list(query) {
    await delay();
    let data = [...mockUsers];
    
    // 搜索过滤
    if (query.userName) {
      data = data.filter(item => item.userName.includes(query.userName));
    }
    if (query.phonenumber) {
      data = data.filter(item => item.phonenumber.includes(query.phonenumber));
    }
    if (query.status !== undefined && query.status !== '') {
      data = data.filter(item => item.status === query.status);
    }
    
    // 分页
    const pageNum = parseInt(query.pageNum) || 1;
    const pageSize = parseInt(query.pageSize) || 10;
    const total = data.length;
    const start = (pageNum - 1) * pageSize;
    const end = start + pageSize;
    const rows = data.slice(start, end);
    
    return mockPageResponse(rows, total);
  },
  
  async get(userId) {
    await delay();
    const user = mockUsers.find(item => item.userId === parseInt(userId));
    if (user) {
      return mockResponse(user);
    } else {
      return mockResponse(null, '用户不存在', 500);
    }
  },
  
  async add(data) {
    await delay();
    const newUser = {
      ...data,
      userId: Math.max(...mockUsers.map(u => u.userId)) + 1,
      createTime: new Date().toISOString().split('T')[0]
    };
    mockUsers.push(newUser);
    return mockResponse(null);
  },
  
  async update(data) {
    await delay();
    const index = mockUsers.findIndex(item => item.userId === data.userId);
    if (index !== -1) {
      mockUsers[index] = { ...mockUsers[index], ...data };
      return mockResponse(null);
    } else {
      return mockResponse(null, '用户不存在', 500);
    }
  },
  
  async delete(userId) {
    await delay();
    const index = mockUsers.findIndex(item => item.userId === parseInt(userId));
    if (index !== -1) {
      mockUsers.splice(index, 1);
      return mockResponse(null);
    } else {
      return mockResponse(null, '用户不存在', 500);
    }
  },
  
  async resetPwd(data) {
    await delay();
    const index = mockUsers.findIndex(item => item.userId === data.userId);
    if (index !== -1) {
      // 实际应用中应该对密码进行加密
      return mockResponse(null);
    } else {
      return mockResponse(null, '用户不存在', 500);
    }
  }
};

// 模拟角色API
export const roleApi = {
  async list(query) {
    await delay();
    let data = [...mockRoles];
    
    // 搜索过滤
    if (query.roleName) {
      data = data.filter(item => item.roleName.includes(query.roleName));
    }
    if (query.roleKey) {
      data = data.filter(item => item.roleKey.includes(query.roleKey));
    }
    if (query.status !== undefined && query.status !== '') {
      data = data.filter(item => item.status === query.status);
    }
    
    // 分页
    const pageNum = parseInt(query.pageNum) || 1;
    const pageSize = parseInt(query.pageSize) || 10;
    const total = data.length;
    const start = (pageNum - 1) * pageSize;
    const end = start + pageSize;
    const rows = data.slice(start, end);
    
    return mockPageResponse(rows, total);
  },
  
  async get(roleId) {
    await delay();
    const role = mockRoles.find(item => item.roleId === parseInt(roleId));
    if (role) {
      return mockResponse(role);
    } else {
      return mockResponse(null, '角色不存在', 500);
    }
  },
  
  async add(data) {
    await delay();
    const newRole = {
      ...data,
      roleId: Math.max(...mockRoles.map(r => r.roleId)) + 1,
      createTime: new Date().toISOString().split('T')[0]
    };
    mockRoles.push(newRole);
    return mockResponse(null);
  },
  
  async update(data) {
    await delay();
    const index = mockRoles.findIndex(item => item.roleId === data.roleId);
    if (index !== -1) {
      mockRoles[index] = { ...mockRoles[index], ...data };
      return mockResponse(null);
    } else {
      return mockResponse(null, '角色不存在', 500);
    }
  },
  
  async delete(roleId) {
    await delay();
    const index = mockRoles.findIndex(item => item.roleId === parseInt(roleId));
    if (index !== -1) {
      mockRoles.splice(index, 1);
      return mockResponse(null);
    } else {
      return mockResponse(null, '角色不存在', 500);
    }
  }
};

// 模拟菜单API
export const menuApi = {
  async list(query) {
    await delay();
    let data = JSON.parse(JSON.stringify(mockMenus));
    
    // 搜索过滤
    if (query.menuName) {
      // 由于菜单是树形结构，这里简化处理，只在顶层过滤
      data = data.filter(item => item.menuName.includes(query.menuName));
    }
    if (query.status !== undefined && query.status !== '') {
      // 同样简化处理
      data = data.filter(item => item.status === query.status);
    }
    
    return mockResponse(data);
  },
  
  async get(menuId) {
    await delay();
    // 递归查找菜单
    const findMenu = (menus, id) => {
      for (const menu of menus) {
        if (menu.menuId === parseInt(id)) {
          return menu;
        }
        if (menu.children && menu.children.length > 0) {
          const found = findMenu(menu.children, id);
          if (found) return found;
        }
      }
      return null;
    };
    
    const menu = findMenu(mockMenus, menuId);
    if (menu) {
      return mockResponse(menu);
    } else {
      return mockResponse(null, '菜单不存在', 500);
    }
  },
  
  async add(data) {
    await delay();
    // 简化处理，只添加顶层菜单
    if (data.parentId === 0) {
      const newMenu = {
        ...data,
        menuId: Math.max(...mockMenus.map(m => m.menuId)) + 1,
        createTime: new Date().toISOString().split('T')[0],
        children: []
      };
      mockMenus.push(newMenu);
    }
    return mockResponse(null);
  },
  
  async update(data) {
    await delay();
    // 递归更新菜单
    const updateMenu = (menus, menuData) => {
      for (let i = 0; i < menus.length; i++) {
        if (menus[i].menuId === menuData.menuId) {
          menus[i] = { ...menus[i], ...menuData };
          return true;
        }
        if (menus[i].children && menus[i].children.length > 0) {
          if (updateMenu(menus[i].children, menuData)) {
            return true;
          }
        }
      }
      return false;
    };
    
    if (updateMenu(mockMenus, data)) {
      return mockResponse(null);
    } else {
      return mockResponse(null, '菜单不存在', 500);
    }
  },
  
  async delete(menuId) {
    await delay();
    // 递归删除菜单
    const deleteMenu = (menus, id) => {
      for (let i = 0; i < menus.length; i++) {
        if (menus[i].menuId === parseInt(id)) {
          menus.splice(i, 1);
          return true;
        }
        if (menus[i].children && menus[i].children.length > 0) {
          if (deleteMenu(menus[i].children, id)) {
            return true;
          }
        }
      }
      return false;
    };
    
    if (deleteMenu(mockMenus, menuId)) {
      return mockResponse(null);
    } else {
      return mockResponse(null, '菜单不存在', 500);
    }
  }
};

// 模拟部门API
export const deptApi = {
  async list(query) {
    await delay();
    let data = JSON.parse(JSON.stringify(mockDepts));
    
    // 搜索过滤
    if (query.deptName) {
      // 简化处理，只在顶层过滤
      data = data.filter(item => item.deptName.includes(query.deptName));
    }
    if (query.status !== undefined && query.status !== '') {
      // 简化处理
      data = data.filter(item => item.status === query.status);
    }
    
    return mockResponse(data);
  },
  
  async get(deptId) {
    await delay();
    // 递归查找部门
    const findDept = (depts, id) => {
      for (const dept of depts) {
        if (dept.deptId === parseInt(id)) {
          return dept;
        }
        if (dept.children && dept.children.length > 0) {
          const found = findDept(dept.children, id);
          if (found) return found;
        }
      }
      return null;
    };
    
    const dept = findDept(mockDepts, deptId);
    if (dept) {
      return mockResponse(dept);
    } else {
      return mockResponse(null, '部门不存在', 500);
    }
  },
  
  async add(data) {
    await delay();
    // 简化处理，只添加到顶层部门的子部门
    if (data.parentId === 1) {
      const newDept = {
        ...data,
        deptId: Math.max(...mockDepts[0].children.map(d => d.deptId)) + 1,
        createTime: new Date().toISOString().split('T')[0]
      };
      mockDepts[0].children.push(newDept);
    }
    return mockResponse(null);
  },
  
  async update(data) {
    await delay();
    // 递归更新部门
    const updateDept = (depts, deptData) => {
      for (let i = 0; i < depts.length; i++) {
        if (depts[i].deptId === deptData.deptId) {
          depts[i] = { ...depts[i], ...deptData };
          return true;
        }
        if (depts[i].children && depts[i].children.length > 0) {
          if (updateDept(depts[i].children, deptData)) {
            return true;
          }
        }
      }
      return false;
    };
    
    if (updateDept(mockDepts, data)) {
      return mockResponse(null);
    } else {
      return mockResponse(null, '部门不存在', 500);
    }
  },
  
  async delete(deptId) {
    await delay();
    // 递归删除部门
    const deleteDept = (depts, id) => {
      for (let i = 0; i < depts.length; i++) {
        if (depts[i].deptId === parseInt(id)) {
          depts.splice(i, 1);
          return true;
        }
        if (depts[i].children && depts[i].children.length > 0) {
          if (deleteDept(depts[i].children, id)) {
            return true;
          }
        }
      }
      return false;
    };
    
    if (deleteDept(mockDepts, deptId)) {
      return mockResponse(null);
    } else {
      return mockResponse(null, '部门不存在', 500);
    }
  }
}; 