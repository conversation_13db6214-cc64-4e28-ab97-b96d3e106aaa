/**
 * 聊天相关的工具函数
 */

// 格式化历史记录日期
export const formatHistoryDate = (dateString) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now - date);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) {
    return '今天';
  } else if (diffDays === 2) {
    return '昨天';
  } else if (diffDays <= 7) {
    return `${diffDays}天前`;
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    });
  }
};

// 获取历史记录标题
export const getHistoryTitle = (history) => {
  if (!history) return '新对话';
  
  // 如果有自定义标题，使用自定义标题
  if (history.title && history.title !== '新对话') {
    return history.title;
  }
  
  // 如果有第一条用户消息，使用前30个字符作为标题
  if (history.firstUserMessage) {
    return history.firstUserMessage.length > 30 
      ? history.firstUserMessage.substring(0, 30) + '...'
      : history.firstUserMessage;
  }
  
  // 默认标题
  return '新对话';
};

// 根据名称生成随机但固定的颜色
export const generateColorFromName = (name) => {
  if (!name) return 'grey';
  
  const colors = ['purple', 'cyan', 'orange', 'teal', 'blue', 'pink', 'red', 'green', 'blue-grey', 'deep-purple'];
  const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  return colors[hash % colors.length];
};

// 获取历史记录在完整列表中的索引
export const getHistoryFullIndex = (history, chatHistory) => {
  if (!history || !chatHistory) return -1;
  
  let index = 0;
  
  // 检查在recent7Days中的位置
  if (chatHistory.recent7Days) {
    const recent7Index = chatHistory.recent7Days.findIndex(item => item.id === history.id);
    if (recent7Index !== -1) {
      return index + recent7Index;
    }
    index += chatHistory.recent7Days.length;
  }
  
  // 检查在recent30Days中的位置
  if (chatHistory.recent30Days) {
    const recent30Index = chatHistory.recent30Days.findIndex(item => item.id === history.id);
    if (recent30Index !== -1) {
      return index + recent30Index;
    }
    index += chatHistory.recent30Days.length;
  }
  
  // 检查在before30Days中的位置
  if (chatHistory.before30Days) {
    const before30Index = chatHistory.before30Days.findIndex(item => item.id === history.id);
    if (before30Index !== -1) {
      return index + before30Index;
    }
  }
  
  return -1;
};

// 模式选项配置
export const modeOptions = [
  {
    icon: 'mdi-brain',
    text: '日常问答',
    value: 'r1'
  },
  {
    icon: 'mdi-thought-bubble',
    text: 'i暖城问数',
    value: 'v2'
  },
  {
    icon: 'mdi-pencil-outline',
    text: '内容创作',
    value: 'content_creation'
  },
  {
    icon: 'mdi-gavel',
    text: '政策问答',
    value: 'policy_qa'
  }
];

// 欢迎消息配置
export const welcomeMessage = {
  type: 'system',
  welcome: true,
  content: '', // 内容将从API响应中获取
  typingCompleted: true,
  showReferences: true,
  suggestedQuestions: [
    '累计发现问题数',
    '今日发现问题数',
    '已整改问题数',
    '正在解决问题数',
    '全部问题整改率',
    '现状问题状态分布',
    '旗区工作量统计',
    '东康问题总数',
    '近三个月东康问题总数'
  ]
};

// 处理消息内容中的思考标签
export const processThinkingTags = (content) => {
  if (!content) return { content: '', hasThinking: false, thinkingContent: '' };
  
  // 检查是否包含思考标签
  const thinkRegex = /<think>([\s\S]*?)<\/think>/gi;
  const matches = content.match(thinkRegex);
  
  if (!matches) {
    return { content, hasThinking: false, thinkingContent: '' };
  }
  
  // 提取思考内容
  let thinkingContent = '';
  matches.forEach(match => {
    const thinkContent = match.replace(/<\/?think>/gi, '').trim();
    if (thinkContent) {
      thinkingContent += thinkContent + '\n\n';
    }
  });
  
  // 移除思考标签，保留其他内容
  const contentWithoutThink = content.replace(thinkRegex, '').trim();
  
  return {
    content: contentWithoutThink,
    hasThinking: true,
    thinkingContent: thinkingContent.trim()
  };
};

// 获取文件上传状态文本
export const getFileUploadStatusText = (file) => {
  if (!file) return '准备上传...';
  
  const progress = file.progress || 0;
  
  // 动态生成省略号，确保动画效果
  const getDots = () => '.'.repeat(1 + Math.floor(Date.now() / 300) % 3);
  
  // 仅根据进度百分比判断
  if (progress === 100) {
    return '处理完成 100%';
  } else if (progress >= 99) {
    // 99%时显示"结构化处理中"加省略号动画效果
    return `结构化处理中${getDots()} 99%`;
  } else if (progress >= 75) {
    return `结构化处理中${getDots()} ${Math.round(progress)}%`;
  } else if (progress >= 50) {
    return `文件解析中${getDots()} ${Math.round(progress)}%`;
  } else {
    return `文件上传中${getDots()} ${Math.round(progress)}%`;
  }
};

// 检查是否有处理中的文件
export const hasProcessingFiles = (fileList) => {
  return fileList.some(file => 
    file.status === 'PARSING' || file.status === 'PROCESSING' || file.status === 'UPLOADING'
  );
};

// 显示Toast消息的工具函数
export const createToastMessage = (message, type = 'success', duration = 3000) => {
  return {
    show: true,
    message,
    type, // 'success', 'error', 'warning', 'info'
    duration
  };
};

// 滚动到聊天容器底部
export const scrollToBottom = (container, smooth = true) => {
  if (!container) return;
  
  const scrollOptions = {
    top: container.scrollHeight,
    behavior: smooth ? 'smooth' : 'auto'
  };
  
  container.scrollTo(scrollOptions);
};

// 滚动到聊天容器顶部
export const scrollToTop = (container, smooth = true) => {
  if (!container) return;
  
  const scrollOptions = {
    top: 0,
    behavior: smooth ? 'smooth' : 'auto'
  };
  
  container.scrollTo(scrollOptions);
};

// 适配移动端建议问题
export const adaptSuggestedQuestions = (questions, isMobile) => {
  if (!isMobile || !questions) return questions;
  
  // 移动端只显示前6个建议问题
  return questions.slice(0, 6);
};

// 检查用户权限
export const checkUserPermissions = (userStore, file) => {
  if (!userStore || !file) return { canEdit: false, canDelete: false, canShare: false };
  
  // 最高级管理员权限
  const isHighLevelAdmin = userStore.permissions?.includes('SUPER_ADMIN') || 
                          userStore.roles?.includes('SUPER_ADMIN');
  
  // 知识库管理员权限
  const isCurrentRepoAdmin = file.operationPermission === 'EDIT';
  
  // 文件所有者权限
  const isFileOwner = file.userId === userStore.id || file.createBy === userStore.name;
  
  return {
    canEdit: isHighLevelAdmin || isCurrentRepoAdmin || isFileOwner,
    canDelete: isHighLevelAdmin || isCurrentRepoAdmin || isFileOwner,
    canShare: isHighLevelAdmin || isCurrentRepoAdmin || isFileOwner,
    isHighLevelAdmin,
    isCurrentRepoAdmin,
    isFileOwner
  };
};

// 处理知识库选择逻辑
export const getKnowledgeBaseId = (repoIds, selectedKnowledgeBase, repoList) => {
  let kbId = null;
  let repoId = null;
  
  // 首先尝试从左侧菜单选择的知识库获取ID
  if (repoIds) {
    repoId = repoIds;
    
    // 查找对应的知识库详细信息
    const selectedRepo = repoList.find(repo => repo.repoId == repoIds);
    
    if (selectedRepo) {
      // 根据isJoined状态决定使用哪个ID作为kbId
      if (selectedRepo.isJoined) {
        kbId = selectedRepo.repoId;
      } else {
        kbId = selectedRepo.sharedRepoId || selectedRepo.repoId;
      }
    } else {
      kbId = repoIds;
    }
  }
  // 如果左侧没有选择，则从下拉选择框获取
  else if (selectedKnowledgeBase) {
    if (selectedKnowledgeBase.value === 'unit') {
      // 单位知识库，使用null
      kbId = null;
      repoId = null;
    } else if (selectedKnowledgeBase.value.startsWith('repo_')) {
      // 特定知识库，提取ID
      const selectedRepoId = selectedKnowledgeBase.value.split('_')[1];
      repoId = selectedRepoId;
      
      // 查找对应的知识库详细信息
      const selectedRepo = repoList.find(repo => repo.repoId == selectedRepoId);
      
      if (selectedRepo) {
        // 根据isJoined状态决定使用哪个ID作为kbId
        if (selectedRepo.isJoined) {
          kbId = selectedRepo.repoId;
        } else {
          kbId = selectedRepo.sharedRepoId || selectedRepo.repoId;
        }
      } else {
        kbId = selectedRepoId;
      }
    }
  }
  
  return { kbId, repoId };
};
