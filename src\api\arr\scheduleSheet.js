import request from '@/utils/request'

// 查询排班班次岗位组合列表
export function arrWorkList(query) {
  return request({
    url: '/arr/workRecord/arrWorkList',
    method: 'get',
    params: query
  })
}
// 新增排班校验规则
export function saveWorkCheck(data) {
  return request({
    url: '/arr/workCheck/saveWorkCheck',
    method: 'post',
    data: data
  })
}

// 获取排班校验规则
export function workChecklist() {
  return request({
    url: 'arr/workCheck/workChecklist',
    method: 'get'
  })
}
// 查询期望排班列表
export function desireWorkList(query) {
  return request({
    url: '/arr/workRecord/desireWorkList',
    method: 'get',
    params: query
  })
}
// // 查询排班班次岗位组合详细
// export function getWorkShiftsPost(id) {
//   return request({
//     url: '/arr/workShiftsPost/' + id,
//     method: 'get'
//   })
// }

// 保存排班编辑工作台数据
export function saveWorkTable(data) {
  return request({
    url: '/arr/workRecord/saveWorkList',
    method: 'post',
    data: data
  })
}

// // 另存为模板消息
// export function addTemplate(data){
//   return request({
//     url:"/arr/arrTemplate/addTemplate",
//     method:"post",
//     data:data,
//   })
// }

// // 模板列表查询接口
// export function templateList(query){
//   return request({
//     url:"/arr/arrTemplate/list",
//     method:"get",
//     params: query
//   })
// }

// // 获取模板详情接口
// export function modelDetail(id){
//   return request({
//     url:'/arr/arrTemplate/arrWorkList/'+ id,
//     method:"get"
//   })
// }