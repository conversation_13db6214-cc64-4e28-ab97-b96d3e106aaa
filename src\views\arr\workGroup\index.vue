<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-护理单元-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="请输入护理单元名称"
            clearable
            prefix-icon="Search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deptOptions"
            :props="{ label: 'label', children: 'children' }"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="deptTreeRef"
            node-key="id"
            highlight-current
            default-expand-all
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px" @submit.native.prevent>
          <el-form-item label="班组名称" prop="groupName">
            <el-input
              v-model="queryParams.groupName"
              placeholder="请输入班组名称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="Plus"
              @click="handleAdd"
              v-hasPermi="['arr:workGroup:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="Edit"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['arr:workGroup:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['arr:workGroup:remove']"
            >删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="Download"
              @click="handleExport"
              v-hasPermi="['arr:workGroup:export']"
            >导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="workGroupList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <!-- <el-table-column label="id" align="center" prop="id" /> -->
          <el-table-column label="班组名称" align="center" prop="groupName" />
          <!-- <el-table-column label="所属护理单元/科室id" align="center" prop="deptId" /> -->
          <el-table-column label="护理单元/科室名称" align="center" prop="deptName" />
          <!-- <el-table-column label="状态" align="center" prop="status" /> -->
          <el-table-column label="排序" align="center" prop="orderNum" />
          <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['arr:workGroup:edit']">修改</el-button>
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['arr:workGroup:remove']">删除</el-button>
              <el-button link type="primary" icon="User" @click="handleAuthUser(scope.row)">分配用户</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <pagination
          v-show="total>0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />

      </el-col>
      </el-row>

    <!-- 添加或修改排班护理组对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="workGroupRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="班组名称" prop="groupName">
          <el-input v-model="form.groupName" placeholder="请输入班组名称" />
        </el-form-item>
        <el-form-item label="护理单元" prop="deptId">
          <el-tree-select
            style="width: 100%;"
            v-model="form.deptId"
            :data="deptOptions"
            :props="{ value: 'id', label: 'label', children: 'children' }"
            value-key="id"
            placeholder="请选择护理单元"
            check-strictly
            @node-click="handleClick"
          />
        </el-form-item>
        <el-form-item label="排序" prop="orderNum">
          <el-input v-model="form.orderNum" placeholder="请输入排序" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="WorkGroup">
import { listWorkGroup, getWorkGroup, delWorkGroup, addWorkGroup, updateWorkGroup } from "@/api/arr/workGroup";
import { deptTreeSelect } from "@/api/system/user";
const router = useRouter();
const { proxy } = getCurrentInstance();
const workGroupList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    groupName: null,
    deptId: null,
    deptName: null,
    status: null,
    orderNum: null,
  },
  rules: {
    groupName: [{ required: true, message: "班组名称不能为空", trigger: "blur" }],
    deptId: [{ required: true, message: "护理单元不能为空", trigger: "blur" }],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询排班护理组列表 */
function getList() {
  loading.value = true;
  listWorkGroup(queryParams.value).then(response => {
    workGroupList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}
/** 根据名称筛选部门树 */
const deptName = ref("");
watch(deptName, (val) => {
  proxy.$refs["deptTreeRef"].filter(val);
});
/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};
// 表单重置
function reset() {
  form.value = {
    id: null,
    groupName: null,
    deptId: null,
    deptName: null,
    status: null,
    orderNum: null,
    createBy: null,
    createId: null,
    createTime: null,
    updateBy: null,
    updateId: null,
    updateTime: null,
    tenantId: null,
    delFlag: null,
    remark: null
  };
  proxy.resetForm("workGroupRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加排班护理组";
  form.value.deptId = queryParams.value.deptId;
  form.value.deptName = queryParams.value.deptName;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getWorkGroup(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改排班护理组";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["workGroupRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateWorkGroup(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addWorkGroup(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除排班护理组编号为"' + _ids + '"的数据项？').then(function() {
    return delWorkGroup(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('arr/workGroup/export', {
    ...queryParams.value
  }, `workGroup_${new Date().getTime()}.xlsx`)
}
/** 查询部门下拉树结构 */
const deptOptions = ref(undefined);
function getDeptTree() {
  deptTreeSelect().then((response) => {
    console.log(response);
    deptOptions.value = response.data;
  });
}

/** 获取护理单元选中节点信息 */
function handleClick(node) {
  form.value.deptName = node.label;
}
/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.value.deptId = data.id;
  queryParams.value.deptName = data.label;
  handleQuery();
}
function handleAuthUser(row) {
  console.log(row)
  router.push({name: 'WorkAuthUser', query: {nurseGroupId: row.id,nurseGroupName:row.groupName}})
}
getDeptTree();
getList();
</script>
