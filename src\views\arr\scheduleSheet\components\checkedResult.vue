<template>
  <el-dialog
    v-model="resultVisible"
    title="排班校验"
    width="900px"
    align-center
    @close="getClose"
  >
    <div class="result_box" v-for="(item, index) in resultList" :key="index">
      <div style="margin-right:1rem;">{{ index + 1 }}. </div>
      <div>
        <span style="margin-right:0.5rem;">{{ item.weekNum }}</span>
        <span style="margin-right:0.5rem;" v-if="item.date">({{ item.date }})</span>
        <span v-if="item.type==1">缺少{{ item.shiftsName }}({{ item.postName }})人员</span>
        <span v-if="item.type==2">{{ item.shiftsName }}({{ item.postName }})人员少于规定人数</span>
      </div>
    </div>
    <!-- <template #footer>
      <span class="dialog-footer">
        <el-button @click="getClose">取消</el-button>
        <el-button type="primary" @click="confirm">返回修改</el-button>
      </span>
    </template> -->
  </el-dialog>
</template>

<script setup>
const { proxy } = getCurrentInstance();
const props = defineProps({
  resultVisible: {
    type: Boolean,
  },
  resultList: {
    type: Array,
    default: [],
  },
});
const emit = defineEmits(["closeResult", "confirmResult"]);

// 监听排版备注列表弹窗打开关闭
const resultVisible = ref(false);
const resultList = ref([]);
watch(
  () => [props.resultVisible, props.resultList],
  (newValue, oldValue) => {
    console.log(newValue);
    if (newValue) {
      resultVisible.value = newValue[0];
      resultList.value = newValue[1];
    }
  }
);
// 关闭弹窗组件
function getClose() {
  resultVisible.value = false;
  emit("closeResult");
}

// 保存
function confirm() {
  resultVisible.value = false;
  emit("confirmResult", remarkList.value);
}
</script>

<style lang="scss" scoped>
.result_box{
  display: flex;
  align-content: center;
  height: 40px;
  line-height: 40px;
}
</style>
