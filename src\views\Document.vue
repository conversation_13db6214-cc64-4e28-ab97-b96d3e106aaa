<template>
  <div class="document-page">
    <!-- 左侧导航栏 -->
    <div class="left-nav">
      <div class="category-filter">
        <div class="category-header" @click="toggleCategories" :class="{ 'is-expanded': isCategoryExpanded }">
          <el-icon>
            <Document />
          </el-icon>
          <span>类型筛选</span>
          <el-icon>
            <ArrowDown />
          </el-icon>
        </div>
        <div class="category-buttons" v-show="isCategoryExpanded">
          <div class="category-grid">
            <button class="category-btn" v-for="category in categories" :key="category.id"
              :class="{ active: category.active }" @click="selectCategory(category.id)">
              {{ category.name }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧内容区 -->
    <div class="content-area">
      <!-- 顶部筛选栏 -->
      <div class="filter-bar">
        <div class="filter-group">
          <div class="filter-item">
            <el-select v-model="selectedTypes" placeholder="类型筛选" class="filter-select" multiple collapse-tags
              collapse-tags-tooltip>
              <el-option value="policy" label="政策法规"></el-option>
              <el-option value="reference" label="参考文献"></el-option>
            </el-select>
          </div>
          <div class="filter-item">
            <el-select v-model="dateSort" placeholder="按状态筛选" class="filter-select">
              <el-option value="" label="按状态筛选"></el-option>
              <el-option value="parsed" label="已解析"></el-option>
              <el-option value="processing" label="处理中"></el-option>
              <el-option value="failed" label="解析失败"></el-option>
            </el-select>
          </div>
          <div class="filter-item">
            <el-select v-model="timeSort" placeholder="按上传日期排序" class="filter-select">
              <el-option value="newest" label="最新上传"></el-option>
              <el-option value="oldest" label="最早上传"></el-option>
            </el-select>
          </div>
        </div>
        <el-button class="upload-btn" @click="showUploadDialog = true">
          选择文件
        </el-button>
      </div>

      <!-- 文件列表 -->
      <div class="file-list" v-if="paginatedDocuments.length > 0">
        <table>
          <thead>
            <tr>
              <th class="checkbox-column">
                <el-checkbox v-model="selectAll" @change="handleSelectAll"></el-checkbox>
              </th>
              <th class="name-column">文件名</th>
              <th class="status-column">状态</th>
              <th class="date-column">上传时间</th>
              <th class="size-column">大小</th>
              <th class="actions-column">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="file in paginatedDocuments" :key="file.id">
              <td class="checkbox-column">
                <el-checkbox v-model="file.selected" @change="handleFileSelect(file)"></el-checkbox>
              </td>
              <td class="name-column">
                <div class="file-info">
                  <div :class="['file-type-icon', file.name.split('.').pop()]">
                    <el-icon>
                      <Document />
                    </el-icon>
                  </div>
                  <div class="file-details">
                    <div class="file-name">{{ file.name }}</div>
                    <div class="file-uploader">上传者: {{ file.uploader }}</div>
                  </div>
                </div>
              </td>
              <td class="status-column">
                <span :class="['status-tag', getStatusClass(file.status)]">{{ file.statusText }}</span>
              </td>
              <td class="date-column">{{ file.date }}</td>
              <td class="size-column">{{ file.size }}</td>
              <td class="actions-column">
                <div class="action-buttons">
                  <template v-if="file.status == 'failed'">
                    <el-tooltip content="重新解析" placement="top">
                      <el-icon class="action-icon" @click="handleReparse(file)">
                        <RefreshRight />
                      </el-icon>
                    </el-tooltip>
                  </template>
                  <template v-else>
                    <el-icon class="action-icon" @click="handleSetting(file)">
                      <Setting />
                    </el-icon>
                    <el-icon class="action-icon" @click="handleDownload(file)">
                      <Download />
                    </el-icon>
                    <el-icon class="action-icon" @click="handleDelete(file)">
                      <Delete />
                    </el-icon>
                    <el-icon class="action-icon" @click="handleLock(file)">
                      <Lock />
                    </el-icon>
                  </template>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div v-else class="no-data">
        <el-empty description="暂无数据"></el-empty>
      </div>

      <!-- 分页 -->
      <div class="pagination" v-if="totalDocuments > 0">
        <span class="page-info">显示 {{ paginationStart }} - {{ paginationEnd }} 条，共 {{ totalDocuments }} 条记录</span>
        <div class="page-controls">
          <button class="page-btn prev" :disabled="currentPage == 1" @click="handlePageChange(currentPage - 1)">
            <el-icon>
              <ArrowLeft />
            </el-icon>
          </button>
          <template v-for="page in visiblePageNumbers" :key="page">
            <button v-if="typeof page == 'number'" :class="['page-btn', { active: currentPage == page }]"
              @click="handlePageChange(page)">
              {{ page }}
            </button>
            <span v-else class="page-ellipsis">{{ page }}</span>
          </template>
          <button class="page-btn next" :disabled="currentPage == totalPages"
            @click="handlePageChange(currentPage + 1)">
            <el-icon>
              <ArrowRight />
            </el-icon>
          </button>
        </div>
      </div>

      <!-- 上传文件弹窗 -->
      <el-dialog v-model="showUploadDialog" title="上传文件" width="500px" :close-on-click-modal="false">
        <div class="upload-dialog-content">
          <div class="upload-area" :class="{ 'drag-over': isDragOver }" @dragover.prevent="handleDragOver"
            @dragleave.prevent="handleDragLeave" @drop.prevent="handleDrop" @click="triggerFileInput">
            <input type="file" ref="fileInput" multiple style="display: none" @change="handleFileInputChange"
              accept=".pdf,.doc,.docx,.xls,.xlsx,.txt" />
            <div class="upload-icon">
              <el-icon>
                <Upload />
              </el-icon>
            </div>
            <div class="upload-text">
              <div class="upload-title">点击上传文件或将文件拖拽到这里</div>
              <div class="upload-desc">支持扩展名：.pdf .doc .docx .xls .xlsx .txt</div>
            </div>
          </div>
        </div>
      </el-dialog>
    </div>

    <!-- 权限设置弹窗 -->
    <el-dialog v-model="showPermissionDialog" width="360px" :close-on-click-modal="false" :show-close="true"
      custom-class="permission-dialog" :modal="true" :append-to-body="true" :lock-scroll="true"
      :destroy-on-close="false" :transition-name="'dialog-fade'">
      <template #header>
        <div class="dialog-header">
          <div class="dialog-title">文件权限设置</div>
          <div class="dialog-subtitle">{{ currentFileName }}</div>
        </div>
      </template>
      <div class="permission-content">
        <div class="permission-title">访问权限</div>
        <div class="permission-options">
          <label class="permission-option">
            <input type="radio" v-model="selectedPermission" value="department" class="permission-radio">
            <span class="permission-label">本部门</span>
          </label>
          <label class="permission-option">
            <input type="radio" v-model="selectedPermission" value="unit" class="permission-radio">
            <span class="permission-label">全单位</span>
          </label>
          <label class="permission-option">
            <input type="radio" v-model="selectedPermission" value="public" class="permission-radio">
            <span class="permission-label">公开</span>
          </label>
          <label class="permission-option">
            <input type="radio" v-model="selectedPermission" value="private" class="permission-radio">
            <span class="permission-label">仅个人</span>
          </label>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="save-btn" type="primary" @click="handlePermissionConfirm">保存设置</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { getFileList, removeFile } from "@/api/file/file.js";
import { ElMessage, ElMessageBox, ElTooltip, ElCheckbox, ElSelect, ElOption, ElDialog, ElButton } from 'element-plus';
import { Document, ArrowDown, Setting, Download, Delete, Lock, Upload, ArrowLeft, ArrowRight, RefreshRight } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import { getToken } from '@/utils/auth';

// 状态管理
const selectedTypes = ref([]);
const statusFilter = ref('');
const dateSort = ref('');
const timeSort = ref('newest');
const searchQuery = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const showUploadDialog = ref(false);
const isDragOver = ref(false);

// 多选相关
const selectAll = ref(false);
const selectedFiles = ref([]);

const handleSelectAll = (val) => {
  paginatedDocuments.value.forEach(file => {
    file.selected = val;
  });
  updateSelectedFiles();
};

const updateSelectedFiles = () => {
  selectedFiles.value = documents.value.filter(file => file.selected);
};

const handleFileSelect = (file) => {
  updateSelectedFiles();
  selectAll.value = paginatedDocuments.value.length > 0 &&
    paginatedDocuments.value.every(f => f.selected);
};

// 批量操作方法
const handleBatchDownload = () => {
  if (selectedFiles.value.length == 0) {
    ElMessage.warning('请选择要下载的文件');
    return;
  }
  selectedFiles.value.forEach(file => {
    handleDownload(file);
  });
};

const handleBatchDelete = () => {
  if (selectedFiles.value.length == 0) {
    ElMessage.warning('请选择要删除的文件');
    return;
  }
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedFiles.value.length} 个文件吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    let successCount = 0;
    let failCount = 0;
    
    // 逐个删除文件
    for (const file of selectedFiles.value) {
      try {
        // 获取文件的dataset_id，通常存储在fileMd5字段中
        const dataset_id = file.fileMd5;
        
        if (!dataset_id) {
          failCount++;
          console.error('文件ID不存在，无法删除:', file.fileName);
          continue;
        }
        
        // 调用删除API
        const config = {
          method: 'delete',
          url: `https://ragflow-4029862aab3ad76af3e3a564499a320b.zcznbj.com/api/v1/datasets/${dataset_id}/documents`,
          headers: {
            'Authorization': `Bearer ${getToken()}`
          }
        };
        
        // 发送请求
        const response = await axios(config);
        
        if (response.status === 200 || response.status === 204) {
          // 调用系统API删除文件记录
          await removeFile({ fileId: file.fileId });
          successCount++;
        } else {
          failCount++;
        }
      } catch (error) {
        console.error('删除文件失败:', error);
        failCount++;
      }
    }
    
    // 显示结果消息
    if (successCount > 0 && failCount === 0) {
      ElMessage.success(`已成功删除 ${successCount} 个文件`);
    } else if (successCount > 0 && failCount > 0) {
      ElMessage.warning(`已删除 ${successCount} 个文件，${failCount} 个文件删除失败`);
    } else {
      ElMessage.error('文件删除失败');
    }
    
    selectAll.value = false;
    selectedFiles.value = [];
    
    // 重新获取文件列表
    fetchDocuments();
  });
};

// 文件上传相关
const fileInput = ref(null);
const isUploading = ref(false);
const uploadProgress = ref(0);

const triggerFileInput = () => {
  fileInput.value.click();
};

const handleFileInputChange = (e) => {
  const files = Array.from(e.target.files);
  handleFiles(files);
  e.target.value = ''; // 重置input以允许选择相同文件
};

const handleDragOver = (e) => {
  e.preventDefault();
  isDragOver.value = true;
};

const handleDragLeave = (e) => {
  e.preventDefault();
  isDragOver.value = false;
};

const handleDrop = (e) => {
  e.preventDefault();
  isDragOver.value = false;
  const files = Array.from(e.dataTransfer.files);
  handleFiles(files);
};

const validateFile = (file) => {
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain'
  ];

  const allowedExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt'];
  const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

  if (!allowedExtensions.includes(fileExtension)) {
    ElMessage.error(`不支持的文件类型: ${file.name}`);
    return false;
  }

  // 设置文件大小限制为100MB
  const maxSize = 100 * 1024 * 1024;
  if (file.size > maxSize) {
    ElMessage.error(`文件过大: ${file.name}`);
    return false;
  }

  return true;
};

const handleFiles = (files) => {
  const validFiles = files.filter(validateFile);
  if (validFiles.length == 0) return;

  showUploadDialog.value = false; // 关闭上传对话框
  showPermissionDialog.value = true; // 显示权限设置弹窗
  currentUploadFiles.value = validFiles;
};

// 添加权限设置相关的响应式变量
const showPermissionDialog = ref(false);
const currentUploadFiles = ref([]);
const selectedPermission = ref('department');  // 默认选择"本部门"
const currentFileName = computed(() => {
  if (currentUploadFiles.value.length == 0) return '';
  if (currentUploadFiles.value.length == 1) {
    return currentUploadFiles.value[0].name;
  }
  return `已选择 ${currentUploadFiles.value.length} 个文件`;
});

// 处理权限设置确认
const handlePermissionConfirm = async () => {
  showPermissionDialog.value = false;
  isUploading.value = true;
  uploadProgress.value = 0;

  try {
    const formData = new FormData();
    currentUploadFiles.value.forEach(file => {
      formData.append('files', file);
    });
    formData.append('permission', selectedPermission.value);

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 10;
      }
    }, 200);

    // 模拟上传和解析过程
    await new Promise(resolve => setTimeout(resolve, 2000));

    clearInterval(progressInterval);
    uploadProgress.value = 100;

    // 模拟解析结果（这里应该替换为实际的API调用）
    const parseResults = currentUploadFiles.value.map(file => ({
      success: Math.random() > 0.3, // 模拟70%的成功率
      fileId: Date.now() + Math.random(),
      fileName: file.name
    }));

    // 处理每个文件的解析结果
    parseResults.forEach(result => {
      const newFile = {
        id: result.fileId,
        name: result.fileName,
        uploader: '当前用户',
        status: result.success ? 'parsed' : 'failed',
        statusText: result.success ? '已解析' : '此文档解析失败，未能成功上传',
        size: formatFileSize(currentUploadFiles.value.find(f => f.name == result.fileName).size),
        date: new Date().toLocaleString(),
        section: selectedPermission.value,
        isLocked: false,
        selected: false
      };

      // 如果解析成功，跳转到预览页面
      if (result.success) {
        router.push({
          path: '/pdf-preview',
          query: {
            id: newFile.id,
            name: newFile.name
          }
        });
      }

      documents.value = [newFile, ...documents.value];
    });

    const successCount = parseResults.filter(r => r.success).length;
    if (successCount > 0) {
      ElMessage.success(`成功上传并解析 ${successCount} 个文件`);
    }

    const failCount = parseResults.filter(r => !r.success).length;
    if (failCount > 0) {
      ElMessage.warning(`${failCount} 个文件解析失败`);
    }

  } catch (error) {
    ElMessage.error('文件上传失败');
    console.error('Upload error:', error);
  } finally {
    isUploading.value = false;
    uploadProgress.value = 0;
    currentUploadFiles.value = [];
  }
};

// 添加重新解析方法
const handleReparse = async (file) => {
  try {
    // 更新文件状态为处理中
    file.status = 'processing';
    file.statusText = '处理中';

    // 模拟重新解析过程
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 模拟解析结果（这里应该替换为实际的API调用）
    const success = Math.random() > 0.3; // 模拟70%的成功率

    if (success) {
      file.status = 'parsed';
      file.statusText = '已解析';
      ElMessage.success('文件解析成功');

      // 解析成功后跳转到预览页面
      router.push({
        path: '/pdf-preview',
        query: {
          id: file.id,
          name: file.name
        }
      });
    } else {
      file.status = 'failed';
      file.statusText = '此文档解析失败，未能成功上传';
      ElMessage.error('文件解析失败');
    }
  } catch (error) {
    file.status = 'failed';
    file.statusText = '此文档解析失败，未能成功上传';
    ElMessage.error('重新解析失败');
  }
};

const formatFileSize = (bytes) => {
  if (bytes == 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 文件操作方法
const handleView = (file) => {
  window.open(`/api/preview/${file.id}`, '_blank');
};

const handleEdit = (file) => {
  ElMessageBox.prompt('请输入新的文件名', '编辑文件', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: file.name,
    inputPattern: /\S+/,
    inputErrorMessage: '文件名不能为空'
  }).then(({ value }) => {
    ElMessage.success(`文件已重命名为: ${value}`);
  });
};

const handleDownload = (file) => {
  const link = document.createElement('a');
  link.href = `/api/download/${file.id}`;
  link.download = file.name;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const handleDelete = (file) => {
  ElMessageBox.confirm(
    '确定要删除这个文件吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // 获取文件的dataset_id，通常存储在fileMd5字段中
      const dataset_id = file.fileMd5;
      
      if (!dataset_id) {
        ElMessage.error('文件ID不存在，无法删除');
        return;
      }
      
      // 调用删除API
      const config = {
        method: 'delete',
        url: `https://ragflow-4029862aab3ad76af3e3a564499a320b.zcznbj.com/api/v1/datasets/${dataset_id}/documents`,
        headers: {
          'Authorization': `Bearer ${getToken()}`
        }
      };
      
      // 发送请求
      const response = await axios(config);
      console.log('删除文件响应:', response);
      
      if (response.status === 200 || response.status === 204) {
        // 从文件列表中移除该文件
        const index = documents.value.findIndex(item => item.id === file.id);
        if (index !== -1) {
          documents.value.splice(index, 1);
        }
        
        // 调用系统API删除文件记录
        await removeFile({ fileId: file.fileId });
        
        ElMessage.success('文件已删除');
        
        // 重新获取文件列表
        fetchDocuments();
      } else {
        ElMessage.error('删除文件失败');
      }
    } catch (error) {
      console.error('删除文件失败:', error);
      ElMessage.error('删除文件失败，请重试');
    }
  });
};

const handleLock = (file) => {
  const action = file.isLocked ? '解锁' : '锁定';
  ElMessageBox.confirm(
    `确定要${action}这个文件吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    file.isLocked = !file.isLocked;
    ElMessage.success(`文件已${action}`);
  });
};

const handleSetting = (file) => {
  router.push({
    path: '/pdf-preview',
    query: {
      id: file.id,
      name: file.name
    }
  });
};

// 辅助方法
const getFileType = (filename) => {
  const extension = filename.split('.').pop().toLowerCase();
  return extension;
};

const getStatusClass = (status) => {
  const statusMap = {
    'parsed': 'success',
    'processing': 'warning',
    'failed': 'error',
    'locked': 'warning'
  };
  return statusMap[status] || 'default';
};

const getStatusText = (status) => {
  const statusMap = {
    'parsed': '已解析',
    'processing': '处理中',
    'failed': '解析失败',
    'locked': '已锁定'
  };
  return statusMap[status] || status;
};

// 获取文件图标
const getFileIcon = (filename) => {
  const extension = filename.split('.').pop().toLowerCase();
  return extension;
};

// 生成更多的模拟数据
const generateMockData = () => {
  const data = [];
  const fileTypes = ['pdf', 'docx', 'xlsx', 'txt'];
  const statuses = ['parsed', 'processing', 'failed'];
  const statusTexts = {
    'parsed': '已解析',
    'processing': '处理中',
    'failed': '解析失败'
  };
  const documentTypes = ['policy', 'reference', 'technical', 'report', 'manual'];
  const uploaders = ['张三', '李四', '王五', '赵六', '钱七', '孙八'];

  // 预设一些真实的文件名
  const fileNames = [
    { name: '企业知识图谱应用白皮书', type: 'pdf', uploader: '张三' },
    { name: '产品技术说明书', type: 'docx', uploader: '李四' },
    { name: '销售数据分析', type: 'xlsx', uploader: '王五' },
    { name: '项目会议纪要', type: 'txt', uploader: '赵六' },
    { name: '系统架构设计方案', type: 'pdf', uploader: '钱七' },
    { name: '用户调研报告', type: 'docx', uploader: '孙八' },
    { name: '市场分析数据', type: 'xlsx', uploader: '张三' },
    { name: '技术评审会议记录', type: 'txt', uploader: '李四' },
    { name: '产品需求规格说明', type: 'pdf', uploader: '王五' },
    { name: '运营数据月报', type: 'xlsx', uploader: '赵六' }
  ];

  for (let i = 1; i <= 50; i++) {
    // 从预设文件名中随机选择一个
    const fileTemplate = fileNames[Math.floor(Math.random() * fileNames.length)];
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
    const randomType = documentTypes[Math.floor(Math.random() * documentTypes.length)];

    // 如果是重复的文件名，添加编号
    const fileName = i <= fileNames.length
      ? `${fileTemplate.name}.${fileTemplate.type}`
      : `${fileTemplate.name}_${Math.floor(i / fileNames.length)}.${fileTemplate.type}`;

    data.push({
      id: i,
      name: fileName,
      uploader: fileTemplate.uploader,
      status: randomStatus,
      statusText: statusTexts[randomStatus],
      size: `${(Math.random() * 5).toFixed(1)} MB`,
      date: `2024-${String(Math.floor(Math.random() * 3) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')} ${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
      selected: false,
      type: randomType
    });
  }
  return data;
};

// 状态管理
const documents = ref([]);
const totalDocumentsCount = ref(0);
const loading = ref(false);

// 获取文件列表
const fetchDocuments = async () => {
  loading.value = true;
  try {
    // 构建请求参数
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      status: 0
    };

    // 文件类型筛选：优先使用顶部筛选栏的选择，如果没有选择则使用左侧类别选择
    if (selectedTypes.value.length > 0) {
      params.fileType = selectedTypes.value.join(',');
    } else if (selectedCategories.value.length > 0) {
      params.fileType = selectedCategories.value.join(',');
    }

    // 添加状态筛选
    if (dateSort.value) {
      params.status = dateSort.value;
    }

    const response = await getFileList(params);

    // 根据返回的code判断是否有数据
    if (response && response.code == 0) {
      // 有数据
      documents.value = response.data.list.map(item => ({
        ...item,
        selected: false,
        // 确保文件有状态文本
        statusText: getStatusText(item.status)
      }));
      totalDocumentsCount.value = response.data.total || 0;
    } else if (response && response.code == -1) {
      // 没有数据
      documents.value = [];
      totalDocumentsCount.value = 0;
      ElMessage.info('暂无数据');
    } else {
      // 其他错误情况
      documents.value = [];
      totalDocumentsCount.value = 0;
      ElMessage.error(response?.message || '获取文档列表失败');
    }
  } catch (error) {
    documents.value = [];
    totalDocumentsCount.value = 0;
    ElMessage.error('获取文档列表失败');
    console.error('获取文档列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 计算属性
const filteredAndSortedDocuments = computed(() => {
  let result = [...documents.value];

  // 根据状态筛选
  if (dateSort.value) {
    result = result.filter(doc => doc.status == dateSort.value);
  }

  // 根据时间排序
  if (timeSort.value == 'newest') {
    result.sort((a, b) => new Date(b.date) - new Date(a.date));
  } else if (timeSort.value == 'oldest') {
    result.sort((a, b) => new Date(a.date) - new Date(b.date));
  }

  return result;
});

// 分页数据
const paginatedDocuments = computed(() => {
  // 由于接口已经处理了分页，这里直接返回文档列表
  return filteredAndSortedDocuments.value;
});

// 总数据量
const totalDocuments = computed(() => totalDocumentsCount.value);

// 总页数
const totalPages = computed(() => Math.ceil(totalDocuments.value / pageSize.value));

// 当前页显示范围
const paginationStart = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value + 1;
  return Math.min(start, totalDocuments.value);
});

const paginationEnd = computed(() => {
  const end = currentPage.value * pageSize.value;
  return Math.min(end, totalDocuments.value);
});

// 页码显示逻辑
const visiblePageNumbers = computed(() => {
  const total = totalPages.value;
  const current = currentPage.value;
  const maxVisible = 7;

  if (total <= maxVisible) {
    return Array.from({ length: total }, (_, i) => i + 1);
  }

  let pages = [];
  if (current <= 4) {
    for (let i = 1; i <= 5; i++) {
      pages.push(i);
    }
    if (total > 6) {
      pages.push('...');
      pages.push(total);
    }
  } else if (current >= total - 3) {
    pages.push(1);
    pages.push('...');
    for (let i = total - 4; i <= total; i++) {
      pages.push(i);
    }
  } else {
    pages.push(1);
    pages.push('...');
    for (let i = current - 1; i <= current + 1; i++) {
      pages.push(i);
    }
    pages.push('...');
    pages.push(total);
  }
  return pages;
});

// 页码变化处理
const handlePageChange = (page) => {
  if (typeof page == 'number' && page != currentPage.value) {
    currentPage.value = page;
    selectAll.value = false;
    updateSelectedFiles();
    fetchDocuments(); // 页码变化后重新获取数据
  }
};

// 导航数据
const categories = ref([
  { id: 'policy', name: '政策法规', active: false },
  { id: 'reference', name: '参考文献', active: false },
  { id: 'technical', name: '技术文档', active: false },
  { id: 'report', name: '报告文件', active: false },
  { id: 'manual', name: '操作手册', active: false }
]);

const selectedCategories = ref([]);

const selectCategory = (categoryId) => {
  const category = categories.value.find(cat => cat.id == categoryId);
  if (category) {
    category.active = !category.active;
    if (category.active) {
      selectedCategories.value.push(categoryId);
    } else {
      const index = selectedCategories.value.indexOf(categoryId);
      if (index > -1) {
        selectedCategories.value.splice(index, 1);
      }
    }
    // 选择左侧类别时，清空顶部类型筛选
    selectedTypes.value = [];
    // 重置到第一页并重新获取数据
    currentPage.value = 1;
    fetchDocuments();
  }
};

// 初始化数据
const router = useRouter();
onMounted(async () => {
  await fetchDocuments();
});

// 监听筛选变化
watch([dateSort, timeSort, selectedTypes, selectedCategories], () => {
  // 当筛选条件变化时，重置到第一页并重新获取数据
  currentPage.value = 1;
  fetchDocuments();
});

// 左侧导航栏相关
const isCategoryExpanded = ref(true);
const toggleCategories = () => {
  isCategoryExpanded.value = !isCategoryExpanded.value;
};
</script>

<style scoped>
/* 页面布局 */
.document-page {
  display: flex;
  height: 100%;
  background-color: #fff;
}

/* 左侧导航栏 */
.left-nav {
  width: 220px;
  background-color: #fff;
  padding: 16px;
  border-right: 1px solid #e8e8e8;
  height: 100%;
  overflow-y: auto;
}

.category-filter {
  width: 100%;
}

.category-header {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  cursor: pointer;
  user-select: none;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  margin-bottom: 8px;
  height: 32px;
  transition: all 0.3s;
}

.category-header.is-expanded {
  background-color: #E3EAFB;
  border-color: #E3EAFB;
}

.category-header .el-icon {
  font-size: 14px;
  color: #666;
}

.category-header span {
  flex: 1;
  margin: 0 8px;
  font-size: 14px;
  color: #333;
}

.is-expanded .el-icon:last-child {
  transform: rotate(180deg);
}

.el-icon {
  transition: transform 0.3s;
}

.category-buttons {
  transition: all 0.3s;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.category-btn {
  padding: 4px 8px;
  text-align: center;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: all 0.3s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 28px;
  line-height: 20px;
}

.category-btn:hover {
  border-color: #409EFF;
  color: #409EFF;
}

.category-btn.active {
  background: #409EFF;
  color: #fff;
  border-color: #409EFF;
}

/* 右侧内容区 */
.content-area {
  flex: 1;
  padding: 24px;
  overflow: auto;
  background-color: #fff;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.filter-group {
  display: flex;
  gap: 12px;
}

.filter-item {
  width: 140px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-select .el-input__wrapper) {
  background-color: #fff;
}

:deep(.el-select .el-input__inner) {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
}

:deep(.el-select__tags) {
  transform: translateY(0);
}

:deep(.el-select .el-tag) {
  margin: 2px;
}

.upload-btn {
  background-color: #FFD028;
  border: none;
  color: #000;
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 4px;
  height: 32px;
}

.upload-btn:hover {
  background-color: #FFE366;
}

/* 文件列表样式 */
.file-list {
  background: #fff;
  border-radius: 4px;
  border: 1px solid #EBEEF5;
  margin-bottom: 16px;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th {
  background: #F5F7FA;
  padding: 12px 16px;
  text-align: left;
  font-weight: 500;
  color: #606266;
  font-size: 14px;
  border-bottom: 1px solid #EBEEF5;
}

td {
  padding: 16px;
  border-bottom: 1px solid #EBEEF5;
  font-size: 14px;
}

tr:hover {
  background-color: #F5F7FA;
}

.checkbox-column {
  width: 40px;
  text-align: center;
}

.name-column {
  min-width: 300px;
}

.status-column {
  width: 100px;
}

.date-column {
  width: 160px;
}

.size-column {
  width: 100px;
}

.actions-column {
  width: 160px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-type-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.file-type-icon.pdf {
  color: #F56C6C;
  background: rgba(245, 108, 108, 0.1);
}

.file-type-icon.doc,
.file-type-icon.docx {
  color: #409EFF;
  background: rgba(64, 158, 255, 0.1);
}

.file-type-icon.xls,
.file-type-icon.xlsx {
  color: #67C23A;
  background: rgba(103, 194, 58, 0.1);
}

.file-type-icon.txt {
  color: #909399;
  background: rgba(144, 147, 153, 0.1);
}

.file-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-size: 14px;
  color: #303133;
}

.file-uploader {
  font-size: 12px;
  color: #909399;
}

.status-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-tag.success {
  background: #F0F9EB;
  color: #67C23A;
}

.status-tag.warning {
  background: #FDF6EC;
  color: #E6A23C;
}

.status-tag.error {
  background: #FEF0F0;
  color: #F56C6C;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

.action-icon {
  font-size: 16px;
  color: #606266;
  cursor: pointer;
  transition: color 0.3s;
}

.action-icon:hover {
  color: #409EFF;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  margin-top: 16px;
}

.page-info {
  font-size: 14px;
  color: #606266;
}

.page-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.page-btn {
  min-width: 32px;
  height: 32px;
  padding: 0 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  background: #fff;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;
}

.page-btn:hover:not(:disabled) {
  color: #409EFF;
  border-color: #409EFF;
}

.page-btn.active {
  background: #409EFF;
  color: #fff;
  border-color: #409EFF;
}

.page-btn:disabled {
  cursor: not-allowed;
  color: #C0C4CC;
  background-color: #F5F7FA;
}

.page-ellipsis {
  color: #606266;
  margin: 0 4px;
}

.prev,
.next {
  font-size: 12px;
}

/* 上传弹窗样式 */
.upload-dialog-content {
  padding: 20px;
}

.upload-area {
  border: 2px dashed #DCDFE6;
  border-radius: 6px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-area:hover,
.upload-area.drag-over {
  border-color: #409EFF;
  background: #F5F7FA;
}

.upload-icon {
  font-size: 48px;
  color: #909399;
  margin-bottom: 16px;
}

.upload-title {
  font-size: 16px;
  color: #303133;
  margin-bottom: 8px;
}

.upload-desc {
  font-size: 14px;
  color: #909399;
}

/* 权限设置弹窗样式 */
.permission-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 2000;
  padding-top: 25vh;
}

.permission-dialog :deep(.el-overlay) {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity 0.3s;
  opacity: 0;
}

.permission-dialog :deep(.el-overlay-dialog) {
  position: relative;
  z-index: 2001;
}

.permission-dialog :deep(.el-dialog) {
  margin: 0 auto !important;
  border-radius: 8px;
  overflow: hidden;
  transform: translateY(-20px);
  opacity: 0;
  transition: transform 0.3s, opacity 0.3s;
}

.permission-dialog :deep(.el-overlay.is-message-box) {
  opacity: 1;
}

.permission-dialog :deep(.el-overlay.is-message-box) .el-dialog {
  transform: translateY(0);
  opacity: 1;
}

.permission-dialog :deep(.el-dialog__header) {
  margin: 0;
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
}

.permission-dialog :deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.permission-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.permission-dialog :deep(.el-dialog__footer) {
  padding: 0;
  border-top: none;
  background: #fff;
  position: relative;
  height: 64px;
}

.permission-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 16px;
}

.permission-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.permission-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.permission-radio {
  margin: 0;
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.permission-label {
  margin-left: 8px;
  font-size: 14px;
  color: #333;
}

.dialog-footer {
  margin-top: 80px;
  text-align: right;
}

.dialog-footer :deep(.el-button) {
  padding: 8px 20px;
  font-size: 14px;
  border-radius: 4px;
}

.dialog-footer :deep(.el-button + .el-button) {
  margin-left: 12px;
}

.save-btn {
  width: 88px;
  height: 32px;
  padding: 0;
  font-size: 14px;
  background: #FFD028;
  border-color: #FFD028;
  border-radius: 4px;
  position: absolute;
  right: 16px;
  bottom: 16px;
}

.save-btn:hover {
  background: #FFE366;
  border-color: #FFE366;
}

.save-btn:active {
  background: #D4AD0F;
  border-color: #D4AD0F;
}

.no-data {
  padding: 40px 0;
  text-align: center;
}
</style>