<template>
  <div class="role-management">
    <!-- 搜索和工具栏 -->
    <div class="search-bar">
      <v-row>
        <v-col cols="12" sm="6" md="3" lg="2">
          <v-text-field
            v-model="queryParams.roleName"
            label="角色名称"
            placeholder="请输入角色名称"
            clearable
            hide-details
            density="compact"
            variant="outlined"
            @keyup.enter="handleQuery"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="3" lg="2">
          <v-text-field
            v-model="queryParams.roleKey"
            label="权限字符"
            placeholder="请输入权限字符"
            clearable
            hide-details
            density="compact"
            variant="outlined"
            @keyup.enter="handleQuery"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="3" lg="2">
          <v-select
            v-model="queryParams.status"
            :items="statusOptions"
            item-title="label"
            item-value="value"
            label="状态"
            clearable
            hide-details
            density="compact"
            variant="outlined"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="3" lg="2">
          <v-btn color="primary" @click="handleQuery" class="mr-2">
            <v-icon start>mdi-magnify</v-icon>搜索
          </v-btn>
          <v-btn @click="resetQuery" variant="outlined">
            <v-icon start>mdi-refresh</v-icon>重置
          </v-btn>
        </v-col>
      </v-row>
    </div>

    <!-- 操作按钮区 -->
    <div class="table-toolbar">
      <v-btn color="primary" @click="handleAdd" class="mr-2">
        <v-icon start>mdi-plus</v-icon>新增
      </v-btn>
      <v-btn color="error" @click="handleBatchDelete" :disabled="selectedItems.length === 0" class="mr-2">
        <v-icon start>mdi-delete</v-icon>删除
      </v-btn>
      <v-btn @click="handleRefresh" variant="outlined">
        <v-icon start>mdi-refresh</v-icon>刷新
      </v-btn>
    </div>

    <!-- 数据表格 -->
    <v-data-table
      v-model="selectedItems"
      :headers="headers"
      :items="roleList"
      :loading="loading"
      :items-per-page="queryParams.pageSize"
      :page="queryParams.pageNum"
      :server-items-length="total"
      item-value="roleId"
      show-select
      class="elevation-1 mt-4"
      @update:page="handlePageChange"
      @update:items-per-page="handlePageSizeChange"
    >
      <!-- 状态列 -->
      <template #[`item.status`]="{ item }">
        <v-chip
          :color="item.status === '0' ? 'success' : 'error'"
          :text="item.status === '0' ? '正常' : '停用'"
          size="small"
        ></v-chip>
      </template>

      <!-- 操作列 -->
      <template #[`item.actions`]="{ item }">
        <v-btn icon size="small" color="primary" @click="handleEdit(item)" class="mr-1">
          <v-icon>mdi-pencil</v-icon>
        </v-btn>
        <v-btn icon size="small" color="info" @click="handlePermission(item)" class="mr-1">
          <v-icon>mdi-key-variant</v-icon>
        </v-btn>
        <v-btn icon size="small" color="error" @click="handleDelete(item)">
          <v-icon>mdi-delete</v-icon>
        </v-btn>
      </template>
    </v-data-table>

    <!-- 角色表单对话框 -->
    <v-dialog v-model="dialog.visible" max-width="700px">
      <v-card>
        <v-card-title>
          <span class="text-h6">{{ dialog.title }}</span>
        </v-card-title>
        <v-card-text>
          <v-form ref="formRef" v-model="formValid">
            <v-container>
              <v-row>
                <v-col cols="12" sm="6">
                  <v-text-field
                    v-model="form.roleName"
                    label="角色名称"
                    required
                    :rules="[v => !!v || '角色名称不能为空']"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-text-field
                    v-model="form.roleKey"
                    label="权限字符"
                    required
                    :rules="[v => !!v || '权限字符不能为空']"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-text-field
                    v-model="form.roleSort"
                    label="显示顺序"
                    type="number"
                    required
                    :rules="[v => !!v || '显示顺序不能为空']"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-radio-group v-model="form.status" row label="状态">
                    <v-radio label="正常" value="0"></v-radio>
                    <v-radio label="停用" value="1"></v-radio>
                  </v-radio-group>
                </v-col>
                <v-col cols="12">
                  <v-textarea
                    v-model="form.remark"
                    label="备注"
                    rows="3"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" @click="submitForm">确定</v-btn>
          <v-btn @click="dialog.visible = false">取消</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 权限分配对话框 -->
    <v-dialog v-model="permissionDialog.visible" max-width="700px">
      <v-card>
        <v-card-title>
          <span class="text-h6">分配权限 - {{ permissionDialog.roleName }}</span>
        </v-card-title>
        <v-card-text>
          <div v-if="menuTree.length > 0" class="permission-tree">
            <v-treeview
              v-model="permissionDialog.checkedKeys"
              :items="menuTree"
              item-key="id"
              item-text="name"
              item-children="children"
              selectable
              return-object
              open-all
              dense
              selection-type="independent"
            ></v-treeview>
          </div>
          <div v-else class="text-center pa-4">
            <v-progress-circular indeterminate color="primary"></v-progress-circular>
            <div class="mt-2">加载中...</div>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" @click="submitPermission">确定</v-btn>
          <v-btn @click="permissionDialog.visible = false">取消</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { roleApi, menuApi } from '../../utils/mock';

// 表格列定义
const headers = [
  { title: '角色编号', key: 'roleId' },
  { title: '角色名称', key: 'roleName' },
  { title: '权限字符', key: 'roleKey' },
  { title: '显示顺序', key: 'roleSort' },
  { title: '状态', key: 'status' },
  { title: '创建时间', key: 'createTime' },
  { title: '操作', key: 'actions', sortable: false }
];

// 状态选项
const statusOptions = [
  { label: '正常', value: '0' },
  { label: '停用', value: '1' }
];

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  roleName: '',
  roleKey: '',
  status: ''
});

// 表单参数
const form = reactive({
  roleId: undefined,
  roleName: '',
  roleKey: '',
  roleSort: 0,
  status: '0',
  remark: '',
  menuIds: []
});

// 对话框控制
const dialog = reactive({
  visible: false,
  title: '',
  type: '' // add 或 edit
});

// 权限分配对话框
const permissionDialog = reactive({
  visible: false,
  roleId: undefined,
  roleName: '',
  checkedKeys: []
});

// 数据相关
const loading = ref(false);
const roleList = ref([]);
const total = ref(0);
const selectedItems = ref([]);
const formRef = ref(null);
const formValid = ref(false);
const menuTree = ref([]);

// 获取角色列表
const getList = async () => {
  loading.value = true;
  try {
    const res = await roleApi.list(queryParams);
    roleList.value = res.data.rows;
    total.value = res.data.total;
  } catch (error) {
    console.error('获取角色列表失败', error);
    ElMessage.error('获取角色列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取菜单树
const getMenuTree = async () => {
  try {
    const res = await menuApi.list({});
    
    // 递归处理菜单数据为树形结构
    const buildMenuTree = (menus) => {
      return menus.map(menu => {
        const node = {
          id: menu.menuId,
          name: menu.menuName,
          children: menu.children && menu.children.length > 0 ? buildMenuTree(menu.children) : []
        };
        return node;
      });
    };
    
    menuTree.value = buildMenuTree(res.data);
  } catch (error) {
    console.error('获取菜单树失败', error);
    ElMessage.error('获取菜单树失败');
  }
};

// 处理查询
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

// 重置查询
const resetQuery = () => {
  queryParams.roleName = '';
  queryParams.roleKey = '';
  queryParams.status = '';
  handleQuery();
};

// 处理页码变化
const handlePageChange = (page) => {
  queryParams.pageNum = page;
  getList();
};

// 处理每页条数变化
const handlePageSizeChange = (pageSize) => {
  queryParams.pageSize = pageSize;
  getList();
};

// 处理刷新
const handleRefresh = () => {
  getList();
};

// 处理新增
const handleAdd = () => {
  resetForm();
  dialog.visible = true;
  dialog.title = '添加角色';
  dialog.type = 'add';
};

// 处理编辑
const handleEdit = async (row) => {
  resetForm();
  dialog.visible = true;
  dialog.title = '编辑角色';
  dialog.type = 'edit';
  
  try {
    const res = await roleApi.get(row.roleId);
    if (res.data) {
      Object.assign(form, res.data);
    }
  } catch (error) {
    console.error('获取角色详情失败', error);
    ElMessage.error('获取角色详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除角色"${row.roleName}"吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await roleApi.delete(row.roleId);
      ElMessage.success('删除成功');
      getList();
    } catch (error) {
      console.error('删除角色失败', error);
      ElMessage.error('删除角色失败');
    }
  }).catch(() => {});
};

// 处理批量删除
const handleBatchDelete = () => {
  const roleNames = selectedItems.value.map(item => item.roleName).join('、');
  ElMessageBox.confirm(`确认删除角色"${roleNames}"吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 实际应用中应该使用批量删除API
      for (const item of selectedItems.value) {
        await roleApi.delete(item.roleId);
      }
      ElMessage.success('批量删除成功');
      getList();
      selectedItems.value = [];
    } catch (error) {
      console.error('批量删除角色失败', error);
      ElMessage.error('批量删除角色失败');
    }
  }).catch(() => {});
};

// 处理权限分配
const handlePermission = (row) => {
  permissionDialog.roleId = row.roleId;
  permissionDialog.roleName = row.roleName;
  permissionDialog.checkedKeys = []; // 实际应用中，应该获取该角色已有的权限
  permissionDialog.visible = true;
  
  // 加载菜单树
  if (menuTree.value.length === 0) {
    getMenuTree();
  }
};

// 提交权限分配
const submitPermission = async () => {
  try {
    // 实际应用中，应该调用更新角色权限的API
    ElMessage.success('权限分配成功');
    permissionDialog.visible = false;
  } catch (error) {
    console.error('权限分配失败', error);
    ElMessage.error('权限分配失败');
  }
};

// 提交表单
const submitForm = async () => {
  if (!formValid.value) {
    return;
  }
  
  try {
    if (dialog.type === 'add') {
      await roleApi.add(form);
      ElMessage.success('添加成功');
    } else {
      await roleApi.update(form);
      ElMessage.success('修改成功');
    }
    dialog.visible = false;
    getList();
  } catch (error) {
    console.error('提交表单失败', error);
    ElMessage.error('提交表单失败');
  }
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.reset();
  }
  Object.assign(form, {
    roleId: undefined,
    roleName: '',
    roleKey: '',
    roleSort: 0,
    status: '0',
    remark: '',
    menuIds: []
  });
};

// 页面初始化
onMounted(() => {
  getList();
});
</script>

<style scoped>
.role-management {
  width: 100%;
}

.search-bar {
  background-color: var(--card-background);
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px var(--shadow-color);
}

.table-toolbar {
  margin-bottom: 16px;
  display: flex;
  flex-wrap: wrap;
}

.table-toolbar .v-btn {
  margin-bottom: 8px;
}

:deep(.v-data-table) {
  background-color: var(--card-background) !important;
  border-radius: 4px;
  box-shadow: 0 1px 2px var(--shadow-color) !important;
}

:deep(.v-data-table-header) {
  background-color: var(--background-color);
}

.permission-tree {
  max-height: 400px;
  overflow-y: auto;
}
</style> 