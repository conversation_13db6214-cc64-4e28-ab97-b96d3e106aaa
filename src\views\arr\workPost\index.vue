<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="请输入科室/病区"
            clearable
            prefix-icon="Search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deptOptions"
            :props="{ label: 'label', children: 'children' }"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="deptTreeRef"
            node-key="id"
            highlight-current
            default-expand-all
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="岗位名称" prop="postName">
            <el-input
              v-model="queryParams.postName"
              placeholder="请输入岗位名称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="所属科室/病区" prop="deptId"  label-width="100px">
              <el-tree-select
                style="width: 100%;"
                v-model="queryParams.deptId"
                :data="deptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                placeholder="请选择所属科室/病区"
                check-strictly
              />
          </el-form-item>
          <el-form-item label="岗位状态" prop="status" v-if="false">
            <el-select style="width: 100%;" v-model="queryParams.status" placeholder="请选择岗位状态">
              <el-option
                v-for="dict in statusList"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="Plus"
              @click="handleAdd"
              v-hasPermi="['arr:workPost:add']"
            >新增岗位</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="Edit"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['arr:workPost:edit']"
            >修改岗位</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['arr:workPost:remove']"
            >删除岗位</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="Download"
              @click="handleExport"
              v-hasPermi="['arr:workPost:export']"
            >导出岗位</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="workPostList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="岗位名称" align="center" prop="postName" />
          <!-- <el-table-column label="岗位状态" align="center" prop="status" /> -->
          <el-table-column label="岗位状态"  width="100" align="center" prop="status">
            <template #default="scope">
              <el-tag v-if="scope.row.status== '1'" type="success">正常</el-tag>
              <el-tag v-if="scope.row.status== '2'" type="danger">停用</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="所属科室/病区" align="center" prop="deptName"/>
          <el-table-column label="颜色"  width="60" align="center" prop="color">
            <template #default="scope">
              <div style="width: 50px;height: 20px;border-radius: 1px;" :style="{'background-color':scope.row.color}"></div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['arr:workPost:edit']">修改</el-button>
              <el-button link type="primary" icon="SwitchButton" @click="handleOnOff(scope.row)">{{scope.row.status == '1'?'停用':'开启'}}</el-button>
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['arr:workPost:remove']">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <pagination
          v-show="total>0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
    <!-- 添加或修改排班岗位管理对话框 -->
    <el-dialog :title="title" v-model="open" width="900px" append-to-body>
      <el-form ref="workPostRef" :model="form" :rules="rules" label-width="90px">
        <el-row :gutter="20">
          <el-col :span="8" :xs="24">
            <el-form-item label="岗位名称" prop="postName">
              <el-input v-model="form.postName" placeholder="请输入岗位名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="所属科室/病区" prop="deptId" label-width="110px">
              <el-tree-select
                v-model="form.deptId"
                :data="deptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                placeholder="请选择所属科室/病区"
                check-strictly
                @node-click="handleClick"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="护理能级" prop="level">
              <el-select style="width: 100%;" v-model="form.level" placeholder="请选择护理能级">
                <el-option
                  v-for="dict in hr_staff_level"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="岗位状态" prop="status">
              <el-select style="width: 100%;" v-model="form.status" placeholder="请选择岗位状态">
                <el-option
                  v-for="dict in hr_staff_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="分值" prop="score" label-width="110px">
              <el-input v-model="form.score" oninput="value=value.replace(/[^0-9.]/g,'').replace(/\.{2,}/g,'.').replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3')" placeholder="请输入分值" />
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="系数" prop="coefficient">
              <el-input v-model="form.coefficient" oninput="value=value.replace(/[^0-9.]/g,'').replace(/\.{2,}/g,'.').replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3')" placeholder="请输入系数" />
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="排序" prop="orderNum">
              <el-input v-model="form.orderNum" oninput="value=value.replace(/[^\d]/g,'')" placeholder="请输入排序" />
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="住院岗标志" prop="hospFlag" label-width="110px">
              <el-radio-group v-model="form.hospFlag">
                <el-radio
                  v-for="dict in sys_common_flag"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="责护标志" prop="respFlag">
              <el-radio-group v-model="form.respFlag">
                <el-radio
                  v-for="dict in sys_common_flag"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="颜色" prop="color">
              <el-color-picker v-model="form.color" @change="getColor"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="WorkPost">
import { listWorkPost, getWorkPost, delWorkPost, addWorkPost, updateWorkPost, changeStatus } from "@/api/arr/workPost";
import { deptTreeSelect } from "@/api/system/user";

const { proxy } = getCurrentInstance();
const { sys_common_flag, hr_staff_level,hr_staff_status } = proxy.useDict('sys_common_flag', 'hr_staff_level','hr_staff_status');
const workPostList = ref([{
  deptName: ""
}]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    postName: null,
    deptId: null,
    deptName: null,
    level: null,
    shortName: null,
    description: null,
    score: null,
    coefficient: null,
    color: null,
    status: null,
    respFlag: null,
    hospFlag: null,
    orderNum: null,
  },
  rules: {
    postName: [{ required: true, message: "岗位名称不能为空", trigger: "blur" }],
    deptId: [{ required: true, message: "护理单元不能为空", trigger: "blur" }],
    level: [{ required: true, message: "护理能级不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules,statusList } = toRefs(data);

/** 查询排班岗位管理列表 */
function getList() {
  loading.value = true;
  listWorkPost(queryParams.value).then(response => {
    console.log(response);
    workPostList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    postName: null,
    deptId: null,
    deptName: null,
    level: null,
    shortName: null,
    description: null,
    score: null,
    coefficient: null,
    color: null,
    status: null,
    respFlag: null,
    hospFlag: null,
    orderNum: null,
    createBy: null,
    createId: null,
    createTime: null,
    updateBy: null,
    updateId: null,
    updateTime: null,
    tenantId: null,
    delFlag: null,
    remark: null
  };
  proxy.resetForm("workPostRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加岗位";
  form.value.deptId = queryParams.value.deptId;
  form.value.deptName = queryParams.value.deptName;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getWorkPost(_id).then(response => {
    console.log(response)
    form.value = response.data;
    open.value = true;
    title.value = "修改岗位";
  });
}
/** 停用/开启按钮操作 */
function handleOnOff(row) {
  const _id = row.id || ids.value
  const _status = row.status
  const statusText = _status == '1'?'停用':'启用'
  
  proxy.$modal.confirm(`是否确认${statusText}排班岗位管理编号为${_id}的数据项？`).then(function() {
    return changeStatus(_status == '1'?'2':'1',_id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess(`${statusText}成功`);
  }).catch(() => {});
}

const formColor = ref()
function getColor(e){
  console.log(e)
}

/** 提交按钮 */
function submitForm() {
  console.log(form.value)
  proxy.$refs["workPostRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateWorkPost(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addWorkPost(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除排班岗位管理编号为"' + _ids + '"的数据项？').then(function() {
    return delWorkPost(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('arr/workPost/export', {
    ...queryParams.value
  }, `workPost_${new Date().getTime()}.xlsx`)
}

/** 根据名称筛选部门树 */
const deptName = ref("");
watch(deptName, (val) => {
  proxy.$refs["deptTreeRef"].filter(val);
});
/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};
/** 查询部门下拉树结构 */
const deptOptions = ref(undefined);
function getDeptTree() {
  deptTreeSelect().then((response) => {
    console.log(response);
    deptOptions.value = response.data;
  });
}

/** 获取护理单元选中节点信息 */
function handleClick(node) {
  form.value.deptName = node.label;
}
/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.value.deptId = data.id;
  queryParams.value.deptName = data.label;
  handleQuery();
}
getDeptTree();
getList();
</script>
