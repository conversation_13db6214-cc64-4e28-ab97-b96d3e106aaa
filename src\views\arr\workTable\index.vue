<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 部门树 - 点击查看对应部门下的排版内容 -->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="请输入科室/病区"
            clearable
            prefix-icon="Search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deptOptions"
            :props="{ label: 'label', children: 'children' }"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="deptTreeRef"
            node-key="id"
            highlight-current
            default-expand-all
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!-- 右侧整体排班内容 -->
      <el-col :span="20" :xs="24">
        <!-- 条件查询form表 -->
        <el-form
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          label-width="68px"
        >
          <el-row :gutter="24">
            <el-col :span="18">
              <el-form-item>
                <div class="user_deptName">{{ queryParams.deptName }}</div>
              </el-form-item>
              <el-form-item>
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  style="width: 250px"
                  :editable="false"
                  :default-time="defaultTime"
                  :clearable="false"
                  @change="changeDate"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="fun_btn">
                <el-button
                  type="primary"
                  icon="VideoPlay"
                  @click="startScheduling"
                  v-hasPermi="['arr:workRecord:start']"
                  >开始排班</el-button
                >
                <el-button type="warning" icon="Download" plain @click="exportWorkTable"
                  >导出排班</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <!-- 排班人员的table表单 - 子组件 -->
        <ShowTable :dates="dates" :deptId="queryParams.deptId" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="WorkTable">
import ShowTable from "./components/showTable.vue";
import { deptTreeSelect } from "@/api/system/user";
import { useRouter } from "vue-router";
const router = useRouter();
const { proxy } = getCurrentInstance();

const total = ref(0);
const userId = ref(localStorage.getItem("userId"));
const userName = ref(localStorage.getItem("userName"));
const userDeptId = ref(localStorage.getItem("deptId"));
const userDeptName = ref(
  localStorage.getItem("deptName") != "null" ? localStorage.getItem("deptName") : null
);

const data = reactive({
  queryParams: {
    deptId: userDeptId.value,
    deptName: userDeptName.value,
  },
});

const { queryParams } = toRefs(data);

/** 根据名称筛选部门树 */
const deptName = ref("");
watch(deptName, (val) => {
  proxy.$refs["deptTreeRef"].filter(val);
});
/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};
/** 查询部门下拉树结构 */
const deptOptions = ref(undefined);
function getDeptTree() {
  deptTreeSelect().then((response) => {
    deptOptions.value = response.data;
  });
}
/* 部门树节点点击事件 */
function handleNodeClick(data) {
  queryParams.value.deptId = JSON.stringify(data.id);
  queryParams.value.deptName = data.label;
}

/* 选择开始时间和结束时间 */
// 默认一周时间（7天）
const timeInterval = ref(6);
// 当前日期
const today = ref(new Date());
// 计算一周后的日期 - 当前日期 + 6天
const nextday = (e) => {
  let date2 = new Date(today.value);
  date2.setDate(today.value.getDate() + timeInterval.value);
  return date2;
};
// 将开始时间和结束时间赋给 el-date-picker
const dateRange = ref([new Date(), nextday()]);
// el-date-picker 设置默认时分秒为： 开始时间-00:00:00 ；结束时间- 23:59:59
const defaultTime = reactive([new Date(0, 0, 0, 0, 0, 0), new Date(0, 0, 0, 23, 59, 59)]);
const dates = ref([]);
// 修改时间范围事件
const changeDate = (event) => {
  // 清空时间范围内每天的日期
  dates.value = [];  
  if (dateRange.value) {
    let currentDate = event ? dateRange.value[0] : new Date();
    // 算出时间范围内所有的日期（只保留年月日），并push到dates数组里，将dates传给子组件；
    while (currentDate <= dateRange.value[1]) {
      dates.value.push(
        `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(
          2,
          "0"
        )}-${String(currentDate.getDate()).padStart(2, "0")}`
      );
      currentDate.setDate(currentDate.getDate() + 1);
    }
  }
};

/** 开始排班操作  */
function startScheduling() {
  let routeData = router.resolve({
    path: "/scheduleSheet",
    query: { deptId: queryParams.value.deptId, deptName: queryParams.value.deptName },
  });
  window.open(routeData.href, "_blank");
}

/* 导出排班 */
function exportWorkTable() {
  let newArr = [];
  if (dates.value.length!=0) {
    dates.value.map((item, index) => {
      if (index == 0) {
        newArr.push(item);
      } else if (index == dates.value.length - 1) {
        newArr.push(item);
      }
    });
  } else {
    dateRange.value.map((item) => {
      let newValue = `${item.getFullYear()}-${String(item.getMonth() + 1).padStart(2,"0")}-${item.getDate()}`;
      newArr.push(newValue);
    });
  }
  proxy.download(
    "arr/workRecord/export",
    {
      ...proxy.addDateRange(queryParams.value, newArr),
    },
    `护士排班_${newArr[0]}-${newArr[1]}.xlsx`
  );
}

getDeptTree();
</script>

<style lang="scss" scoped>
.app-container {
  .user_deptName {
    min-width: 100px;
    font-size: 18px;
    font-weight: bold;
  }
  .user_deptInfo {
    width: 200px;

    :deep(.el-input__wrapper) {
      // background-color: rgba($color: #000000, $alpha: 0);
      box-shadow: none;
      .el-input__inner {
        color: #000;
        font-size: 16px;
        font-weight: bold;
      }
      .el-icon {
        color: #000;
      }
    }
    :deep(.is-focus) {
      box-shadow: none !important;
    }
  }
  .fun_btn {
    display: flex;
    :deep(.el-form-item__content) {
      justify-content: right;
    }
  }
}
</style>
