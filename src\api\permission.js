import request from '@/utils/request';

// 获取用户列表
export function getUserList(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query
  });
}

// 获取用户详情
export function getUser(userId) {
  return request({
    url: `/system/user/${userId}`,
    method: 'get'
  });
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/system/user',
    method: 'post',
    data: data
  });
}

// 修改用户
export function updateUser(data) {
  return request({
    url: '/system/user',
    method: 'put',
    data: data
  });
}

// 删除用户
export function deleteUser(userId) {
  return request({
    url: `/system/user/${userId}`,
    method: 'delete'
  });
}

// 导出用户
export function exportUser(query) {
  return request({
    url: '/system/user/export',
    method: 'get',
    params: query
  });
}

// 重置密码
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password
  };
  return request({
    url: '/system/user/resetPwd',
    method: 'put',
    data: data
  });
}

// 获取角色列表
export function getRoleList(query) {
  return request({
    url: '/system/role/list',
    method: 'get',
    params: query
  });
}

// 获取角色详情
export function getRole(roleId) {
  return request({
    url: `/system/role/${roleId}`,
    method: 'get'
  });
}

// 新增角色
export function addRole(data) {
  return request({
    url: '/system/role',
    method: 'post',
    data: data
  });
}

// 修改角色
export function updateRole(data) {
  return request({
    url: '/system/role',
    method: 'put',
    data: data
  });
}

// 删除角色
export function deleteRole(roleId) {
  return request({
    url: `/system/role/${roleId}`,
    method: 'delete'
  });
}

// 获取菜单列表
export function getMenuList(query) {
  return request({
    url: '/system/menu/list',
    method: 'get',
    params: query
  });
}

// 获取菜单详情
export function getMenu(menuId) {
  return request({
    url: `/system/menu/${menuId}`,
    method: 'get'
  });
}

// 新增菜单
export function addMenu(data) {
  return request({
    url: '/system/menu',
    method: 'post',
    data: data
  });
}

// 修改菜单
export function updateMenu(data) {
  return request({
    url: '/system/menu',
    method: 'put',
    data: data
  });
}

// 删除菜单
export function deleteMenu(menuId) {
  return request({
    url: `/system/menu/${menuId}`,
    method: 'delete'
  });
}

// 获取部门列表
export function getDeptList(query) {
  return request({
    url: '/system/dept/list',
    method: 'get',
    params: query
  });
}

// 获取部门详情
export function getDept(deptId) {
  return request({
    url: `/system/dept/${deptId}`,
    method: 'get'
  });
}

// 新增部门
export function addDept(data) {
  return request({
    url: '/system/dept',
    method: 'post',
    data: data
  });
}

// 修改部门
export function updateDept(data) {
  return request({
    url: '/system/dept',
    method: 'put',
    data: data
  });
}

// 删除部门
export function deleteDept(deptId) {
  return request({
    url: `/system/dept/${deptId}`,
    method: 'delete'
  });
} 