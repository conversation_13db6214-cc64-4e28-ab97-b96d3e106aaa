<template>
  <div class="app_content">
    <!-- <div class="menu_title">班种列表</div> -->
    <div class="app_box">
      <div
        class="ban_content"
        v-for="(item, index) in tree"
        :key="index"
        @click="selButtonItem(item)"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<script setup>
const { proxy } = getCurrentInstance();
const emits = defineEmits(["miniMenuFun"]);
const props = defineProps({
  dates: {
    type: String,
  },
});

const tree = ref([
  {
    key: "sort",
    label: "设置排序",
  },
  {
    key: "group",
    label: "移动分组",
  },
  {
    key: "positive",
    label: "正序轮岗",
  },
  {
    key: "negative",
    label: "倒序轮岗",
  },
]);

const selButtonItem = (item) => {
  console.log(item.key);
  // if (item.key == 0) {
  // } else if (item.key == 1) {
  // } else if (item.key == 2) {
  // } else if (item.key == 3) {
  // }
  emits("miniMenuFun", item.key);
};
</script>

<style lang="scss" scoped>
.app_content {
  width: 100px;
  height: auto;
  padding: 0px 8px;
  background-color: #fff;
  border-radius: 10px;
  z-index: 9;
  box-shadow: 0px 2px 10px 2px rgba(0, 0, 0, 0.15), 0px 4px 5px 0px rgba(0, 0, 0, 0.12);
  overflow-y: auto;
  .app_box {
    width: 100%;
    .app_box:hover {
      // cursor: pointer;
      background-color: #f5f7fa;
    }
  }

  .ban_content {
    width: 100%;
    height: auto;
    font-size: 16px;
    padding: 2px 6px;
    margin: 14px 0;
    border-radius: 4px;
  }
  .ban_content:hover {
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.08);
  }
}
</style>
