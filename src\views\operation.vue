<template>
  <div class="operation-container">
    <div class="header">
      <h1 class="title">运维数据</h1>
      <div class="header-actions">
        <v-btn color="primary" prepend-icon="mdi-refresh" class="refresh-btn" @click="refreshData">
          刷新数据
        </v-btn>
        <v-btn variant="outlined" prepend-icon="mdi-cog" class="settings-btn" @click="openSettings">
          设置
        </v-btn>
      </div>
    </div>

    <!-- Data Cards -->
    <div class="data-cards">
      <!-- 工单数据 Card -->
      <div class="data-card">
        <div class="card-left">
          <span class="card-title">工单数据</span>
          <span class="card-number">{{ stats.workOrders.total }}</span>
          <span class="card-subtitle">今日新增: {{ stats.workOrders.today }}</span>
        </div>
        <div class="card-icon work-order-icon">
          <v-icon icon="mdi-clipboard-text-outline"></v-icon>
        </div>
      </div>

      <!-- 设备状态 Card -->
      <div class="data-card">
        <div class="card-left">
          <span class="card-title">设备状态</span>
          <span class="card-number">{{ stats.devices.total }}</span>
          <span class="card-subtitle">正常: {{ stats.devices.normal }} | 告警: {{ stats.devices.alarm }}</span>
        </div>
        <div class="card-icon device-icon">
          <v-icon icon="mdi-devices"></v-icon>
        </div>
      </div>

      <!-- 能耗数据 Card -->
      <div class="data-card">
        <div class="card-left">
          <span class="card-title">能耗数据</span>
          <span class="card-number">{{ stats.energy.value }}</span>
          <span class="card-subtitle">同比增长: {{ stats.energy.growth }}</span>
        </div>
        <div class="card-icon energy-icon">
          <v-icon icon="mdi-flash"></v-icon>
        </div>
      </div>

      <!-- 环境数据 Card -->
      <div class="data-card">
        <div class="card-left">
          <span class="card-title">环境数据</span>
          <span class="card-number">{{ stats.environment.temp }}</span>
          <span class="card-subtitle">空气质量: {{ stats.environment.airQuality }}</span>
        </div>
        <div class="card-icon environment-icon">
          <v-icon icon="mdi-thermometer"></v-icon>
        </div>
      </div>
    </div>

    <!-- Tabs -->
    <div class="tabs">
      <div 
        v-for="tab in tabs" 
        :key="tab.value"
        :class="['tab', { active: activeTab === tab.value }]"
        @click="activeTab = tab.value"
      >
        {{ tab.title }}
      </div>
    </div>

    <!-- Search Bar -->
    <div class="search-bar">
      <div class="search-input">
        <v-icon icon="mdi-magnify" class="search-icon"></v-icon>
        <input 
          v-model="searchQuery" 
          type="text" 
          placeholder="搜索数据..." 
          @input="filterTableData"
        >
      </div>
      <div class="filter-btn" @click="showFilterOptions = !showFilterOptions">
        <v-icon icon="mdi-filter-variant"></v-icon>
        筛选
        
        <!-- Filter Dropdown -->
        <div v-if="showFilterOptions" class="filter-dropdown">
          <div class="filter-option" @click="sortByTime">按时间排序</div>
          <div class="filter-option" @click="sortByName">按名称排序</div>
          <div class="filter-option" @click="filterByStatus">按状态筛选</div>
        </div>
      </div>
    </div>

    <!-- Data Table -->
    <div class="data-table">
      <table>
        <thead>
          <tr>
            <th class="checkbox-col">
              <input 
                type="checkbox" 
                @change="selectAllItems" 
                :checked="allItemsSelected"
              >
            </th>
            <th>数据名称</th>
            <th>类型</th>
            <th>数值/状态</th>
            <th>更新时间</th>
            <th>知识状态</th>
            <th class="actions-col">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in filteredTableData" :key="index">
            <td>
              <input 
                type="checkbox" 
                v-model="item.selected" 
                @change="updateSelectAllState"
              >
            </td>
            <td>
              <div class="data-name">
                <div class="data-icon" :class="getIconClass(item.type)">
                  <v-icon :icon="getIconName(item.type)"></v-icon>
                </div>
                <div class="data-info">
                  <div class="item-name">{{ item.name }}</div>
                  <div class="data-id">ID: {{ item.id }}</div>
                </div>
              </div>
            </td>
            <td>
              <div class="data-tag" :class="getTagClass(item.type)">{{ item.type }}</div>
            </td>
            <td>
              <div :class="['status-text', getStatusClass(item.status)]">{{ item.status }}</div>
            </td>
            <td>{{ item.updateTime }}</td>
            <td>
              <div class="status-tag">
                <v-icon icon="mdi-check-circle" size="small"></v-icon>
                已确认
              </div>
            </td>
            <td>
              <div class="action-buttons">
                <button class="action-btn preview-btn" @click="previewItem(item)">
                  <v-icon icon="mdi-eye" size="small"></v-icon>
                  预览
                </button>
                <button class="action-btn download-btn" @click="downloadItem(item)">
                  <v-icon icon="mdi-download" size="small"></v-icon>
                  下载
                </button>
                <button class="action-btn share-btn" @click="shareItem(item)">
                  <v-icon icon="mdi-share-variant" size="small"></v-icon>
                  分享
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div class="pagination">
      <div class="pagination-info">显示 1 到 10 条，共 {{ tableData.length }} 条</div>
      <div class="pagination-controls">
        <button class="page-btn prev" :disabled="currentPage === 1" @click="goToPreviousPage">
          <v-icon icon="mdi-chevron-left" size="small"></v-icon>
        </button>
        <button 
          v-for="page in totalPages" 
          :key="page" 
          :class="['page-btn', { active: currentPage === page }]"
          @click="goToPage(page)"
        >
          {{ page }}
        </button>
        <button class="page-btn next" :disabled="currentPage === totalPages" @click="goToNextPage">
          <v-icon icon="mdi-chevron-right" size="small"></v-icon>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// Data state
const loading = ref(false);
const showFilterOptions = ref(false);

// Active tab
const activeTab = ref('all');
const tabs = [
  { title: '全部数据', value: 'all' },
  { title: '工单数据', value: 'workOrders' },
  { title: '设备状态', value: 'devices' },
  { title: '能耗数据', value: 'energy' },
  { title: '环境数据', value: 'environment' }
];

// Search query
const searchQuery = ref('');

// Pagination
const itemsPerPage = 10;
const currentPage = ref(1);
const totalPages = computed(() => Math.ceil(tableData.value.length / itemsPerPage));

// Selection state
const allItemsSelected = ref(false);

// Stats data for cards
const stats = ref({
  workOrders: { total: 5, today: 0 },
  devices: { total: 5, normal: 4, alarm: 1 },
  energy: { value: '2,109 kWh', growth: '5.2%' },
  environment: { temp: '22°C / 45%', airQuality: '良好' }
});

// Table raw data
const tableData = ref([
  {
    id: 'wo-001',
    name: '空调系统维修工单',
    type: '工单数据',
    status: '待处理',
    updateTime: '2025-04-13',
    selected: false
  },
  {
    id: 'wo-002',
    name: '安防摄像头维护工单',
    type: '工单数据',
    status: '已完成',
    updateTime: '2025-04-12',
    selected: false
  },
  {
    id: 'wo-003',
    name: '门禁系统保养工单',
    type: '工单数据',
    status: '进行中',
    updateTime: '2025-04-11',
    selected: false
  },
  {
    id: 'wo-004',
    name: '电梯年检工单',
    type: '工单数据',
    status: '待审核',
    updateTime: '2025-04-10',
    selected: false
  },
  {
    id: 'wo-005',
    name: '照明系统维修工单',
    type: '工单数据',
    status: '已完成',
    updateTime: '2025-04-09',
    selected: false
  },
  {
    id: 'dev-001',
    name: '中央空调主机',
    type: '设备状态',
    status: '正常',
    updateTime: '2025-04-13',
    selected: false
  },
  {
    id: 'dev-002',
    name: '1号电梯',
    type: '设备状态',
    status: '告警',
    updateTime: '2025-04-12',
    selected: false
  },
  {
    id: 'dev-003',
    name: '配电室变压器',
    type: '设备状态',
    status: '正常',
    updateTime: '2025-04-13',
    selected: false
  },
  {
    id: 'dev-004',
    name: '消防泵',
    type: '设备状态',
    status: '正常',
    updateTime: '2025-04-13',
    selected: false
  },
  {
    id: 'dev-005',
    name: '新风系统',
    type: '设备状态',
    status: '正常',
    updateTime: '2025-04-13',
    selected: false
  }
]);

// Filtered table data based on active tab and search query
const filteredTableData = computed(() => {
  let data = tableData.value;
  
  // Filter by tab
  if (activeTab.value !== 'all') {
    const typeMap = {
      workOrders: '工单数据',
      devices: '设备状态',
      energy: '能耗数据',
      environment: '环境数据'
    };
    
    data = data.filter(item => item.type === typeMap[activeTab.value]);
  }
  
  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    data = data.filter(item => 
      item.name.toLowerCase().includes(query) || 
      item.id.toLowerCase().includes(query) ||
      item.status.toLowerCase().includes(query)
    );
  }
  
  return data;
});

// Helper function to get icon class based on data type
const getIconClass = (type) => {
  if (type === '工单数据') return 'work-order-small';
  if (type === '设备状态') return 'device-small';
  if (type === '能耗数据') return 'energy-small';
  if (type === '环境数据') return 'environment-small';
  return '';
};

// Helper function to get icon name based on data type
const getIconName = (type) => {
  if (type === '工单数据') return 'mdi-clipboard-text-outline';
  if (type === '设备状态') return 'mdi-devices';
  if (type === '能耗数据') return 'mdi-flash';
  if (type === '环境数据') return 'mdi-thermometer';
  return '';
};

// Helper function to get tag class based on data type
const getTagClass = (type) => {
  if (type === '工单数据') return 'work-order-tag';
  if (type === '设备状态') return 'device-tag';
  if (type === '能耗数据') return 'energy-tag';
  if (type === '环境数据') return 'environment-tag';
  return '';
};

// Helper function to get status class
const getStatusClass = (status) => {
  if (status === '正常') return 'status-normal';
  if (status === '告警') return 'status-alarm';
  if (status === '待处理') return 'status-pending';
  if (status === '进行中') return 'status-progress';
  if (status === '已完成') return 'status-completed';
  if (status === '待审核') return 'status-review';
  return '';
};

// Select all items
const selectAllItems = (event) => {
  const checked = event.target.checked;
  allItemsSelected.value = checked;
  tableData.value.forEach(item => {
    item.selected = checked;
  });
};

// Update select all checkbox state
const updateSelectAllState = () => {
  allItemsSelected.value = tableData.value.every(item => item.selected);
};

// Refresh data
const refreshData = () => {
  loading.value = true;
  // Simulate data refresh
  setTimeout(() => {
    loading.value = false;
    window.ElMessage.success('数据已更新');
  }, 800);
};

// Open settings
const openSettings = () => {
  window.ElMessage.info('打开设置面板');
};

// Filter table data
const filterTableData = () => {
  currentPage.value = 1; // Reset to first page on filter
};

// Sort by time
const sortByTime = () => {
  tableData.value.sort((a, b) => new Date(b.updateTime) - new Date(a.updateTime));
  showFilterOptions.value = false;
  window.ElMessage.success('已按时间排序');
};

// Sort by name
const sortByName = () => {
  tableData.value.sort((a, b) => a.name.localeCompare(b.name));
  showFilterOptions.value = false;
  window.ElMessage.success('已按名称排序');
};

// Filter by status
const filterByStatus = () => {
  // For demo, just filter to show only normal status
  tableData.value = tableData.value.filter(item => item.status === '正常');
  showFilterOptions.value = false;
  window.ElMessage.success('已按状态筛选');
};

// Pagination methods
const goToPage = (page) => {
  currentPage.value = page;
};

const goToPreviousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const goToNextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

// Item actions
const previewItem = (item) => {
  window.ElMessage.info(`预览 ${item.name}`);
};

const downloadItem = (item) => {
  window.ElMessage.success(`正在下载 ${item.name}`);
};

const shareItem = (item) => {
  window.ElMessage.info(`分享 ${item.name}`);
};

// Initialize
onMounted(() => {
  // Anything we need to do on component mount
});
</script>

<style scoped>
.operation-container {
  padding: 20px;
  font-family: Arial, sans-serif;
  color: #333;
  background-color: #f5f5f5;
  max-width: 100%;
  overflow-x: hidden;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 15px;
}

.title {
  font-size: 22px;
  font-weight: 600;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.refresh-btn {
  height: 36px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.refresh-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.refresh-btn:active {
  transform: translateY(0);
}

.settings-btn {
  height: 36px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.settings-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.settings-btn:active {
  transform: translateY(0);
}

/* Data Cards */
.data-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.data-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-left: 4px solid transparent;
  min-height: 100px;
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
}

.data-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.data-card:nth-child(1) {
  border-left-color: #4569ff;
}

.data-card:nth-child(2) {
  border-left-color: #36b37e;
}

.data-card:nth-child(3) {
  border-left-color: #ffab00;
}

.data-card:nth-child(4) {
  border-left-color: #805ad5;
}

.card-left {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.card-title {
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.card-number {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 5px;
  transition: color 0.3s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.data-card:hover .card-number {
  color: #4569ff;
}

.card-subtitle {
  color: #888;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: transform 0.3s, background-color 0.3s;
  flex-shrink: 0;
  margin-left: 10px;
}

.data-card:hover .card-icon {
  transform: scale(1.1) rotate(5deg);
}

.work-order-icon {
  background-color: #ebefff;
  color: #4569ff;
}

.data-card:hover .work-order-icon {
  background-color: #4569ff;
  color: white;
}

.device-icon {
  background-color: #e8f8f0;
  color: #36b37e;
}

.data-card:hover .device-icon {
  background-color: #36b37e;
  color: white;
}

.energy-icon {
  background-color: #fff8e6;
  color: #ffab00;
}

.data-card:hover .energy-icon {
  background-color: #ffab00;
  color: white;
}

.environment-icon {
  background-color: #f0e7ff;
  color: #805ad5;
}

.data-card:hover .environment-icon {
  background-color: #805ad5;
  color: white;
}

/* Tabs */
.tabs {
  display: flex;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 24px;
  background: white;
  border-radius: 4px 4px 0 0;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
}

.tabs::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.tab {
  padding: 12px 20px;
  font-size: 14px;
  cursor: pointer;
  position: relative;
  transition: color 0.3s, background-color 0.3s;
  white-space: nowrap;
}

.tab.active {
  color: #4569ff;
  font-weight: 500;
}

.tab::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #4569ff;
  transform: scaleX(0);
  transition: transform 0.3s;
}

.tab.active::after {
  transform: scaleX(1);
}

.tab:hover:not(.active) {
  color: #6988ff;
  background-color: #f8f9ff;
}

.tab:hover::after {
  transform: scaleX(1);
  opacity: 0.5;
}

/* Search Bar */
.search-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.search-input {
  flex-grow: 1;
  position: relative;
  min-width: 200px;
}

.search-input input {
  width: 100%;
  padding: 10px 10px 10px 36px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.search-input input:focus {
  outline: none;
  border-color: #4569ff;
  box-shadow: 0 0 0 3px rgba(69, 105, 255, 0.1);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #888;
  transition: color 0.3s;
}

.search-input input:focus + .search-icon {
  color: #4569ff;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 10px 16px;
  background-color: white;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  position: relative;
  transition: background-color 0.3s, border-color 0.3s;
}

.filter-btn:hover {
  background-color: #f8f9ff;
  border-color: #d0d8ff;
}

.filter-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 160px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
  margin-top: 5px;
  animation: fadeIn 0.2s ease-out;
  transform-origin: top right;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.filter-option {
  padding: 10px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
  border-bottom: 1px solid #f0f0f0;
}

.filter-option:last-child {
  border-bottom: none;
}

.filter-option:hover {
  background-color: #f5f5f5;
  color: #4569ff;
}

/* Data Table */
.data-table {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  transition: box-shadow 0.3s;
}

.data-table:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

table {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
}

th, td {
  padding: 15px;
  text-align: left;
  font-size: 14px;
  border-bottom: 1px solid #f0f0f0;
}

th {
  color: #666;
  font-weight: 500;
  background-color: #f8f8f8;
  transition: background-color 0.3s;
  position: sticky;
  top: 0;
  z-index: 1;
}

tbody tr {
  transition: background-color 0.2s, transform 0.2s;
  cursor: pointer;
}

tbody tr:hover {
  background-color: #f8f9ff;
  transform: translateX(3px);
}

tbody tr:active {
  background-color: #f0f4ff;
}

.checkbox-col {
  width: 40px;
}

.actions-col {
  text-align: center;
}

input[type="checkbox"] {
  cursor: pointer;
  width: 16px;
  height: 16px;
  border: 1px solid #ccc;
  border-radius: 3px;
  position: relative;
  transition: border-color 0.2s, background-color 0.2s;
}

input[type="checkbox"]:hover {
  border-color: #4569ff;
}

input[type="checkbox"]:checked {
  background-color: #4569ff;
  border-color: #4569ff;
}

.data-name {
  display: flex;
  align-items: center;
  gap: 12px;
}

.data-icon {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: transform 0.3s, background-color 0.3s, color 0.3s;
  flex-shrink: 0;
}

tr:hover .data-icon {
  transform: scale(1.05);
}

.work-order-small {
  background-color: #ebefff;
  color: #4569ff;
}

tr:hover .work-order-small {
  background-color: #4569ff;
  color: white;
}

.device-small {
  background-color: #e8f8f0;
  color: #36b37e;
}

tr:hover .device-small {
  background-color: #36b37e;
  color: white;
}

.energy-small {
  background-color: #fff8e6;
  color: #ffab00;
}

tr:hover .energy-small {
  background-color: #ffab00;
  color: white;
}

.environment-small {
  background-color: #f0e7ff;
  color: #805ad5;
}

tr:hover .environment-small {
  background-color: #805ad5;
  color: white;
}

.data-info {
  display: flex;
  flex-direction: column;
  gap: 3px;
  transition: transform 0.2s;
  overflow: hidden;
}

tr:hover .data-info {
  transform: translateX(3px);
}

.item-name {
  font-weight: 500;
  transition: color 0.2s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

tr:hover .item-name {
  color: #4569ff;
}

.data-id {
  color: #888;
  font-size: 12px;
}

.data-tag {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 12px;
  transition: transform 0.2s, opacity 0.2s;
  white-space: nowrap;
}

tr:hover .data-tag {
  transform: scale(1.05);
}

.work-order-tag {
  background-color: #ebefff;
  color: #4569ff;
}

.device-tag {
  background-color: #e8f8f0;
  color: #36b37e;
}

.energy-tag {
  background-color: #fff8e6;
  color: #ffab00;
}

.environment-tag {
  background-color: #f0e7ff;
  color: #805ad5;
}

.status-text {
  font-weight: 500;
  transition: transform 0.2s;
  white-space: nowrap;
}

tr:hover .status-text {
  transform: scale(1.05);
}

.status-normal {
  color: #36b37e;
}

.status-alarm {
  color: #f44336;
}

.status-pending {
  color: #ffab00;
}

.status-progress {
  color: #4569ff;
}

.status-completed {
  color: #36b37e;
}

.status-review {
  color: #ffab00;
}

.status-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 10px;
  background-color: #e6f7ed;
  color: #2ea160;
  border-radius: 4px;
  font-size: 12px;
  transition: transform 0.2s, box-shadow 0.2s;
  white-space: nowrap;
}

tr:hover .status-tag {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 5px;
  opacity: 0.7;
  transition: opacity 0.3s;
  flex-wrap: wrap;
}

tr:hover .action-buttons {
  opacity: 1;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 3px;
  padding: 5px 8px;
  border: none;
  background: none;
  color: #4569ff;
  cursor: pointer;
  font-size: 12px;
  transition: transform 0.2s, color 0.2s, background-color 0.2s;
  border-radius: 4px;
  white-space: nowrap;
}

.action-btn:hover {
  transform: translateY(-2px);
  background-color: #f0f4ff;
}

.action-btn:active {
  transform: translateY(0);
}

.preview-btn:hover {
  color: #4569ff;
}

.download-btn:hover {
  color: #36b37e;
}

.share-btn:hover {
  color: #ffab00;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  flex-wrap: wrap;
  gap: 15px;
}

.pagination-info {
  white-space: nowrap;
}

.pagination-controls {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.page-btn {
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #e8e8e8;
  background-color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.page-btn.active {
  background-color: #4569ff;
  color: white;
  border-color: #4569ff;
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(69, 105, 255, 0.3);
}

.page-btn:not(.active):hover {
  background-color: #f5f5f5;
  border-color: #d0d8ff;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.page-btn:not(.active):active {
  transform: translateY(0);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.prev, .next {
  transition: transform 0.2s;
}

.prev:not(:disabled):hover {
  transform: translateX(-2px);
}

.next:not(:disabled):hover {
  transform: translateX(2px);
}

/* Media Queries for Responsive Design */
@media (max-width: 1200px) {
  .data-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .data-cards {
    grid-template-columns: 1fr;
  }
  
  .header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .pagination {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .search-bar {
    flex-direction: column;
  }
  
  .search-input {
    width: 100%;
  }
  
  .filter-btn {
    align-self: flex-start;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .action-btn {
    width: 100%;
    justify-content: flex-start;
  }
}

@media (max-width: 480px) {
  .operation-container {
    padding: 10px;
  }
  
  .title {
    font-size: 18px;
  }
  
  .data-card {
    padding: 15px;
  }
  
  .card-number {
    font-size: 20px;
  }
  
  th, td {
    padding: 10px 5px;
    font-size: 12px;
  }
  
  .data-name {
    gap: 8px;
  }
  
  .data-icon {
    width: 30px;
    height: 30px;
  }
  
  .item-name {
    max-width: 150px;
  }
}
</style>