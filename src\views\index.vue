<template>
  <div class="app-container home">医疗云</div>
</template>

<script setup name="Index">
import Cookies from "js-cookie";
import { getUserProfile } from "@/api/system/user";

const version = ref("3.8.7");

function goTarget(href) {
  window.open(href, "_blank");
}

function getUser() {
  getUserProfile().then((response) => {
    console.log(response);
    localStorage.setItem("userId", response.data.userId)
    localStorage.setItem("userName", response.data.userName)
    localStorage.setItem("deptId", response.data.deptId)
    localStorage.setItem("deptName", response.data.dept.deptName)
    localStorage.setItem("roles", response.data.roles)
    localStorage.setItem("roleKey", response.data.roles[0].roleKey)
    localStorage.setItem("permissions", response.data.roles[0].permissions)
    // state.user = response.data;
    // state.roleGroup = response.roleGroup;
    // state.postGroup = response.postGroup;
  });
}

getUser();

// export default {
//   name: "Index",
//   data() {
//     return {
//       // 版本号
//       version: "3.8.7"
//     };
//   },
//   methods: {
//     goTarget(href) {
//       window.open(href, "_blank");
//     }
//   }
// };
</script>

<style scoped lang="scss">
.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }

  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }

  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans",
  "Helvetica Neue",
  Helvetica,
  Arial,
  sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }
}
</style>
