<template>
  <div class="login">
    <div class="login-container">
      <div class="login-left">
        <img class="loginForm_img_logo" :src="loginLogo" alt="">
        <img class="loginForm_img_bg" :src="clIcon" alt="">
      </div>
      <div class="login-right">
        <h3 class="title">知识库登录</h3>

        <!-- 账号登录表单 -->
        <div v-if="!qrCodeMode">
          <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
            <el-form-item prop="username">
              <el-input v-model="loginForm.username" type="text" size="large" auto-complete="off" placeholder="账号/手机号">
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input v-model="loginForm.password" type="password" size="large" auto-complete="off" placeholder="密码"
                @keyup.enter="handleLogin">
              </el-input>
            </el-form-item>
            <el-form-item prop="code" v-if="captchaEnabled && captchaType != 'phone'">
              <div class="captcha-container">
                <el-input v-model="loginForm.code" size="large" auto-complete="off" placeholder="验证码"
                  class="captcha-input" @keyup.enter="handleLogin">
                </el-input>
                <img :src="codeUrl" @click="getCode" class="captcha-img" />
              </div>
            </el-form-item>
            <div class="remember-password">
              <el-checkbox v-model="loginForm.rememberMe">记住密码</el-checkbox>
              <a href="javascript:;" class="forgot-password" @click="showForgotPassword">忘记密码?</a>
            </div>
            <el-form-item>
              <el-button :loading="loading" size="large" type="primary" class="login-button" @click="handleLogin">
                <span v-if="!loading">登 录</span>
                <span v-else>登 录 中...</span>
              </el-button>
            </el-form-item>
          </el-form>
          <!-- <div class="login-mode-switch">
            <a href="javascript:;" @click="switchToQRCode">二维码登录</a>
          </div> -->
        </div>

        <!-- 二维码登录 -->
        <div v-else class="qrcode-login-container">
          <div style="height: 210px;width: 100%;">
            <wxlogin :appid="appid" :scope="scope" :redirect_uri="callBackUrl"
              href="data:text/css;base64,LmltcG93ZXJCb3ggLnN0YXR1cy5zdGF0dXNfYnJvd3NlcnsKICBkaXNwbGF5OiBub25lOwp9Ci50aXRsZXsKICBkaXNwbGF5OiBub25lOwp9Ci5pbXBvd2VyQm94IC5xcmNvZGV7CiAgd2lkdGg6IDIwMHB4OwogIGJvcmRlcjogbm9uZTsKfQ==">
            </wxlogin>
          </div>
          <p class="qrcode-tip">微信扫码登录</p>
          <p class="qrcode-desc">请打开微信扫一扫，扫描上方二维码进行登录</p>
          <div class="login-mode-switch">
            <a href="javascript:;" @click="switchToAccount">账号登录</a>
          </div>
        </div>
      </div>
    </div>

    <!-- 手机验证码登录对话框 -->
    <el-dialog v-model="isPhone" :title="title" width="400px" style="top: 20%" :before-close="handleClose"
      append-to-body :close-on-click-modal="false">
      <el-form ref="formCode" :model="formCode" :rules="codeRules" @submit.prevent>
        <div class="form_text">验证注册手机号：{{ phoneNumber }}</div>
        <el-form-item prop="code">
          <el-input v-model="formCode.code" placeholder="请输入验证码" style="width: 60%" @keyup.enter="submitCode" />
          <el-button type="primary" @click="getMobileCodeByLogin(realPhoneNumber)" :disabled="show"
            style="width: 35%; margin-left: 5%">{{ codeText }}</el-button>
        </el-form-item>
      </el-form>
      <el-button class="form_submit" type="primary" @click="submitCode">确 认</el-button>
    </el-dialog>

    <!-- 忘记密码对话框 -->
    <el-dialog v-model="forgotPasswordVisible" title="忘记密码" width="400px" :show-close="true" append-to-body center
      class="forgot-password-dialog">
      <el-form ref="forgotPasswordForm" :model="forgotPasswordForm" :rules="forgotPasswordRules">
        <el-form-item prop="phone">
          <el-input v-model="forgotPasswordForm.phone" placeholder="手机号" prefix-icon="el-icon-user">
            <template #prefix>
              <i class="el-icon-user">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" data-v-78e17ca8="" class="svg-icon"
                  style="width: 1em; height: 1em; color: #bfbfbf;">
                  <path fill="currentColor"
                    d="M628.736 528.896A416 416 0 0 1 928 928H96a415.872 415.872 0 0 1 299.264-399.104L512 704l116.736-175.104zM720 304a208 208 0 1 1-416 0 208 208 0 0 1 416 0z">
                  </path>
                </svg>
              </i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="verificationCode">
          <div class="verification-code-container">
            <el-input v-model="forgotPasswordForm.verificationCode" placeholder="请输入验证码" />
            <el-button type="primary" :disabled="forgotCodeTimer > 0" @click="getForgotPasswordCode"
              class="verification-code-button">
              {{ forgotCodeTimer > 0 ? `${forgotCodeTimer}秒后重试` : '获取验证码' }}
            </el-button>
          </div>
        </el-form-item>
        <el-form-item prop="newPassword">
          <el-input v-model="forgotPasswordForm.newPassword" type="password" placeholder="新密码">
            <template #prefix>
              <i class="el-icon-lock">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" data-v-78e17ca8="" class="svg-icon"
                  style="width: 1em; height: 1em; color: #bfbfbf;">
                  <path fill="currentColor"
                    d="M224 448a32 32 0 0 0-32 32v384a32 32 0 0 0 32 32h576a32 32 0 0 0 32-32V480a32 32 0 0 0-32-32H224zm0-64h576a96 96 0 0 1 96 96v384a96 96 0 0 1-96 96H224a96 96 0 0 1-96-96V480a96 96 0 0 1 96-96z">
                  </path>
                  <path fill="currentColor"
                    d="M512 544a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V576a32 32 0 0 1 32-32zm192-160v-64a192 192 0 1 0-384 0v64h384zM512 64a256 256 0 0 1 256 256v128H256V320A256 256 0 0 1 512 64z">
                  </path>
                </svg>
              </i>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <el-button type="primary" class="confirm-button" @click="resetPassword">确 认</el-button>
    </el-dialog>

    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © 2018-2022 Tidal Star. All Rights Reserved.</span>
    </div>
  </div>
</template>

<script setup>
import {
  getCodeImg,
  getPublicKey,
  getPhone,
  checkSms,
  sendSms,
  sendSmsByPhone,
} from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import useUserStore from "@/store/modules/user";
import { getToken, removeToken, setIsCode, setCaptchaType, setToken } from "@/utils/auth";
import { ref, watch, getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import wxlogin from "vue-wxlogin";
import axios from 'axios';
// import backImg from "@/assets/images/<EMAIL>";
import loginIcon from "@/assets/images/login_icon.png";
import clIcon from "@/assets/images/clIcon.png";
import loginLogo from "@/assets/images/loginLogo.png";
import { baseURL } from '@/utils/request'
const userStore = useUserStore();
const route = useRoute();
const router = useRouter(); 
const { proxy } = getCurrentInstance();
const codeUrl = ref("");
const qrcodeUrl = ref("");
const qrCodeMode = ref(false);
const appid = ref("wx17d7030b44da5dc7");
const scope = ref("snsapi_login");
const callBackUrl = ref("https%3A%2F%2Fchat.zcznbj.com%2Flogin%3Fredirect%3D%252Findex");
const loginForm = ref({
  username: "",
  password: "",
  rememberMe: false,
  code: "",
  uuid: "",
});

// 忘记密码相关
const forgotPasswordVisible = ref(false);
const forgotPasswordForm = ref({
  phone: "",
  verificationCode: "",
  newPassword: ""
});
const forgotPasswordRules = {
  phone: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号", trigger: "blur" }
  ],
  verificationCode: [
    { required: true, message: "请输入验证码", trigger: "blur" }
  ],
  newPassword: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    { min: 6, message: "密码长度不能少于6位", trigger: "blur" }
  ]
};
const forgotCodeTimer = ref(0);
const forgotCodeInterval = ref(null);

const showForgotPassword = () => {
  forgotPasswordVisible.value = true;
};

const getForgotPasswordCode = () => {
  if (forgotPasswordForm.value.phone === '') {
    proxy.$message.error('请先输入手机号');
    return;
  }

  if (!/^1[3-9]\d{9}$/.test(forgotPasswordForm.value.phone)) {
    proxy.$message.error('请输入正确的手机号');
    return;
  }

  // 这里应该调用发送验证码的API
  // sendSmsByPhone(forgotPasswordForm.value.phone).then(res => {
  //   proxy.$message.success('验证码发送成功');
  // }).catch(err => {
  //   proxy.$message.error('验证码发送失败');
  // });

  // 模拟发送成功
  proxy.$message.success('验证码发送成功');

  // 开始倒计时
  forgotCodeTimer.value = 60;
  forgotCodeInterval.value = setInterval(() => {
    forgotCodeTimer.value--;
    if (forgotCodeTimer.value <= 0) {
      clearInterval(forgotCodeInterval.value);
    }
  }, 1000);
};

const resetPassword = () => {
  proxy.$refs.forgotPasswordForm.validate(valid => {
    if (valid) {
      // 这里应该调用重置密码的API
      // 假设重置成功后关闭对话框
      proxy.$message.success('密码重置成功，请使用新密码登录');
      forgotPasswordVisible.value = false;
      forgotPasswordForm.value = {
        phone: "",
        verificationCode: "",
        newPassword: ""
      };
    }
  });
};

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }],
};
const codeRules = {
  code: [{ required: true, trigger: "blur", message: "验证码不能为空" }],
};
const TELRGE = "/^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/";
const sysTitle = ref("");
const formCode = ref({});
const isPhone = ref(false);
const show = ref(false);
const phoneNumber = ref("");
const realPhoneNumber = ref("");
const codeText = ref("获取验证码");
const title = ref("验证码登录");
const captchaType = ref("");
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);
const publicKey = ref("");
const backImg = ref("");
// const baseURL = process.env.VUE_APP_BASE_API
console.log('baseURL', baseURL);

// 切换到二维码登录
const switchToQRCode = () => {
  qrCodeMode.value = true;
  // 这里可以调用获取二维码的API
  // 临时使用静态二维码图片
  // qrcodeUrl.value = "data:image/png;base64,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";
};

// 切换到账号登录
const switchToAccount = () => {
  qrCodeMode.value = false;
};

watch(
  route,
  (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect;

    // Check if code parameter exists in route query
    if (newRoute.query.code) {
      const url = baseURL + '/app/api/city/manageLoginApi/webByCode?code=' + newRoute.query.code;
      axios({
        method: 'get',
        url: url,
        headers: {
          "Content-Type": "application/json;charset=utf-8"
        }
      }).then(async (res) => {
        if (res.data.code == 200) {
          setToken(res.data.data.token);
          router.push("/").catch(() => { });
        }
      });
    }
  },
  { immediate: true }
);
const getPublicKeyValue = async () => {
  const result = await getPublicKey();
  publicKey.value = result.publicKey;
};

function getCode() {
  getCodeImg().then((res) => {
    console.log('返回來的', res);

    // 获取背景图
    if (res.backImg === undefined || res.backImg.trim() === "") {
      backImg.value = `background-image: url('${backImg.value}');`;
    } else {
      backImg.value = 'background-image: url("' + res.backImg.value + '");';
    }
    // 获取标题
    if (res.title === undefined || res.title.trim() === "") {
      sysTitle.value = import.meta.env.VITE_APP_TITLE || '知识库项目';
    } else {
      sysTitle.value = res.title;
      window.document.title = res.title;
    }
    Cookies.set("sysTitle", sysTitle);
    captchaType.value = res.captchaType;
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled;

    if (captchaEnabled.value && captchaType.value !== "phone") {
      codeUrl.value = "data:image/gif;base64," + res.img;
      loginForm.value.uuid = res.uuid;
      setIsCode("true");
    } else {
      // 验证phone时需要存入
      setIsCode("false");
    }
    setCaptchaType(captchaType.value);
  });
}

function getCookie() {
  const username = Cookies.get("username");
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password:
      password === undefined
        ? loginForm.value.password
        : decrypt(password, publicKey.value),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
  };
}

getPublicKeyValue();
getCode();
getCookie();

const handleLogin = async () => {
  // 有token后再次点击登陆清除token重新获取
  // getPublicKeyValue();
  if (getToken()) {
    removeToken();
  }
  proxy.$refs.loginRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set("username", loginForm.value.username, { expires: 30 });
        Cookies.set("password", encrypt(loginForm.value.password), { expires: 30 });
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 });
      } else {
        // 否则移除
        Cookies.remove("username");
        Cookies.remove("password");
        Cookies.remove("rememberMe");
      }

      const password = encrypt(loginForm.value.password, publicKey.value);
      const data = {
        code: loginForm.value.code,
        password: password,
        username: loginForm.value.username,
        uuid: loginForm.value.uuid,
        rememberMe: loginForm.value.rememberMe,
      };
      // 调用action的登录方法
      userStore
        .login(data)
        .then(() => {
          if (captchaType.value === "phone") {
            clearTimer();
            loading.value = false;
            isPhone.value = true;
            getPhone().then((res) => {
              realPhoneNumber.value = res.phone;
              phoneNumber.value = res.phone.substr(0, 3) + "****" + res.phone.substr(7);
            });
            return;
          }
          router.push("/");
        })
        .catch(() => {
          loading.value = false;
          // 重新获取验证码
          if (captchaEnabled.value) {
            getCode();
          }
        });
    }
  });
};
function handleClose(done) {
  proxy.$modal
    .confirm("确认关闭？")
    .then((_) => {
      done();
    })
    .catch((_) => { });
}

// 验证码倒计时
const timer = ref(null);
function getMobileCodeByLogin(phoneNumber) {
  sendSms(phoneNumber).then((res) => {
    if (res.code == 200) {
      proxy.$modal.msgSuccess(res.msg);
    } else {
      proxy.$modal.msgSuccess(res.msg);
    }
  });
  const TIME_COUNT = 60;
  if (!timer.value) {
    let count = TIME_COUNT;
    show.value = true;
    timer.value = setInterval(() => {
      if (count > 0 && count <= TIME_COUNT) {
        count--;
        codeText.value = `${count}s后重新获取`;
      } else {
        clearTimer();
      }
    }, 1000);
  }
}

// 验证码提交
function submitCode() {
  proxy.$refs.formCode.validate((valid) => {
    if (valid) {
      checkSms(realPhoneNumber.value, formCode.value.code).then((res) => {
        if (res.isOk) {
          // 手机验证时需要存入
          setIsCode("true");
          setCaptchaType(captchaType.value);
          router.push("/");
        } else {
          proxy.$modal.msgError("验证码错误");
        }
      });
    }
  });
}
// 清除倒计时
function clearTimer() {
  show.value = false;
  codeText.value = "获取验证码";
  clearInterval(timer.value);
  timer.value = null;
}
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
  background-color: #0a1d6e;
  position: relative;
}

.login-container {
  display: flex;
  width: 850px;
  height: 480px !important;
  min-height: 480px;
  max-height: 480px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.login-left {
  position: relative;
  width: 350px;
  height: 440px;
}

.logo {
  width: 320px;
  height: auto;
}

.login-right {
  flex: 1;
  background-color: #fff;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 350px;
  height: 440px;
}

.title {
  margin: 10px 0 20px 0;
  text-align: center;
  color: #333;
  font-size: 22px;
  font-weight: normal;
}

.login-form {
  width: 100%;
}

.el-input {
  height: 40px;
  margin-bottom: 15px;

  :deep(.el-input__wrapper) {
    border-radius: 4px;
    box-shadow: none;
    border: 1px solid #dcdfe6;
  }

  :deep(.el-input__inner) {
    height: 38px;
    line-height: 38px;
  }
}

.captcha-container {
  display: flex;
  gap: 10px;
}

.captcha-input {
  flex: 1;
}

.loginForm_img_logo {
  position: absolute;
  top: 30px;
  left: 10%;
  width: 80%;
}

.loginForm_img_bg {
  width: 100%;
  height: 100%;
}

.captcha-img {
  height: 40px;
  border-radius: 4px;
  cursor: pointer;
}

.remember-password {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.forgot-password {
  color: #909399;
  font-size: 13px;
  text-decoration: none;
}

.login-button {
  width: 100%;
  height: 40px;
  background-color: #409eff;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  margin-top: 5px;
}

.login-mode-switch {
  text-align: center;
  margin-top: 10px;
}

.login-mode-switch a {
  color: #409eff;
  text-decoration: none;
  font-size: 13px;
}

.qrcode-login-container {
  width: 100%;
  text-align: center;
  padding: 40px 0;
}

.qrcode-box {
  width: 180px;
  height: 180px;
  margin: 30px auto;
}

.qrcode-img {
  width: 100%;
  height: 100%;
}

.qrcode-tip {
  font-size: 18px;
  margin-bottom: 10px;
}

.qrcode-desc {
  color: #909399;
  font-size: 14px;
  margin-bottom: 20px;
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: absolute;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.form_text {
  width: 100%;
  height: 30px;
  font-size: 16px;
}

.form_submit {
  width: 100%;
}

.confirm-button {
  width: 100%;
  height: 40px;
  background-color: #409eff;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  margin-top: 10px;
}

.verification-code-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.verification-code-container .el-input {
  flex: 1;
}

.verification-code-button {
  min-width: 120px;
  height: 40px;
  padding: 0 15px;
  white-space: nowrap;
  font-size: 14px;
}

:deep(.el-dialog) {
  border-radius: 8px;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;

  .el-dialog__header {
    padding: 15px;
    margin: 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .el-dialog__body {
    padding: 20px;
  }
}

.forgot-password-dialog {
  :deep(.el-form-item) {
    margin-bottom: 20px;
  }

  :deep(.el-input__wrapper) {
    height: 40px;
  }
}
</style>
