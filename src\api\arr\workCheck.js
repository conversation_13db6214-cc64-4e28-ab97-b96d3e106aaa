import request from '@/utils/request'

// 查询排班规则设置列表
export function listWorkCheck(query) {
  return request({
    url: '/arr/workCheck/list',
    method: 'get',
    params: query
  })
}

// 查询排班规则设置详细
export function getWorkCheck(id) {
  return request({
    url: '/arr/workCheck/' + id,
    method: 'get'
  })
}

// 新增排班规则设置
export function addWorkCheck(data) {
  return request({
    url: '/arr/workCheck',
    method: 'post',
    data: data
  })
}

// 修改排班规则设置
export function updateWorkCheck(data) {
  return request({
    url: '/arr/workCheck/edit',
    method: 'post',
    data: data
  })
}

// 删除排班规则设置
export function delWorkCheck(id) {
  return request({
    url: '/arr/workCheck/del/' + id,
    method: 'post'
  })
}
