<template>
  <div class="app-container">
    <el-dialog
      title="验证旧手机号"
      :visible.sync="dialogVisible"
      width="500px"
      style="top: 20%"
      :before-close="closeDialog"
      append-to-body
      v-dialogDrag
      :close-on-click-modal="false"
    >
      <el-steps :active="active" finish-status="success" align-center>
        <el-step title="验证旧手机号"></el-step>
        <el-step title="绑定新手机号"></el-step>
      </el-steps>

      <el-form
        ref="validPhoneForm"
        :model="validPhoneForm"
        :rules="validPhoneRules"
        @submit.native.prevent
        v-if="!isValid"
        label-width="70px"
      >
        <el-form-item prop="phonenumber" label="手机号" label-position="right">
          <el-input
            v-model="user.phonenumber"
            placeholder="请输入旧手机号"
            style="width: 50%"
            disabled="disabled"
          />
        </el-form-item>
        <el-form-item prop="code" label="验证码">
          <el-input
            v-model="validPhoneForm.code"
            placeholder="请输入验证码"
            style="width: 50%"
          />
          <el-button
            type="primary"
            @click="getMobileCodeByLogin(user.phonenumber)"
            :disabled="show"
            style="width: 150px; margin-left: 10px"
            >{{ codeText }}</el-button
          >
        </el-form-item>
        <el-button
          class="form_submit"
          type="primary"
          @click="validOldPhone"
          :disabled="disableSubmit"
          >验证旧手机号</el-button
        >
      </el-form>

      <el-form
        ref="changePhoneForm"
        :model="changePhoneForm"
        :rules="changePhoneRule"
        @submit.native.prevent
        v-if="isValid"
      >
        <el-form-item prop="newPhoneNumber" label="手机号">
          <el-input
            v-model="changePhoneForm.newPhoneNumber"
            placeholder="请输入新手机号"
            style="width: 50%"
          />
        </el-form-item>
        <el-form-item prop="newCode" label="验证码">
          <el-input
            v-model="changePhoneForm.newCode"
            placeholder="请输入验证码"
            style="width: 50%"
          />
          <el-button
            type="primary"
            @click="getMobileCodeByLogin(changePhoneForm.newPhoneNumber)"
            :disabled="show"
            style="width: 150px; margin-left: 10px"
            >{{ codeText }}</el-button
          >
        </el-form-item>
        <el-button
          class="form_submit"
          type="primary"
          @click="submitChangePhone"
          :disabled="disableSubmit"
          >绑定新手机号</el-button
        >
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  getPublicKey,
  getPhone,
  checkSms,
  sendSms,
  sendSmsByPhone,
} from "@/api/login";
import { updateUserProfile } from "@/api/system/user";
const userStore = useUserStore();
const props = defineProps({
  user: {
    type: Object,
  },
  dialogVisible: {
    type: Boolean,
  },
});
const { proxy } = getCurrentInstance();
const validPhoneForm = ref({});
const changePhoneForm = ref({});
const show = ref(false);
const codeText = ref("获取验证码");
const captchaType = ref("");
const loading = ref(false);
const disableSubmit = ref(true);
const active = ref(0);
const isValid = ref(false);
const changePhoneRule = ref({
  newCode: [{ required: true, trigger: "blur", message: "验证码不能为空" }],
  newPhoneNumber: [
    { required: true, message: "手机号码不能为空", trigger: "blur" },
    {
      pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
});
const validPhoneRules = ref({
  code: [{ required: true, trigger: "blur", message: "验证码不能为空" }],
});


const emit = defineEmits(["closeDialog"]);
function closeDialog(done) {
  emit("closeDialog");
}
// 验证码倒计时
// function getMobileCodeByForgetPwd(phoneNumber) {
//   if (!this.TELRGE.test(phoneNumber)) {
//     this.msgSuccess("手机号格式错误！");
//     return;
//   }
//   sendSmsByPhone(phoneNumber).then((res) => {
//     if (res.code == 200) {
//       this.msgSuccess(res.msg);
//     } else {
//       this.msgSuccess(res.msg);
//     }
//   });

//   const TIME_COUNT = 60;
//   if (!this.timer) {
//     this.count = TIME_COUNT;
//     this.show = true;
//     this.timer = setInterval(() => {
//       if (this.count > 0 && this.count <= TIME_COUNT) {
//         this.count--;
//         this.codeText = `${this.count}s后重新获取`;
//       } else {
//         this.clearTimer();
//       }
//     }, 1000);
//   }
// }
// 验证码倒计时
const timer = ref();
function getMobileCodeByLogin(phoneNumber) {
  sendSms(phoneNumber).then((res) => {
    if (res.code == 200) {
      proxy.$modal.msgSuccess(res.msg);
    } else {
      proxy.$modal.msgSuccess(res.msg);
    }
  });
  const TIME_COUNT = 60;
  if (!timer.value) {
    let count = TIME_COUNT;
    show.value = true;
    timer.value = setInterval(() => {
      if (count > 0 && count <= TIME_COUNT) {
        count--;
        codeText.value = `${count}s后重新获取`;
      } else {
        clearTimer();
      }
    }, 1000);
  }
}
// 验证码提交
function validOldPhone() {
  proxy.$refs.validPhoneForm.validate((valid) => {
    if (valid) {
      checkSms(user.value.phonenumber, validPhoneForm.value.code).then((res) => {
        if (res.isOk) {
          // 手机验证时需要存入
          proxy.$modal.msgSuccess("旧手机验证码正确！");
          active.value = 1;
          isValid.value = true;
          clearTimer();
          disableSubmit.value = true;
        } else {
          proxy.$modal.msgError("验证码错误");
        }
      });
    }
  });
}
// 清除倒计时
function clearTimer() {
  show.value = false;
  codeText.value = "获取验证码";
  clearInterval(timer.value);
  timer.value = null;
}
function submitChangePhone() {
  proxy.$refs["changePhoneForm"].validate((valid) => {
    if (valid) {
      checkSms(changePhoneForm.value.newPhoneNumber, changePhoneForm.value.newCode).then(
        (res) => {
          console.log(res.isOk);
          if (res.isOk) {
            user.value.phonenumber = changePhoneForm.value.newPhoneNumber;
            updateUserProfile(user.value).then((response) => {
              proxy.$modal.msgSuccess("修改成功");
            });
            emit("closeDialog");
          } else {
            proxy.$modal.msgError("验证码错误");
          }
        }
      );
    }
  });
}

// export default {
//   props: {
//     user: {
//       type: Object,
//     },
//     dialogVisible: {
//       type: Boolean,
//     },
//   },
//   data() {
//     return {
//       validPhoneForm: {},
//       changePhoneForm: {},
//       show: false,
//       codeText: "获取验证码",
//       captchaType: "",
//       loading: false,
//       disableSubmit: true,
//       active: 0,
//       isValid: false,
//       changePhoneRule: {
//         newCode: [{ required: true, trigger: "blur", message: "验证码不能为空" }],
//         newPhoneNumber: [
//           { required: true, message: "手机号码不能为空", trigger: "blur" },
//           {
//             pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
//             message: "请输入正确的手机号码",
//             trigger: "blur",
//           },
//         ],
//       },
//       validPhoneRules: {
//         code: [{ required: true, trigger: "blur", message: "验证码不能为空" }],
//       },
//     };
//   },

//   methods: {
//     closeDialog(done) {
//       this.$emit("closeDialog");
//     },
//     // 验证码倒计时
//     getMobileCodeByForgetPwd(phoneNumber) {
//       if (!this.TELRGE.test(phoneNumber)) {
//         this.msgSuccess("手机号格式错误！");
//         return;
//       }
//       sendSmsByPhone(phoneNumber).then((res) => {
//         if (res.code == 200) {
//           this.msgSuccess(res.msg);
//         } else {
//           this.msgSuccess(res.msg);
//         }
//       });

//       const TIME_COUNT = 60;
//       if (!this.timer) {
//         this.count = TIME_COUNT;
//         this.show = true;
//         this.timer = setInterval(() => {
//           if (this.count > 0 && this.count <= TIME_COUNT) {
//             this.count--;
//             this.codeText = `${this.count}s后重新获取`;
//           } else {
//             this.clearTimer();
//           }
//         }, 1000);
//       }
//     },
//     // 验证码倒计时
//     getMobileCodeByLogin(phoneNumber) {
//       this.disableSubmit = false;
//       sendSms(phoneNumber).then((res) => {
//         if (res.code == 200) {
//           this.msgSuccess(res.msg);
//         } else {
//           this.msgSuccess(res.msg);
//         }
//       });
//       const TIME_COUNT = 60;
//       if (!this.timer) {
//         this.count = TIME_COUNT;
//         this.show = true;
//         this.timer = setInterval(() => {
//           if (this.count > 0 && this.count <= TIME_COUNT) {
//             this.count--;
//             this.codeText = `${this.count}s后重新获取`;
//           } else {
//             this.clearTimer();
//           }
//         }, 1000);
//       }
//     },
//     // 验证码提交
//     validOldPhone() {
//       this.$refs.validPhoneForm.validate((valid) => {
//         if (valid) {
//           checkSms(this.user.phonenumber, this.validPhoneForm.code).then((res) => {
//             if (res.isOk) {
//               // 手机验证时需要存入
//               this.msgSuccess("旧手机验证码正确！");
//               this.active = 1;
//               this.isValid = true;
//               this.clearTimer();
//               this.disableSubmit = true;
//             } else {
//               this.msgError("验证码错误");
//             }
//           });
//         }
//       });
//     },
//     // 清除倒计时
//     clearTimer() {
//       this.show = false;
//       this.codeText = "获取验证码";
//       clearInterval(this.timer);
//       this.timer = null;
//     },
//     closeDialog() {
//       this.$emit("closeDialog");
//     },
//     submitChangePhone() {
//       console.log("修改手机号", this.newPhoneNumber, this.newCode);
//       this.$refs["changePhoneForm"].validate((valid) => {
//         if (valid) {
//           checkSms(
//             this.changePhoneForm.newPhoneNumber,
//             this.changePhoneForm.newCode
//           ).then((res) => {
//             console.log(res.isOk);
//             if (res.isOk) {
//               this.user.phonenumber = this.changePhoneForm.newPhoneNumber;
//               updateUserProfile(this.user).then((response) => {
//                 this.$modal.msgSuccess("修改成功");
//               });
//               this.$emit("closeDialog");
//             } else {
//               this.msgError("验证码错误");
//             }
//           });
//         }
//       });
//     },
//   },
// };
</script>
<style>
.form_submit {
  width: 100%;
}
</style>
