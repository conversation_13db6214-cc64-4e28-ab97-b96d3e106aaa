<template>
  <div class="dept-management">
    <!-- 搜索和工具栏 -->
    <div class="search-bar">
      <v-row>
        <v-col cols="12" sm="6" md="3" lg="2">
          <v-text-field
            v-model="queryParams.deptName"
            label="部门名称"
            placeholder="请输入部门名称"
            clearable
            hide-details
            density="compact"
            variant="outlined"
            @keyup.enter="handleQuery"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="3" lg="2">
          <v-select
            v-model="queryParams.status"
            :items="statusOptions"
            item-title="label"
            item-value="value"
            label="状态"
            clearable
            hide-details
            density="compact"
            variant="outlined"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="3" lg="2">
          <v-btn color="primary" @click="handleQuery" class="mr-2">
            <v-icon start>mdi-magnify</v-icon>搜索
          </v-btn>
          <v-btn @click="resetQuery" variant="outlined">
            <v-icon start>mdi-refresh</v-icon>重置
          </v-btn>
        </v-col>
      </v-row>
    </div>

    <!-- 操作按钮区 -->
    <div class="table-toolbar">
      <v-btn color="primary" @click="handleAdd" class="mr-2">
        <v-icon start>mdi-plus</v-icon>新增
      </v-btn>
      <v-btn @click="handleExpandAll" class="mr-2">
        <v-icon start>mdi-arrow-expand-all</v-icon>展开/折叠
      </v-btn>
      <v-btn @click="handleRefresh" variant="outlined">
        <v-icon start>mdi-refresh</v-icon>刷新
      </v-btn>
    </div>

    <!-- 数据表格 -->
    <v-data-table
      :headers="headers"
      :items="deptList"
      :loading="loading"
      class="elevation-1 mt-4"
      :items-per-page="-1"
    >
      <!-- 部门名称列 -->
      <template #[`item.deptName`]="{ item }">
        <span :style="{ marginLeft: (item.level || 0) * 20 + 'px' }">
          <v-icon color="primary" class="mr-1">mdi-domain</v-icon>
          {{ item.deptName }}
        </span>
      </template>

      <!-- 排序列 -->
      <template #[`item.orderNum`]="{ item }">
        {{ item.orderNum }}
      </template>

      <!-- 状态列 -->
      <template #[`item.status`]="{ item }">
        <v-chip
          :color="item.status === '0' ? 'success' : 'error'"
          :text="item.status === '0' ? '正常' : '停用'"
          size="small"
        ></v-chip>
      </template>

      <!-- 创建时间列 -->
      <template #[`item.createTime`]="{ item }">
        {{ item.createTime }}
      </template>

      <!-- 操作列 -->
      <template #[`item.actions`]="{ item }">
        <v-btn icon size="small" color="primary" @click="handleAdd(item)" class="mr-1">
          <v-icon>mdi-plus</v-icon>
        </v-btn>
        <v-btn icon size="small" color="info" @click="handleEdit(item)" class="mr-1">
          <v-icon>mdi-pencil</v-icon>
        </v-btn>
        <v-btn icon size="small" color="error" @click="handleDelete(item)">
          <v-icon>mdi-delete</v-icon>
        </v-btn>
      </template>
    </v-data-table>

    <!-- 部门表单对话框 -->
    <v-dialog v-model="dialog.visible" max-width="600px">
      <v-card>
        <v-card-title>
          <span class="text-h6">{{ dialog.title }}</span>
        </v-card-title>
        <v-card-text>
          <v-form ref="formRef" v-model="formValid">
            <v-container>
              <v-row>
                <v-col cols="12" sm="6">
                  <v-select
                    v-model="form.parentId"
                    :items="parentDeptOptions"
                    item-title="deptName"
                    item-value="deptId"
                    label="上级部门"
                    required
                    :rules="[v => !!v || '上级部门不能为空']"
                  ></v-select>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-text-field
                    v-model="form.deptName"
                    label="部门名称"
                    required
                    :rules="[v => !!v || '部门名称不能为空']"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-text-field
                    v-model="form.orderNum"
                    label="显示排序"
                    type="number"
                    required
                    :rules="[v => !!v || '显示排序不能为空']"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-text-field
                    v-model="form.leader"
                    label="负责人"
                    placeholder="请输入负责人"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-text-field
                    v-model="form.phone"
                    label="联系电话"
                    placeholder="请输入联系电话"
                    :rules="[v => !v || /^1[3-9]\d{9}$/.test(v) || '手机号码格式不正确']"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-text-field
                    v-model="form.email"
                    label="邮箱"
                    placeholder="请输入邮箱"
                    :rules="[v => !v || /.+@.+\..+/.test(v) || '邮箱格式不正确']"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-radio-group v-model="form.status" row label="状态">
                    <v-radio label="正常" value="0"></v-radio>
                    <v-radio label="停用" value="1"></v-radio>
                  </v-radio-group>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" @click="submitForm">确定</v-btn>
          <v-btn @click="dialog.visible = false">取消</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { deptApi } from '../../utils/mock';

// 表格列定义
const headers = [
  { title: '部门名称', key: 'deptName' },
  { title: '排序', key: 'orderNum', width: '80px' },
  { title: '状态', key: 'status', width: '100px' },
  { title: '创建时间', key: 'createTime' },
  { title: '操作', key: 'actions', sortable: false, width: '150px' }
];

// 状态选项
const statusOptions = [
  { label: '正常', value: '0' },
  { label: '停用', value: '1' }
];

// 查询参数
const queryParams = reactive({
  deptName: '',
  status: ''
});

// 表单参数
const form = reactive({
  deptId: undefined,
  parentId: 1,
  deptName: '',
  orderNum: 0,
  leader: '',
  phone: '',
  email: '',
  status: '0'
});

// 对话框控制
const dialog = reactive({
  visible: false,
  title: '',
  type: '' // add 或 edit
});

// 数据相关
const loading = ref(false);
const deptList = ref([]);
const formRef = ref(null);
const formValid = ref(false);
const isExpandAll = ref(false);
const parentDeptOptions = ref([]);

// 获取部门列表
const getList = async () => {
  loading.value = true;
  try {
    const res = await deptApi.list(queryParams);
    
    // 处理部门数据，将树形结构扁平化并添加层级信息
    const flattenDepts = (depts, level = 0) => {
      let result = [];
      depts.forEach(dept => {
        const newDept = { ...dept, level };
        result.push(newDept);
        if (dept.children && dept.children.length > 0) {
          result = result.concat(flattenDepts(dept.children, level + 1));
        }
      });
      return result;
    };
    
    deptList.value = flattenDepts(res.data);
    
    // 构建上级部门选项
    buildParentDeptOptions(res.data);
  } catch (error) {
    console.error('获取部门列表失败', error);
    ElMessage.error('获取部门列表失败');
  } finally {
    loading.value = false;
  }
};

// 构建上级部门选项
const buildParentDeptOptions = (depts) => {
  const options = [];
  
  // 递归构建部门树形选项
  const buildOptions = (depts, prefix = '') => {
    depts.forEach(dept => {
      const deptName = prefix ? `${prefix} / ${dept.deptName}` : dept.deptName;
      options.push({ ...dept, deptName });
      if (dept.children && dept.children.length > 0) {
        buildOptions(dept.children, deptName);
      }
    });
  };
  
  buildOptions(depts);
  parentDeptOptions.value = options;
};

// 处理查询
const handleQuery = () => {
  getList();
};

// 重置查询
const resetQuery = () => {
  queryParams.deptName = '';
  queryParams.status = '';
  handleQuery();
};

// 处理刷新
const handleRefresh = () => {
  getList();
};

// 处理展开/折叠
const handleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value;
  // 实际应用中，应该根据isExpandAll的值来展开或折叠表格
  ElMessage.info(isExpandAll.value ? '展开所有部门' : '折叠所有部门');
};

// 处理新增
const handleAdd = (row) => {
  resetForm();
  dialog.visible = true;
  dialog.title = '添加部门';
  dialog.type = 'add';
  
  if (row && row.deptId) {
    form.parentId = row.deptId;
  } else {
    form.parentId = 1; // 默认为总公司
  }
};

// 处理编辑
const handleEdit = async (row) => {
  resetForm();
  dialog.visible = true;
  dialog.title = '编辑部门';
  dialog.type = 'edit';
  
  try {
    const res = await deptApi.get(row.deptId);
    if (res.data) {
      Object.assign(form, res.data);
    }
  } catch (error) {
    console.error('获取部门详情失败', error);
    ElMessage.error('获取部门详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  // 检查是否有子部门
  const hasChildren = deptList.value.some(item => item.parentId === row.deptId);
  if (hasChildren) {
    ElMessage.warning('存在下级部门，不允许删除');
    return;
  }
  
  ElMessageBox.confirm(`确认删除部门"${row.deptName}"吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deptApi.delete(row.deptId);
      ElMessage.success('删除成功');
      getList();
    } catch (error) {
      console.error('删除部门失败', error);
      ElMessage.error('删除部门失败');
    }
  }).catch(() => {});
};

// 提交表单
const submitForm = async () => {
  if (!formValid.value) {
    return;
  }
  
  // 检查是否选择自己作为父部门
  if (dialog.type === 'edit' && form.parentId === form.deptId) {
    ElMessage.warning('上级部门不能选择自己');
    return;
  }
  
  try {
    if (dialog.type === 'add') {
      await deptApi.add(form);
      ElMessage.success('添加成功');
    } else {
      await deptApi.update(form);
      ElMessage.success('修改成功');
    }
    dialog.visible = false;
    getList();
  } catch (error) {
    console.error('提交表单失败', error);
    ElMessage.error('提交表单失败');
  }
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.reset();
  }
  Object.assign(form, {
    deptId: undefined,
    parentId: 1,
    deptName: '',
    orderNum: 0,
    leader: '',
    phone: '',
    email: '',
    status: '0'
  });
};

// 页面初始化
onMounted(() => {
  getList();
});
</script>

<style scoped>
.dept-management {
  width: 100%;
}

.search-bar {
  background-color: var(--card-background);
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px var(--shadow-color);
}

.table-toolbar {
  margin-bottom: 16px;
  display: flex;
  flex-wrap: wrap;
}

.table-toolbar .v-btn {
  margin-bottom: 8px;
}

:deep(.v-data-table) {
  background-color: var(--card-background) !important;
  border-radius: 4px;
  box-shadow: 0 1px 2px var(--shadow-color) !important;
}

:deep(.v-data-table-header) {
  background-color: var(--background-color);
}
</style> 