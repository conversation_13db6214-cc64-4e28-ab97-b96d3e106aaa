import request from '@/utils/request'

// 查询排班申请休假列表
export function listTakeHoliday(query) {
  return request({
    url: '/arr/takeHoliday/list',
    method: 'get',
    params: query
  })
}

// 查询排班申请休假详细
export function getTakeHoliday(id) {
  return request({
    url: '/arr/takeHoliday/' + id,
    method: 'get'
  })
}

// 新增排班申请休假
export function addTakeHoliday(data) {
  return request({
    url: '/arr/takeHoliday',
    method: 'post',
    data: data
  })
}

// 修改排班申请休假
export function updateTakeHoliday(data) {
  return request({
    url: '/arr/takeHoliday/edit',
    method: 'post',
    data: data
  })
}

// 删除排班申请休假
export function delTakeHoliday(id) {
  return request({
    url: '/arr/takeHoliday/del/' + id,
    method: 'post'
  })
}
