<template>
  <div class="app_content">
    <!-- <div class="menu_title">班种列表</div> -->
    <div class="app_box">
      <div class="ban_title" style="width: 100%" @click="unpackButton(1)">
        <div style="margin-left: 6px">班种列表</div>
        <!-- <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none"
          style="transform: rotate(0deg)" class="arrow_icon1">
          <path
            d="M3.54028 6.45964L4.45952 5.54041L7.9999 9.08079L11.5403 5.54041L12.4595 6.45964L7.9999 10.9193L3.54028 6.45964Z"
            fill="black" fill-opacity="0.4" />
        </svg> -->
      </div>
      <el-divider border-style="dashed" />
      <div class="ban_content1">
        <el-row :gutter="24">
          <el-col style="text-align: center; margin-top: 6px" :span="24" v-for="(item, index) in classesList"
            :key="index">
            <el-button class="ban_item" color="#626aef" plain @click="selButtonItem(item,1)">{{
              item.shiftsName
            }}</el-button>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="app_box">
      <div class="ban_title" style="width: 100%" @click="unpackButton(2)">
        <div style="margin-left: 6px">岗位列表</div>
        <!-- <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none"
          style="transform: rotate(0deg)" class="arrow_icon2">
          <path
            d="M3.54028 6.45964L4.45952 5.54041L7.9999 9.08079L11.5403 5.54041L12.4595 6.45964L7.9999 10.9193L3.54028 6.45964Z"
            fill="black" fill-opacity="0.4" />
        </svg> -->
      </div>
      <el-divider border-style="dashed" />
      <div class="ban_content2">
        <el-row :gutter="24">
          <el-col style="text-align: center; margin-top: 6px" :span="24" v-for="(item, index) in postList" :key="index">
             <el-button class="ban_item" color="#626aef" :plain="
                postSelect.length == 0 ||
                !postSelect.some((child) => child == item.postName)
              " @click="selButtonItem(item,2)">{{
              item.postName
            }}</el-button>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
  const {
    proxy
  } = getCurrentInstance();
  const emits = defineEmits(["selButtonItem"]);
  const props = defineProps({
    classesList: {
      type: Array,
      default: [],
    },
    postList: {
      type: Array,
      default: [],
    },
    combinationsList: {
      type: Array,
      default: [],
    },
    postSelect: {
      type: Array,
      default: [],
    },
  });

  const expandTimer1 = ref(1);
  const collapseTimer1 = ref(null);
  const offsetHeight1 = ref(0);
  const stepHeight1 = ref(0);
  const expandTimer2 = ref(1);
  const collapseTimer2 = ref(null);
  const offsetHeight2 = ref(0);
  const stepHeight2 = ref(0);
  const expandTimer3 = ref(1);
  const collapseTimer3 = ref(null);
  const offsetHeight3 = ref(0);
  const stepHeight3 = ref(0);

  const unpackButton = (index) => {
    return false; //暂时禁用列表展开折叠
    let boxElement = document.querySelector(".ban_content" + index);
    boxElement.style.display = "block";
    if (index == 1) {
      stepHeight1.value = stepHeight1.value > 0 ? stepHeight1.value : boxElement.offsetHeight / 20
      // 如果expandTimer有值,则表示正在展开或已经展开，接下来进行收起动作
      if (expandTimer1.value) {
        clearInterval(expandTimer1.value);
        expandTimer1.value = null;
        let height = boxElement.offsetHeight;
        collapseTimer1.value = setInterval(() => {
          height -= stepHeight1.value;
          if (height <= 0) {
            // 高度小于等于0，代表动画完成，将数据重置
            clearInterval(collapseTimer1.value);
            offsetHeight1.value = 0;
            // 要将元素的高度置为null，不然会影响下一次展开时获取正确的高度
            boxElement.style.height = null;
            // display设为none，将元素隐藏
            boxElement.style.display = "none";
            return;
          }
          boxElement.style.height = height + "px";
        }, 10);
      } else {
        clearInterval(collapseTimer1.value);
        collapseTimer1.value = null;
        // // 获取元素总高度
        // boxElement.style.display = 'block';
        let height = 0;
        // 如果当前没有offsetHeight就要重新获取
        if (!offsetHeight1.value) {
          offsetHeight1.value = boxElement.offsetHeight;
          // 每一次给元素添加或减少的高度，除以 20 (自己设定)，跟下面定时器的每次间隔时间一起控制整个高度动画的时长，也可以给函数添加第二个时间参数，可以自由控制动画时间
          stepHeight1.value = offsetHeight1.value / 20;
        } else {
          // 如果有 offsetHeight 就代表正在进行收起动画， 应该从收起动画的当前高度进行展开
          height = boxElement.offsetHeight;
        }
        boxElement.style.height = height + "px";
        expandTimer1.value = setInterval(() => {
          height += stepHeight1.value;
          if (height >= offsetHeight1.value) {
            clearInterval(expandTimer1.value);
            expandTimer1.value = 1;
            boxElement.style.height = offsetHeight1.value + 'px';
            boxElement.style.display = "block";
            return;
          }
          boxElement.style.height = height + "px";
        }, 10);
      }
    } else if (index == 2) {
      stepHeight2.value = stepHeight2.value > 0 ? stepHeight2.value : boxElement.offsetHeight / 20
      // 如果expandTimer有值,则表示正在展开或已经展开，接下来进行收起动作
      if (expandTimer2.value) {
        clearInterval(expandTimer2.value);
        expandTimer2.value = null;
        let height = boxElement.offsetHeight;
        collapseTimer2.value = setInterval(() => {
          height -= stepHeight2.value;
          if (height <= 0) {
            // 高度小于等于0，代表动画完成，将数据重置
            clearInterval(collapseTimer2.value);
            offsetHeight2.value = 0;
            // 要将元素的高度置为null，不然会影响下一次展开时获取正确的高度
            boxElement.style.height = null;
            // display设为none，将元素隐藏
            boxElement.style.display = "none";
            return;
          }
          boxElement.style.height = height + "px";
        }, 10);
      } else {
        clearInterval(collapseTimer2.value);
        collapseTimer2.value = null;
        // // 获取元素总高度
        // boxElement.style.display = 'block';
        let height = 0;
        // 如果当前没有offsetHeight就要重新获取
        if (!offsetHeight2.value) {
          offsetHeight2.value = boxElement.offsetHeight;
          // 每一次给元素添加或减少的高度，除以 20 (自己设定)，跟下面定时器的每次间隔时间一起控制整个高度动画的时长，也可以给函数添加第二个时间参数，可以自由控制动画时间
          stepHeight2.value = offsetHeight2.value / 20;
        } else {
          // 如果有 offsetHeight 就代表正在进行收起动画， 应该从收起动画的当前高度进行展开
          height = boxElement.offsetHeight;
        }
        boxElement.style.height = height + "px";
        expandTimer2.value = setInterval(() => {
          height += stepHeight2.value;
          if (height >= offsetHeight2.value) {
            clearInterval(expandTimer2.value);
            expandTimer2.value = 1;
            boxElement.style.height = offsetHeight2.value + 'px';
            boxElement.style.display = "block";
            return;
          }
          boxElement.style.height = height + "px";
        }, 10);
      }
    } else if (index == 3) {
      stepHeight3.value = stepHeight3.value > 0 ? stepHeight3.value : boxElement.offsetHeight / 20
      // 如果expandTimer有值,则表示正在展开或已经展开，接下来进行收起动作
      if (expandTimer3.value) {
        clearInterval(expandTimer3.value);
        expandTimer3.value = null;
        let height = boxElement.offsetHeight;
        collapseTimer3.value = setInterval(() => {
          height -= stepHeight3.value;
          if (height <= 0) {
            // 高度小于等于0，代表动画完成，将数据重置
            clearInterval(collapseTimer3.value);
            offsetHeight3.value = 0;
            // 要将元素的高度置为null，不然会影响下一次展开时获取正确的高度
            boxElement.style.height = null;
            // display设为none，将元素隐藏
            boxElement.style.display = "none";
            return;
          }
          boxElement.style.height = height + "px";
        }, 10);
      } else {
        clearInterval(collapseTimer3.value);
        collapseTimer3.value = null;
        // // 获取元素总高度
        // boxElement.style.display = 'block';
        let height = 0;
        // 如果当前没有offsetHeight就要重新获取
        if (!offsetHeight3.value) {
          offsetHeight3.value = boxElement.offsetHeight;
          // 每一次给元素添加或减少的高度，除以 20 (自己设定)，跟下面定时器的每次间隔时间一起控制整个高度动画的时长，也可以给函数添加第二个时间参数，可以自由控制动画时间
          stepHeight3.value = offsetHeight3.value / 20;
        } else {
          // 如果有 offsetHeight 就代表正在进行收起动画， 应该从收起动画的当前高度进行展开
          height = boxElement.offsetHeight;
        }
        boxElement.style.height = height + "px";
        expandTimer3.value = setInterval(() => {
          height += stepHeight3.value;
          if (height >= offsetHeight3.value) {
            clearInterval(expandTimer3.value);
            expandTimer3.value = 1;
            boxElement.style.height = offsetHeight3.value + 'px';
            boxElement.style.display = "block";
            return;
          }
          boxElement.style.height = height + "px";
        }, 10);
      }
    }

    const element = document.querySelector(".arrow_icon" + index);
    element.style.transform =
      element.style.transform == "rotate(-90deg)" ? "rotate(0deg)" : "rotate(-90deg)";
  };

  const selButtonItem = (item, type) => {
    // 排版校验规则组件所需参数 开始
    var shifts_name = ""
    var shifts_id = ""
    var post_name = ""
    var post_id = ""
    if (type == 1) { //普通/班种
      shifts_name = item.shiftsName
      shifts_id = item.id
    }
    if (type == 2) { //岗位
      post_name = item.postName
      post_id = item.id
    }
    // if(type == 3){ //组合
    //   shifts_name = item.shiftsName
    //   shifts_id = item.shiftsId
    //   post_name = item.postName
    //   post_id = item.postId
    // }
    // 排版校验规则组件所需参数 结束
    emits("selButtonItem", true, shifts_name, shifts_id, post_name, post_id, type);
  };
</script>

<style lang="scss" scoped>
  .app_content {
    width: 600px;
    max-height: 500px;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    z-index: 9;
    box-shadow: 0px 2px 10px 2px rgba(0, 0, 0, 0.15), 0px 4px 5px 0px rgba(0, 0, 0, 0.12);
    overflow-y: auto;
    display: flex;
    justify-content: space-between;
    .app_box {
      width: 45%;

      .app_box:hover {
        // cursor: pointer;
        background-color: #f5f7fa;
      }

      .ban_title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #1d2129;
        font-size: 16px;
        padding: 4px 0;
        border-radius: 6px;
        margin-top: 10px;

        .arrow_icon {
          transition-duration: 0.3s;
        }
      }

      .ban_title:hover {
        cursor: pointer;
        background-color: #f5f7fa;
      }
    }

    .el-divider--horizontal {
      margin: 6px 0;
    }

    .ban_content1 {
      width: 100%;
      height: auto;
      overflow: hidden;
      // transition: height 1s;
      // -moz-transition: height 1s;
      // /* Firefox 4 */
      // -webkit-transition: height 1s;
      // /* Safari and Chrome */
      // -o-transition: height 1s;
      // /* Opera */
    }

    .ban_content2 {
      width: 100%;
      height: auto;
      overflow: hidden;
      // transition: height 1s;
      // -moz-transition: height 1s;
      // /* Firefox 4 */
      // -webkit-transition: height 1s;
      // /* Safari and Chrome */
      // -o-transition: height 1s;
      // /* Opera */
    }

    .ban_content3 {
      width: 100%;
      height: auto;
      overflow: hidden;
      // transition: height 1s;
      // -moz-transition: height 1s;
      // /* Firefox 4 */
      // -webkit-transition: height 1s;
      // /* Safari and Chrome */
      // -o-transition: height 1s;
      // /* Opera */
    }
  }
  .ban_item {
    width: 75%;
    margin: 4px 6px;
  }
</style>