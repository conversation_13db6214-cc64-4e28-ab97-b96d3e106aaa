<template>
  <div class="pdf-preview-container">
    <!-- PDF 文件头部信息 -->
    <div class="pdf-header">
      <div class="pdf-info">
        <el-button class="back-button" @click="handleBack" :icon="ArrowLeft" text>
          返回上一页
        </el-button>
        <div class="file-info">
          <img src="../assets/pdf-icon.svg" class="pdf-icon" />
          <span class="pdf-name">{{ pdfName }}</span>
          <span class="pdf-meta">上传于 {{ uploadTime }} | {{ fileSize }} | 文档结构{{ status }}</span>
        </div>
      </div>
      <div class="pdf-actions">
        <!-- 解析文档结构按钮，只对管理员和文档上传者显示 -->
        <Button style="margin-right: 20px;" type="danger" v-if="isAdminOrUploader && !isEditing"
          @click="parseDocumentFormat" :loading="parsing">
          解析文档结构
        </Button>
        <!-- <Button type="primary">Primary Button</Button> -->
        <!-- 非编辑状态显示修改设置按钮 -->

        <Button type="primary" v-if="!isEditing" @click="startEditing">修改设置</Button>
        <!-- 编辑状态显示保存和取消按钮 -->
        <template v-else>
          <Button type="primary" style="margin-right: 10px;" @click="saveSettings">保存设置</Button>
          <Button @click="cancelEditing">取消</Button>
        </template>
      </div>
    </div>

    <!-- 确认对话框 -->
    <el-dialog v-model="showConfirmDialog" title="确认提示" width="30%" :close-on-click-modal="false">
      <span>当前有未保存的修改，确定要取消编辑吗？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showConfirmDialog = false">继续编辑</el-button>
          <el-button type="primary" @click="confirmLeave">确定取消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- PDF 内容区域 -->
    <div class="pdf-content">
      <div class="pdf-left-panel">
        <!-- PDF预览区域 -->
        <div class="pdf-viewer">
          <h3>文档预览</h3>
          <VuePdfEmbed :source="pdfSource" />
        </div>

        <!-- 文档大纲 -->
        <div class="outline-section">
          <h3>一、文档摘要</h3>
          <p>{{ documentSummary }}</p>
        </div>

        <!-- 关键词 -->
        <div class="keywords-section">
          <h3>二、关键词</h3>
          <div class="keyword-tags">
            <el-tag v-for="(tag, index) in keywords" :key="index" :closable="isEditing" @close="removeKeyword(index)">
              {{ tag }}
            </el-tag>
            <el-button v-if="isEditing" class="button-new-tag" size="small" @click="showKeywordInput">
              <el-icon>
                <Plus />
              </el-icon>
            </el-button>
          </div>
          <el-input v-if="keywordInputVisible" v-model="keywordInputValue" ref="keywordInputRef" size="small"
            @keyup.enter="handleKeywordInputConfirm" @blur="handleKeywordInputConfirm" />
        </div>

        <!-- 文档结构 -->
        <div class="structure-section">
          <h3>三、文档结构</h3>
          <!-- 编辑模式使用自定义编辑界面 -->
          <template v-if="isEditing">
            <div class="structure-controls">
              <el-button size="small" type="primary" @click="addStructureItem">
                <el-icon>
                  <Plus />
                </el-icon> 添加章节
              </el-button>
            </div>
            <div class="structure-items">
              <div v-for="(item, index) in documentStructure" :key="index" class="structure-item">
                <div class="structure-header">
                  <el-input v-model="item.label" size="small" placeholder="章节名称" @change="hasUnsavedChanges = true" />
                  <div class="structure-actions">
                    <el-button type="danger" size="small" circle @click="removeStructureItem(index)">
                      <el-icon>
                        <Delete />
                      </el-icon>
                    </el-button>
                    <el-button type="primary" size="small" circle @click="addStructureChild(index)">
                      <el-icon>
                        <Plus />
                      </el-icon>
                    </el-button>
                  </div>
                </div>
                <div class="structure-children" v-if="item.children && item.children.length > 0">
                  <div v-for="(child, childIndex) in item.children" :key="`${index}-${childIndex}`"
                    class="structure-child">
                    <el-input v-model="child.label" size="small" placeholder="子章节内容"
                      @change="hasUnsavedChanges = true" />
                    <el-button type="danger" size="small" circle @click="removeStructureChild(index, childIndex)">
                      <el-icon>
                        <Delete />
                      </el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- 非编辑模式使用树形控件展示 -->
          <template v-else>
            <el-tree :data="documentStructure" :props="defaultProps" :default-expand-all="false"></el-tree>
          </template>
        </div>



        <!-- 文档智能标签 -->
        <div class="tags-section">
          <h3>四、文档智能标签</h3>
          <div class="smart-tags">
            <el-tag v-for="(tag, index) in smartTags" :key="index" :closable="isEditing" @close="removeSmartTag(index)">
              {{ tag }}
            </el-tag>
            <el-button v-if="isEditing" class="button-new-tag" size="small" @click="showSmartTagInput">
              <el-icon>
                <Plus />
              </el-icon>
            </el-button>
          </div>
          <el-input v-if="smartTagInputVisible" v-model="smartTagInputValue" ref="smartTagInputRef" size="small"
            @keyup.enter="handleSmartTagInputConfirm" @blur="handleSmartTagInputConfirm" />
        </div>
      </div>

      <div class="pdf-right-panel">
        <!-- 知识图谱可视化 -->
        <div class="knowledge-graph">
          <h3>知识图谱</h3>
          <div ref="chartRef" style="width: 100%; height: 400px;"></div>
        </div>

        <!-- 修改文档权限 -->
        <div class="permissions-section">
          <h3>修改文档权限</h3>
          <el-radio-group v-model="permissions" :disabled="!isEditing" @change="hasUnsavedChanges = true">
            <el-radio label="REPO">部门私有</el-radio>
            <el-radio label="ALL">单位共享</el-radio>
          </el-radio-group>
        </div>

        <!-- 分页 -->
        <!-- <div class="pagination">
          <el-pagination v-model:current-page="currentPage" :total="total" :page-size="10"
            layout="prev, pager, next"></el-pagination>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { getFileDetail, editFile, graphGet, docFormat } from '@/api/file/file'
import { ref, onMounted, nextTick, computed, watch } from 'vue'
import VuePdfEmbed from 'vue-pdf-embed'
import * as echarts from 'echarts'
import { Plus, ArrowLeft, Delete } from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import useUserStore from '@/store/modules/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const showConfirmDialog = ref(false)
const isEditing = ref(false)
const fileId = ref('') // 文件ID
const loading = ref(false) // 加载状态
const parsing = ref(false) // 解析状态
const originalFileData = ref(null) // 原始文件数据，用于保存时传回所有字段
const originalRouteParams = ref({}) // 保存原始路由参数，用于验证
const isNavigatingAway = ref(false) // 标记是否正在主动导航离开页面

// 验证路由参数是否被篡改
const validateRouteParams = () => {
  const currentParams = route.query;
  // 检查关键参数是否与原始参数一致
  if (originalRouteParams.value.fileId !== currentParams.fileId ||
      originalRouteParams.value.remark !== currentParams.remark) {
    console.error('检测到路由参数被篡改', {
      原始参数: originalRouteParams.value,
      当前参数: currentParams
    });
    ElMessage.error('检测到非法参数修改');
    router.push('/401');
    return false;
  }
  return true;
}

// 判断当前用户是否为管理员或文档上传者
const isAdminOrUploader = computed(() => {
  // 按优先级判断用户权限

  // 1. 首先判断是否为最高级管理员（URL参数中传递）
  const isHighLevelAdmin = route.query.isHighLevelAdmin === '1';
  if (isHighLevelAdmin) {
    console.log('权限检查: 用户是最高级管理员，直接允许操作');
    return true;
  }

  // 2. 如果不是最高级管理员，判断是否为当前知识库管理员
  const isDatabaseAdmin = route.query.isAdmin === '1';
  if (isDatabaseAdmin) {
    console.log('权限检查: 用户是当前知识库管理员，允许操作');
    return true;
  }

  // 3. 如果既不是最高级管理员也不是知识库管理员，判断是否为文档发布者
  const isUploader = originalFileData.value && originalFileData.value.userId === userStore.id;
  if (isUploader) {
    console.log('权限检查: 用户是文档发布者，允许操作');
    return true;
  }

  // 全部不满足，则无权限操作
  console.log('权限检查: 用户无权限操作解析按钮', {
    当前用户ID: userStore.id,
    文件上传者ID: originalFileData.value?.userId,
    是否最高级管理员: isHighLevelAdmin,
    是否数据库管理员: isDatabaseAdmin,
    是否上传者: isUploader
  });

  return false;
});

// 监听原始数据变化，重新评估权限
watch(originalFileData, (newValue) => {
  if (newValue) {
    // 获取权限状态
    const isHighLevelAdmin = route.query.isHighLevelAdmin === '1';
    const isDatabaseAdmin = route.query.isAdmin === '1';
    const isUploader = newValue.userId === userStore.id;

    // 按优先级顺序检查权限
    let permissionResult = false;
    let permissionSource = '无权限';

    if (isHighLevelAdmin) {
      permissionResult = true;
      permissionSource = '最高级管理员';
    } else if (isDatabaseAdmin) {
      permissionResult = true;
      permissionSource = '知识库管理员';
    } else if (isUploader) {
      permissionResult = true;
      permissionSource = '文档发布者';
    }

    console.log('文件原始数据已更新，重新评估权限:', {
      文件ID: fileId.value,
      文件名称: newValue.fileName,
      当前用户ID: userStore.id,
      文件上传者ID: newValue.userId,
      权限类型: permissionSource,
      权限结果: permissionResult
    });
  }
}, { deep: true });

// 监听路由参数变化
watch(() => route.query, (newQuery) => {
  // 只有在组件已经初始化且保存了原始参数后才进行验证
  if (Object.keys(originalRouteParams.value).length > 0) {
    // 如果正在主动导航离开页面，则不进行参数验证
    if (isNavigatingAway.value) {
      console.log('正在主动导航离开页面，跳过参数验证');
      return;
    }

    // 检查是否是完全离开当前页面（路由路径发生变化）
    if (route.path !== '/pdf-preview') {
      console.log('已离开PDF预览页面，跳过参数验证');
      return;
    }

    // 检查关键参数是否被修改
    if (originalRouteParams.value.fileId !== newQuery.fileId ||
        originalRouteParams.value.remark !== newQuery.remark) {
      console.error('检测到路由参数被实时修改', {
        原始参数: originalRouteParams.value,
        当前参数: newQuery,
        当前路径: route.path
      });
      ElMessage.error('检测到非法参数修改');
      router.push('/401');
    }
  }
}, { deep: true });

// 监听路由路径变化，当离开当前页面时重置导航标记
watch(() => route.path, (newPath) => {
  if (newPath !== '/pdf-preview') {
    // 已经离开PDF预览页面，重置标记
    isNavigatingAway.value = false
    console.log('已离开PDF预览页面，重置导航标记')
  }
});

// PDF相关数据
const pdfName = ref('企业知识图谱应用白皮书.pdf')
const uploadTime = ref('2023-09-25 14:30')
const fileSize = ref('2.3 MB')
const status = ref('未解析')
const pdfSource = ref('path/to/your/pdf/file.pdf')

// 文档摘要
const documentSummary = ref('本白皮书详细阐述了企业知识图谱的构建方法、应用场景和实施路径，重点介绍了在企业知识管理、智能客服和决策支持系统中的实际应用案例及效果分析。')

// 关键词管理
const keywords = ref(['知识图谱', '企业知识管理', '设立预算', '智能决策', '自然语言'])
const keywordInputVisible = ref(false)
const keywordInputValue = ref('')
const keywordInputRef = ref(null)

// 智能标签管理
const smartTags = ref(['知识图谱', '图谱构建'])
const smartTagInputVisible = ref(false)
const smartTagInputValue = ref('')
const smartTagInputRef = ref(null)

// 文档结构
const documentStructure = ref([
  {
    label: '1. 引言',
    children: [
      { label: '1.1 知识图谱概述' },
      { label: '1.2 企业应用背景' }
    ]
  },
  {
    label: '2. 知识图谱构建方法',
    children: [
      { label: '2.1 数据采集与预处理' },
      { label: '2.2 实体识别与关系抽取' }
    ]
  },
  {
    label: '3. 企业应用场景',
    children: [
      { label: '3.1 知识管理系统' },
      { label: '3.2 智能客服系统' }
    ]
  }
])



// 其他数据
const permissions = ref('REPO')
const currentPage = ref(1)
const total = ref(20)
const defaultProps = {
  children: 'children',
  label: 'label'
}

// ECharts图表实例
const chartRef = ref(null)
let chart = null

// 知识图谱数据
const graphData = {
  nodes: [
    { name: '精神文明建设', symbolSize: 60, category: 0 },
    { name: '产品技术说明', symbolSize: 40, category: 1 },
    { name: '实践活动', symbolSize: 40, category: 2 },
    { name: '实践所站', symbolSize: 40, category: 3 },
    { name: '活动开展', symbolSize: 40, category: 4 }
  ],
  links: [
    { source: '精神文明建设', target: '产品技术说明' },
    { source: '精神文明建设', target: '实践活动' },
    { source: '精神文明建设', target: '实践所站' },
    { source: '精神文明建设', target: '活动开展' }
  ]
}

// 在onMounted中获取路由参数并加载文件数据
onMounted(async () => {
  // 参数保护机制：验证必要的路由参数
  if (!route.query.fileId || !route.query.remark) {
    console.error('路由参数缺失或无效', route.query);
    ElMessage.error('无效的访问参数');
    router.push('/401');
    return;
  }

  // 保存原始路由参数，用于后续验证
  originalRouteParams.value = { 
    fileId: route.query.fileId,
    remark: route.query.remark,
    isAdmin: route.query.isAdmin,
    isHighLevelAdmin: route.query.isHighLevelAdmin,
    isEdit: route.query.isEdit
  };

  // 获取路由参数
  fileId.value = route.query.fileId
  // 如果有isEdit参数且值为'1'，则进入编辑模式
  if (route.query.isEdit == '1') {
    isEditing.value = true
  }

  // 设置默认为暂无数据
  setEmptyData()

  // 调用知识图谱接口
  try {
    const graphRes = await graphGet()
    console.log('知识图谱接口返回数据:', graphRes)
    if (graphRes.code === 0 && graphRes.data) {
      // 保存图谱数据用于图表初始化
      graphData.nodes = processGraphData(graphRes.data)
      console.log('处理后的图谱数据:', graphData)
    }
  } catch (error) {
    console.error('调用知识图谱接口失败:', error)
  }

  // 如果有fileId，则加载文件数据
  if (fileId.value) {
    await loadFileData()
  }
  // 如果是开发环境且没有fileId，可以选择加载示例数据（取消注释下行）
  // else if (import.meta.env.DEV) {
  //   loadSampleData()
  // }

  // 初始化图表
  setTimeout(() => {
    initChart()
  }, 500)

  // 监听窗口大小变化，重新调整图表大小
  window.addEventListener('resize', () => {
    if (chart) {
      chart.resize()
    }
  })
})

// 处理图谱数据
const processGraphData = (data) => {
  if (!Array.isArray(data) || data.length === 0) {
    return graphData.nodes; // 返回默认数据
  }

  // 查找中心节点（假设是所有节点的父节点）
  let centerNode = null;
  if (data.length > 0) {
    centerNode = data[0].prentName;
  }

  // 创建节点数组
  const nodes = [];

  // 添加中心节点
  if (centerNode) {
    nodes.push({
      name: centerNode,
      symbolSize: 70,
      category: 0,
      itemStyle: {
        color: '#409EFF'
      },
      label: {
        fontSize: 14
      }
    });
  }

  // 添加其他节点
  data.forEach((item, index) => {
    nodes.push({
      name: item.name,
      symbolSize: Math.max(30, Math.min(60, item.value * 5)), // 根据value调整大小
      category: index + 1,
      value: item.value
    });
  });

  // 更新连接关系
  graphData.links = [];
  data.forEach(item => {
    graphData.links.push({
      source: item.prentName,
      target: item.name
    });
  });

  return nodes;
};

// 初始化图表
const initChart = () => {
  nextTick(() => {
    // 确保DOM元素存在
    if (!chartRef.value) {
      console.error('图表DOM元素不存在');
      return;
    }

    // 如果已经初始化过，先销毁
    if (chart) {
      chart.dispose();
    }

    // 初始化图表
    chart = echarts.init(chartRef.value);
    console.log('图表初始化完成');

    // 创建分类
    const categories = [{ name: '中心' }];
    graphData.nodes.forEach((node, index) => {
      if (index > 0) { // 跳过中心节点
        categories.push({ name: node.name });
      }
    });

    const option = {
      title: {
        text: '知识图谱关系',
        top: 'bottom',
        left: 'center',
        textStyle: {
          fontSize: 14,
          color: '#666'
        }
      },
      tooltip: {
        formatter: function (params) {
          if (params.dataType === 'node') {
            return `${params.name}${params.value ? '<br/>文档数量: ' + params.value : ''}`;
          }
          return params.name;
        }
      },
      legend: {
        show: false
      },
      animationDurationUpdate: 1500,
      animationEasingUpdate: 'quinticInOut',
      series: [
        {
          type: 'graph',
          layout: 'force',
          force: {
            repulsion: 200,
            edgeLength: 120
          },
          roam: true,
          label: {
            show: true,
            position: 'right',
            fontSize: 12,
            formatter: function (params) {
              return params.name + (params.data.value ? ' (' + params.data.value + ')' : '');
            }
          },
          edgeSymbol: ['circle', 'arrow'],
          edgeSymbolSize: [4, 10],
          edgeLabel: {
            fontSize: 12
          },
          data: graphData.nodes,
          links: graphData.links,
          categories: categories,
          lineStyle: {
            opacity: 0.9,
            width: 2,
            curveness: 0.2
          },
          emphasis: {
            focus: 'adjacency',
            lineStyle: {
              width: 4
            }
          }
        }
      ]
    }
    chart.setOption(option)
  })
}

// 设置所有数据为暂无数据
const setEmptyData = () => {
  // 更新文档摘要
  documentSummary.value = '暂无数据'

  // 更新关键词
  keywords.value = ['暂无数据']

  // 更新文档结构
  documentStructure.value = [{ label: '暂无数据', children: [] }]

  // 更新文档智能标签
  smartTags.value = ['暂无数据']
}

// 加载示例数据（测试用）
const loadSampleData = () => {
  const sampleData = {
    "文档摘要": "本文档介绍了一个基于 MinIO 的安全文件访问控制方案，重点讨论了如何通过后端生成签名 URL 来实现用户权限控制。",
    "关键词": [
      "MinIO",
      "签名URL",
      "权限控制",
      "Java",
      "文件访问",
      "安全策略"
    ],
    "文档结构": [
      {
        "标题": "引言",
        "内容": "介绍文档背景和目的。"
      },
      {
        "标题": "系统架构设计",
        "内容": "描述整体流程和技术选型。"
      },
      {
        "标题": "后端接口实现",
        "内容": "详细说明 Java 接口的实现方式。"
      },
      {
        "标题": "安全性分析与建议",
        "内容": "分析签名 URL 的风险并提出改进措施。"
      }
    ],
    "文档智能标签": [
      "安全",
      "权限管理",
      "Spring Boot",
      "对象存储",
      "身份验证",
      "后端开发"
    ]
  }

  // 更新文档摘要
  documentSummary.value = sampleData['文档摘要']

  // 更新关键词
  keywords.value = sampleData['关键词']

  // 更新文档结构
  documentStructure.value = sampleData['文档结构'].map((item, index) => {
    return {
      label: `${index + 1}. ${item['标题']}`,
      children: item['内容'] ? [{ label: item['内容'] }] : []
    }
  })



  // 更新文档智能标签
  smartTags.value = sampleData['文档智能标签']

  // 更新基本信息
  pdfName.value = "MinIO安全文件访问控制方案.pdf"
  uploadTime.value = "2025-06-08 10:00"
  fileSize.value = "1.8 MB"
  status.value = "已解析"
}



// 加载文件数据的方法
const loadFileData = async () => {
  // 验证路由参数是否被篡改
  if (!validateRouteParams()) {
    return;
  }
  
  loading.value = true
  try {
    // 获取传递过来的remark参数
    const remark = route.query.remark
    console.log('获取到的remark参数:', remark)

    // 调用API获取文件详情，传递remark参数
    const res = await getFileDetail(fileId.value, remark)
    console.log('API返回数据:', res)
    console.log('res.data类型:', typeof res.data)

    if (res.code == 0 && res.data) {
      // 直接使用返回的文件数据对象
      const fileData = res.data;
      console.log('文件数据:', fileData);

      // 保存原始文件数据，用于编辑时提交
      originalFileData.value = { ...fileData }

      // 立即检查并记录文件上传者权限
      const isHighLevelAdmin = route.query.isHighLevelAdmin === '1';
      const isDatabaseAdmin = route.query.isAdmin === '1';
      const isUploader = fileData.userId === userStore.id;

      // 按优先级顺序检查权限
      let permissionResult = false;
      let permissionSource = '无权限';

      if (isHighLevelAdmin) {
        permissionResult = true;
        permissionSource = '最高级管理员';
      } else if (isDatabaseAdmin) {
        permissionResult = true;
        permissionSource = '知识库管理员';
      } else if (isUploader) {
        permissionResult = true;
        permissionSource = '文档发布者';
      }

      console.log('文件数据已加载，检查权限:', {
        当前用户ID: userStore.id,
        文件上传者ID: fileData.userId,
        权限类型: permissionSource,
        权限结果: permissionResult
      });

      // 更新文件基本信息
      pdfName.value = fileData.fileName || '未命名文件'
      uploadTime.value = fileData.createTime || '-'
      fileSize.value = formatFileSize(fileData.fileSize || 0)
      
      // 根据structuredStatus设置状态显示
      switch(fileData.structuredStatus) {
        case 'UNPARSED':
          status.value = '未解析'
          break
        case 'PARSING':
          status.value = '解析中'
          break
        case 'PARSE_SUCCESS':
          status.value = '解析成功'
          break
        case 'PARSE_FAILED':
          status.value = '解析失败'
          break
        default:
          status.value = '未解析'
      }

      // 设置PDF源
      if (fileData.filePath) {
        pdfSource.value = fileData.filePath
      }

      // 直接从详情接口获取字段值，不再处理formatData
      console.log('开始处理文件详情数据:', fileData);

      // 一、文档摘要是详情接口拿到的res.data.documentSummary
      if (fileData.documentSummary) {
        documentSummary.value = fileData.documentSummary;
        console.log('设置文档摘要:', documentSummary.value);
      } else {
        documentSummary.value = '暂无数据';
        console.log('文档摘要为空，设置为暂无数据');
      }

      // 二、关键词是详情接口拿到的res.data.keywords
      if (fileData.keywords) {
        // 如果keywords是字符串，按逗号或空格分割
        if (typeof fileData.keywords === 'string') {
          keywords.value = fileData.keywords.split(/[,，\s]+/).filter(k => k.trim() !== '');
        } else if (Array.isArray(fileData.keywords)) {
          keywords.value = fileData.keywords.filter(k => k && k.trim() !== '');
        } else {
          keywords.value = ['暂无数据'];
        }
        console.log('设置关键词:', keywords.value);
      } else {
        keywords.value = ['暂无数据'];
        console.log('关键词为空，设置为暂无数据');
      }

      // 三、文档结构是详情接口拿到的res.data.documentStructure
      console.log('开始处理文档结构');
      if (fileData.documentStructure) {
        try {
          let structureText = fileData.documentStructure;

          // 如果documentStructure是对象或数组，先转换为字符串
          if (typeof structureText !== 'string') {
            structureText = JSON.stringify(structureText);
          }

          console.log('文档结构原始数据:', structureText);

          // 解析特定格式：一、综合科: [内容], 二、创建科: [内容] 等
          const structureArray = [];

          // 使用正则表达式匹配 "一、xxx: [内容]" 格式
          const sectionRegex = /([一二三四五六七八九十]+)、([^:：]+)[:：]\s*\[([^\]]+)\]/g;
          let match;

          while ((match = sectionRegex.exec(structureText)) !== null) {
            const [, number, title, content] = match;
            console.log('匹配到章节:', { number, title, content });

            // 处理内容，按逗号分割
            const contentItems = content.split(',').map(item => item.trim()).filter(item => item);

            const sectionItem = {
              label: `${number}、${title}`,
              children: contentItems.map(item => ({ label: item }))
            };

            structureArray.push(sectionItem);
          }

          // 如果没有匹配到特定格式，尝试其他处理方式
          if (structureArray.length === 0) {
            console.log('未匹配到特定格式，尝试其他解析方式');

            // 尝试JSON解析
            try {
              const jsonData = JSON.parse(structureText);
              if (Array.isArray(jsonData)) {
                structureArray.push(...jsonData.map((item, index) => ({
                  label: item.label || item.title || item['标题'] || `章节${index + 1}`,
                  children: item.children || (item.content || item['内容'] ? [{ label: item.content || item['内容'] }] : [])
                })));
              } else if (typeof jsonData === 'object') {
                for (const key in jsonData) {
                  const value = jsonData[key];
                  structureArray.push({
                    label: key,
                    children: Array.isArray(value) ? value.map(v => ({ label: String(v) })) : [{ label: String(value) }]
                  });
                }
              }
            } catch (e) {
              console.log('JSON解析失败，作为普通文本处理');
              structureArray.push({ label: structureText, children: [] });
            }
          }

          documentStructure.value = structureArray.length > 0 ? structureArray : [{ label: '暂无数据', children: [] }];

        } catch (error) {
          console.error('处理文档结构时出错:', error);
          documentStructure.value = [{ label: '暂无数据', children: [] }];
        }
      } else {
        console.log('没有文档结构数据，设置为暂无数据');
        documentStructure.value = [{ label: '暂无数据', children: [] }];
      }
      console.log('最终设置的文档结构:', documentStructure.value);



      // 四、文档智能标签是详情接口拿到的res.data.documentTags
      if (fileData.documentTags) {
        // 如果documentTags是字符串，按逗号或空格分割
        if (typeof fileData.documentTags === 'string') {
          smartTags.value = fileData.documentTags.split(/[,，\s]+/).filter(t => t.trim() !== '');
        } else if (Array.isArray(fileData.documentTags)) {
          smartTags.value = fileData.documentTags.filter(t => t && t.trim() !== '');
        } else {
          smartTags.value = ['暂无数据'];
        }
        console.log('设置文档智能标签:', smartTags.value);
      } else {
        smartTags.value = ['暂无数据'];
        console.log('文档智能标签为空，设置为暂无数据');
      }

      // 更新权限设置
      if (fileData.permissionType) {
        permissions.value = fileData.permissionType;
        console.log('设置权限:', permissions.value);
      }
    } else {
      // 显示错误提示
      ElMessage.error(res.msg || '获取文件数据失败')
      setEmptyData()
    }
  } catch (error) {
    console.error('加载文件数据失败:', error)
    ElMessage.error('加载文件数据失败，请重试')
    setEmptyData()
  } finally {
    loading.value = false
  }
}





// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 关键词相关方法
const showKeywordInput = () => {
  if (!isEditing.value) return
  keywordInputVisible.value = true
  nextTick(() => {
    keywordInputRef.value.focus()
  })
}

const handleKeywordInputConfirm = () => {
  if (keywordInputValue.value) {
    // 如果当前只有一个"暂无数据"的占位符，则先清空数组
    if (keywords.value.length === 1 && keywords.value[0] === '暂无数据') {
      keywords.value = []
    }
    keywords.value.push(keywordInputValue.value)
    hasUnsavedChanges.value = true
  }
  keywordInputVisible.value = false
  keywordInputValue.value = ''
}

const removeKeyword = (index) => {
  if (!isEditing.value) return
  keywords.value.splice(index, 1)
  // 如果删除后没有关键词，则添加"暂无数据"占位符
  if (keywords.value.length === 0) {
    keywords.value = ['暂无数据']
  }
  hasUnsavedChanges.value = true
}

// 智能标签相关方法
const showSmartTagInput = () => {
  if (!isEditing.value) return
  smartTagInputVisible.value = true
  nextTick(() => {
    smartTagInputRef.value.focus()
  })
}

const handleSmartTagInputConfirm = () => {
  if (smartTagInputValue.value) {
    // 如果当前只有一个"暂无数据"的占位符，则先清空数组
    if (smartTags.value.length === 1 && smartTags.value[0] === '暂无数据') {
      smartTags.value = []
    }
    smartTags.value.push(smartTagInputValue.value)
    hasUnsavedChanges.value = true
  }
  smartTagInputVisible.value = false
  smartTagInputValue.value = ''
}

const removeSmartTag = (index) => {
  if (!isEditing.value) return
  smartTags.value.splice(index, 1)
  // 如果删除后没有智能标签，则添加"暂无数据"占位符
  if (smartTags.value.length === 0) {
    smartTags.value = ['暂无数据']
  }
  hasUnsavedChanges.value = true
}

// 开始编辑
const startEditing = () => {
  isEditing.value = true
}

// 保存设置
const saveSettings = async () => {
  // 验证路由参数是否被篡改
  if (!validateRouteParams()) {
    return;
  }

  try {
    // 处理关键词数据（过滤掉"暂无数据"）
    const filteredKeywords = keywords.value.filter(keyword => keyword !== '暂无数据');

    // 处理文档结构数据（过滤掉"暂无数据"）
    let filteredStructure = documentStructure.value
      .filter(item => item.label !== '暂无数据')
      .map(item => {
        const children = item.children ? item.children.filter(child => child.label !== '暂无数据') : [];
        return {
          label: item.label,
          children: children
        };
      });

    // 将结构转换为特定格式的字符串
    let structureString = '';
    filteredStructure.forEach((item, index) => {
      if (index > 0) structureString += ',    ';

      const childrenText = item.children.map(child => child.label).join(', ');
      structureString += `${item.label}: [${childrenText}]`;
    });

    console.log('转换后的文档结构字符串:', structureString);

    // 处理智能标签数据（过滤掉"暂无数据"）
    const filteredSmartTags = smartTags.value.filter(tag => tag !== '暂无数据');

    // 调用编辑接口，直接保存到新的字段中
    const params = {
      fileId: fileId.value,
      fileMd5: originalFileData.value?.fileMd5 || '',
      fileName: pdfName.value,
      filePath: pdfSource.value,
      fileSize: originalFileData.value?.fileSize || 0,
      fileType: originalFileData.value?.fileType || '',
      permissionType: permissions.value,
      repoId: originalFileData.value?.repoId || 0,
      status: originalFileData.value?.status || '',
      userId: originalFileData.value?.userId || 0,
      // 新的字段结构
      documentSummary: documentSummary.value === '暂无数据' ? '' : documentSummary.value,
      keywords: filteredKeywords.length > 0 ? filteredKeywords.join(',') : '',
      documentStructure: structureString || '',
      documentTags: filteredSmartTags.length > 0 ? filteredSmartTags.join(',') : ''
    };

    console.log('保存参数:', params);

    const res = await editFile(params);

    if (res.code === 0) {
      ElMessage.success('保存成功');
      isEditing.value = false;
      hasUnsavedChanges.value = false;
    } else {
      ElMessage.error(res.msg || '保存失败');
    }
  } catch (error) {
    console.error('保存设置失败:', error);
    ElMessage.error('保存设置失败，请重试');
  }
}

// 取消编辑
const cancelEditing = () => {
  if (hasUnsavedChanges.value) {
    showConfirmDialog.value = true
  } else {
    isEditing.value = false
  }
}

// 确认离开编辑状态
const confirmLeave = () => {
  showConfirmDialog.value = false
  isEditing.value = false
  hasUnsavedChanges.value = false
  // 设置导航标记，避免触发参数验证
  isNavigatingAway.value = true
  router.back()
}

// 检查是否有未保存的更改
const hasUnsavedChanges = ref(false)

// 返回上一页
const handleBack = () => {
  if (isEditing.value && hasUnsavedChanges.value) {
    // 如果有未保存的修改，显示确认对话框
    showConfirmDialog.value = true
  } else {
    // 否则直接返回，设置导航标记避免触发参数验证
    isNavigatingAway.value = true
    router.back()
  }
}

// 添加结构项
const addStructureItem = () => {
  documentStructure.value.push({ label: '', children: [] })
  hasUnsavedChanges.value = true
}

// 移除结构项
const removeStructureItem = (index) => {
  documentStructure.value.splice(index, 1)
  hasUnsavedChanges.value = true
}

// 添加子结构项
const addStructureChild = (index) => {
  documentStructure.value[index].children.push({ label: '' })
  hasUnsavedChanges.value = true
}

// 移除子结构项
const removeStructureChild = (index, childIndex) => {
  documentStructure.value[index].children.splice(childIndex, 1)
  hasUnsavedChanges.value = true
}



// 解析文档结构
const parseDocumentFormat = async () => {
  // 验证路由参数是否被篡改
  if (!validateRouteParams()) {
    return;
  }

  if (!fileId.value) {
    ElMessage.error('文件ID不存在，无法解析文档');
    return;
  }

  // 添加确认对话框
  ElMessageBox.confirm('是否确认解析该文档结构?', '操作确认', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    // 用户确认后执行解析
    parsing.value = true;
    try {
      let params = {
        datasetId: route.query.remark,
        docId: fileId.value,
        fileName: pdfName.value // 使用当前文件详情的fileName字段
      }
      const res = await docFormat(params);
      if (res.code == 0) {
        ElMessage.success('文档解析请求已提交');

        // 告知用户解析进行中
        ElMessage({
          type: 'info',
          message: '文档解析需要一些时间，系统正在处理中...',
          duration: 5000
        });

        // 创建轮询机制检查文档解析状态
        let checkCount = 0;
        const maxChecks = 10; // 最多检查10次
        const checkInterval = 3000; // 每3秒检查一次
        
        const checkDocumentStatus = async () => {
          if (checkCount >= maxChecks) {
            ElMessage.warning('文档解析时间较长，请稍后刷新页面查看结果');
            parsing.value = false;
            return;
          }
          
          checkCount++;
          console.log(`第${checkCount}次检查文档解析状态...`);
          
          try {
            // 重新加载文件数据
            await loadFileData();
            
            // 检查文档状态
            if (status.value == '解析成功') {
              ElMessage.success('文档解析完成');
              parsing.value = false;
              return;
            } else if (status.value == '解析失败') {
              ElMessage.error('文档解析失败');
              parsing.value = false;
              return;
            } else {
              // 如果还没解析完成，继续轮询
              setTimeout(checkDocumentStatus, checkInterval);
            }
          } catch (error) {
            console.error('检查文档状态失败:', error);
            setTimeout(checkDocumentStatus, checkInterval);
          }
        };
        
        // 开始轮询检查
        setTimeout(checkDocumentStatus, checkInterval);
      } else {
        ElMessage.error(res.msg || '解析文档失败');
        parsing.value = false;
      }
    } catch (error) {
      console.error('解析文档失败:', error);
      ElMessage.error('解析文档失败，请重试');
      parsing.value = false;
    }
  }).catch(() => {
    // 用户取消操作
    ElMessage.info('已取消解析');
  });
};
</script>

<style scoped>
.pdf-preview-container {
  padding: 24px;
  margin-top: 20px;
  background: #f8fafc;
  min-height: 100vh;
}

.pdf-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: white;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.pdf-header:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.pdf-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  font-size: 15px;
  color: #606266;
  transition: all 0.3s ease;
  padding: 8px 12px;
  border-radius: 8px;
}

.back-button:hover {
  color: #409EFF;
  transform: translateX(-4px);
  background-color: #ecf5ff;
}

.pdf-icon {
  width: 36px;
  height: 36px;
  transition: transform 0.3s ease;
}

.pdf-icon:hover {
  transform: scale(1.1);
}

.pdf-name {
  font-weight: 600;
  font-size: 1.2em;
  color: #1a1a1a;
}

.pdf-meta {
  color: #64748b;
  font-size: 0.95em;
}

.pdf-content {
  display: flex;
  gap: 28px;
  flex-wrap: nowrap;
}

.pdf-left-panel {
  flex: 2;
  background: white;
  padding: 28px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  max-width: 65%;
}

.pdf-right-panel {
  flex: 1;
  background: white;
  padding: 28px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  display: block !important;
  min-width: 30%;
}

.pdf-left-panel:hover,
.pdf-right-panel:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.pdf-viewer {
  margin-bottom: 28px;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  background: #fafafa;
  overflow: hidden;
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.05);
}

.pdf-viewer :deep(embed) {
  max-width: 100%;
  border-radius: 8px;
}

.keyword-tags,
.smart-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 16px;
}

.el-tag {
  max-width: 100%;
  white-space: normal;
  height: auto;
  padding: 6px 12px;
  line-height: 1.5;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.el-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.button-new-tag {
  height: 32px;
  padding: 0 12px;
  transition: all 0.3s ease;
  border-radius: 6px;
}

.button-new-tag:hover {
  transform: scale(1.08);
  background-color: #ecf5ff;
}

.structure-controls {
  margin-bottom: 20px;
}

.structure-item {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: #fbfcfe;
  word-wrap: break-word;
  overflow-wrap: break-word;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

.structure-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.structure-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.structure-actions {
  display: flex;
  gap: 10px;
}

.structure-children {
  margin-left: 28px;
  padding-left: 16px;
  border-left: 3px solid #e2e8f0;
}

.structure-child {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
  padding: 10px;
  background: #fefefe;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.structure-child:hover {
  background: #f0f7ff;
  transform: translateX(4px);
}



.permissions-section {
  margin: 28px 0;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  display: block;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

.permissions-section:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 28px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

h3 {
  margin: 28px 0 20px;
  font-weight: 600;
  color: #0f172a;
  font-size: 1.25em;
  position: relative;
  padding-left: 16px;
  transition: all 0.3s ease;
}

h3::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 5px;
  height: 20px;
  background: #409eff;
  border-radius: 3px;
  transition: all 0.3s ease;
}

h3:hover::before {
  height: 24px;
  background: #66b1ff;
}

/* 确保所有文本元素都能正确换行 */
p,
span,
.el-tree-node__label,
.el-tag {
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

.knowledge-graph {
  width: 100%;
  height: 450px;
  margin-bottom: 30px;
  border-radius: 12px;
  overflow: hidden;
  background: #fafafa;
  padding: 20px;
  display: block;
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.knowledge-graph:hover {
  box-shadow: inset 0 2px 12px rgba(0, 0, 0, 0.08);
}

:deep(.el-tag) {
  transition: all 0.3s ease;
}

:deep(.el-tag:hover) {
  transform: translateY(-3px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

:deep(.el-tree-node__content) {
  padding: 10px;
  transition: all 0.3s ease;
  border-radius: 6px;
  margin: 2px 0;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f0f7ff;
  transform: translateX(4px);
}

:deep(.el-radio) {
  margin-right: 24px;
  transition: all 0.3s ease;
  padding: 6px 8px;
  border-radius: 6px;
}

:deep(.el-radio:hover) {
  transform: translateY(-2px);
  background-color: #f0f7ff;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

:deep(.el-dialog) {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin: 0 !important;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

:deep(.el-dialog__header) {
  margin-right: 0;
  padding: 24px 24px 12px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__body) {
  padding: 28px 24px;
  color: #606266;
  font-size: 15px;
}

:deep(.el-dialog__footer) {
  padding: 12px 24px 24px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-button) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.el-button:hover) {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.el-button--primary) {
  font-weight: 500;
}

:deep(.el-input__inner) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.el-input__inner:focus) {
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .pdf-content {
    flex-direction: column;
  }

  .pdf-left-panel,
  .pdf-right-panel {
    max-width: 100%;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .pdf-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .pdf-actions {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }
}
</style>