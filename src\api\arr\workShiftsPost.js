import request from '@/utils/request'

// 查询排班班次岗位组合列表
export function listWorkShiftsPost(query) {
  return request({
    url: '/arr/workShiftsPost/list',
    method: 'get',
    params: query
  })
}

// 查询排班班次岗位组合详细
export function getWorkShiftsPost(id) {
  return request({
    url: '/arr/workShiftsPost/' + id,
    method: 'get'
  })
}

// 新增排班班次岗位组合
export function addWorkShiftsPost(data) {
  return request({
    url: '/arr/workShiftsPost',
    method: 'post',
    data: data
  })
}

// 修改排班班次岗位组合
export function updateWorkShiftsPost(data) {
  return request({
    url: '/arr/workShiftsPost/edit',
    method: 'post',
    data: data
  })
}

// 删除排班班次岗位组合
export function delWorkShiftsPost(id) {
  return request({
    url: '/arr/workShiftsPost/del/' + id,
    method: 'post'
  })
}
// 停用/启用排班岗位管理
export function changeStatus(status,id) {
  return request({
    url: `/arr/workShiftsPost/changeStatus/${status}/${id}`,
    method: 'get'
  })
}
