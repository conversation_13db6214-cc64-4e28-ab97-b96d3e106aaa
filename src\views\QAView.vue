<template>
  <div>
    <div class="main-content d-flex">
      <!-- 左侧历史记录面板 -->
      <div class="history-sidebar" :class="{ 'collapsed': isSidebarCollapsed }">
        <div class="history-header">
          <div class="d-flex align-center justify-space-between w-100">
            <div class="d-flex align-center">
              <v-icon color="primary" class="mr-2">mdi-history</v-icon>
              <span class="font-weight-medium">历史记录</span>
            </div>
            <div class="d-flex">
              <v-btn variant="text" density="comfortable" icon size="small" color="error" @click="clearHistory"
                :disabled="!chatHistory.recent7Days?.length && !chatHistory.recent30Days?.length && !chatHistory.before30Days?.length">
                <v-icon>mdi-delete-sweep</v-icon>
              </v-btn>
            </div>
          </div>
        </div>

        <div class="new-chat-button-container">
          <v-btn block color="#4C6FFF" class="new-chat-button" @click="createNewSession"
            prepend-icon="mdi-message-plus">
            开启新会话
          </v-btn>
        </div>

        <div v-if="!chatHistory.recent7Days?.length && !chatHistory.recent30Days?.length && !chatHistory.before30Days?.length" class="text-center pa-4 text-body-1 text-medium-emphasis">
          暂无历史记录
        </div>

        <div v-else class="history-groups">
          <!-- 7天内的历史 -->
          <div class="history-group" v-if="chatHistory.recent7Days && chatHistory.recent7Days.length > 0">
            <div class="history-group-title" @click="toggleGroup('recent')">
              <div class="d-flex align-center flex-grow-1">
                <v-icon size="x-small" color="grey-darken-1" class="mr-2">mdi-calendar-range</v-icon>
                <span>近7天内</span>
                <span class="history-count">({{ chatHistory.recent7Days.length }})</span>
              </div>
              <v-icon size="small" color="grey-darken-1" class="collapse-icon"
                :class="{ 'collapsed': !groupExpanded.recent }">
                mdi-chevron-down
              </v-icon>
            </div>
            <div class="history-items" v-show="groupExpanded.recent">
              <div v-for="(history, index) in chatHistory.recent7Days" :key="index" @click="loadHistoryConversation(history)"
                class="history-item"
                :class="{ 'selected-history': selectedHistoryIndex == getHistoryFullIndex(history) }">
                <!-- 左侧内容区域，点击时触发加载历史记录 -->
                <div class="history-item-main" @click="loadHistoryConversation(history)">
                  <v-avatar size="28" :color="history.mode?.value == 'v2' ? 'green-lighten-5' : 'blue-lighten-5'"
                    class="history-avatar">
                    <v-icon size="small" :color="history.mode?.value == 'v2' ? 'green-darken-1' : 'blue-darken-1'">
                      {{ history.mode?.value == 'v2' ? 'mdi-chart-bar' : 'mdi-message-text-outline' }}
                    </v-icon>
                  </v-avatar>
                  <div class="history-text">
                    <div class="history-title">{{ getHistoryTitle(history) }}</div>
                    <div class="history-date">
                      <v-icon size="x-small" color="grey-darken-1">mdi-clock-outline</v-icon>
                      <span>{{ formatHistoryDate(history.lastMessageTime || history.createTime) }}</span>
                    </div>
                  </div>
                </div>
                <!-- 右侧三点菜单按钮 -->
                <el-dropdown size="large" trigger="click" @command="handleCommand">
                  <button class="history-menu-btn" @click.stop="openHistoryMenu($event, history)">
                    <v-icon size="small">mdi-dots-vertical</v-icon>
                  </button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{ type: 'rename', history }">重命名</el-dropdown-item>
                      <el-dropdown-item :command="{ type: 'delete', history }">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>

          <!-- 30天内的历史 -->
          <div class="history-group" v-if="chatHistory.recent30Days && chatHistory.recent30Days.length > 0">
            <div class="history-group-title" @click="toggleGroup('older')">
              <div class="d-flex align-center flex-grow-1">
                <v-icon size="x-small" color="grey-darken-1" class="mr-2">mdi-calendar-month</v-icon>
                <span>30天内</span>
                <span class="history-count">({{ chatHistory.recent30Days.length }})</span>
              </div>
              <v-icon size="small" color="grey-darken-1" class="collapse-icon"
                :class="{ 'collapsed': !groupExpanded.older }">
                mdi-chevron-down
              </v-icon>
            </div>
            <div class="history-items" v-show="groupExpanded.older">
              <div v-for="(history, index) in chatHistory.recent30Days" :key="index" @click="loadHistoryConversation(history)"
                class="history-item"
                :class="{ 'selected-history': selectedHistoryIndex == getHistoryFullIndex(history) }">
                <!-- 左侧内容区域，点击时触发加载历史记录 -->
                <div class="history-item-main" @click="loadHistoryConversation(history)">
                  <v-avatar size="28" :color="history.mode?.value === 'v2' ? 'green-lighten-5' : 'blue-lighten-5'"
                    class="history-avatar">
                    <v-icon size="small" :color="history.mode?.value === 'v2' ? 'green-darken-1' : 'blue-darken-1'">
                      {{ history.mode?.value === 'v2' ? 'mdi-chart-bar' : 'mdi-message-text-outline' }}
                    </v-icon>
                  </v-avatar>
                  <div class="history-text">
                    <div class="history-title">{{ getHistoryTitle(history) }}</div>
                    <div class="history-date">
                      <v-icon size="x-small" color="grey-darken-1">mdi-clock-outline</v-icon>
                      <span>{{ formatHistoryDate(history.lastMessageTime || history.createTime) }}</span>
                    </div>
                  </div>
                </div>
                <!-- 右侧三点菜单按钮 -->
                <el-dropdown size="large" trigger="click" @command="handleCommand">
                  <button class="history-menu-btn" @click.stop="openHistoryMenu($event, history)">
                    <v-icon size="small">mdi-dots-vertical</v-icon>
                  </button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{ type: 'rename', history }">重命名</el-dropdown-item>
                      <el-dropdown-item :command="{ type: 'delete', history }">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>

          <!-- 30天前的历史 -->
          <div class="history-group" v-if="chatHistory.before30Days && chatHistory.before30Days.length > 0">
            <div class="history-group-title" @click="toggleGroup('oldest')">
              <div class="d-flex align-center flex-grow-1">
                <v-icon size="x-small" color="grey-darken-1" class="mr-2">mdi-calendar-clock</v-icon>
                <span>30天前</span>
                <span class="history-count">({{ chatHistory.before30Days.length }})</span>
              </div>
              <v-icon size="small" color="grey-darken-1" class="collapse-icon"
                :class="{ 'collapsed': !groupExpanded.oldest }">
                mdi-chevron-down
              </v-icon>
            </div>
            <div class="history-items" v-show="groupExpanded.oldest">
              <div v-for="(history, index) in chatHistory.before30Days" :key="index" @click="loadHistoryConversation(history)"
                class="history-item"
                :class="{ 'selected-history': selectedHistoryIndex == getHistoryFullIndex(history) }">
                <!-- 左侧内容区域，点击时触发加载历史记录 -->
                <div class="history-item-main" @click="loadHistoryConversation(history)">
                  <v-avatar size="28" :color="history.mode?.value === 'v2' ? 'green-lighten-5' : 'blue-lighten-5'"
                    class="history-avatar">
                    <v-icon size="small" :color="history.mode?.value === 'v2' ? 'green-darken-1' : 'blue-darken-1'">
                      {{ history.mode?.value === 'v2' ? 'mdi-chart-bar' : 'mdi-message-text-outline' }}
                    </v-icon>
                  </v-avatar>
                  <div class="history-text">
                    <div class="history-title">{{ getHistoryTitle(history) }}</div>
                    <div class="history-date">
                      <v-icon size="x-small" color="grey-darken-1">mdi-clock-outline</v-icon>
                      <span>{{ formatHistoryDate(history.lastMessageTime || history.createTime) }}</span>
                    </div>
                  </div>
                </div>
                <!-- 右侧三点菜单按钮 -->
                <el-dropdown size="large" trigger="click" @command="handleCommand">
                  <button class="history-menu-btn" @click.stop="openHistoryMenu($event, history)">
                    <v-icon size="small">mdi-dots-vertical</v-icon>
                  </button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{ type: 'rename', history }">重命名</el-dropdown-item>
                      <el-dropdown-item :command="{ type: 'delete', history }">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
        </div>
      </div>

      <v-card class="chat-container" :class="{ 'v2-mode': selectedMode.value == 'v2', 'expanded': isSidebarCollapsed }">
        <v-card-title class="py-3 py-sm-4 px-4 px-sm-6 bg-primary text-white d-flex justify-space-between align-center">
          <div class="d-flex align-center">
            <v-btn variant="text" color="white" icon @click="toggleSidebar" class="mr-2">
              <v-icon>{{ isSidebarCollapsed ? 'mdi-menu' : 'mdi-menu-open' }}</v-icon>
            </v-btn>
            <v-icon left class="mr-2 d-none d-sm-inline">mdi-message-text</v-icon>
            智能问答
          </div>
          <div class="d-flex">
            <v-btn variant="text" color="white" class="ml-2" :icon="$vuetify.display.xs" @click="clearConversation">
              <v-icon>mdi-delete-sweep</v-icon>
              <span class="d-none d-sm-inline ml-1">清除对话</span>
            </v-btn>
          </div>
        </v-card-title>

        <div class="chat-messages" ref="chatContainer">
          <div v-for="(msg, index) in messages" :key="index" :class="['message', msg.type]">
            <div class="message-avatar" v-if="msg.type == 'system'">
              <v-avatar color="primary" :size="$vuetify.display.xs ? 32 : 40">
                <v-icon color="white" :size="$vuetify.display.xs ? 16 : 24">mdi-robot</v-icon>
              </v-avatar>
            </div>
            <div class="message-avatar" v-if="msg.type == 'user'">
              <v-avatar color="blue-darken-1" :size="$vuetify.display.xs ? 32 : 40">
                <v-icon color="white" :size="$vuetify.display.xs ? 16 : 24">mdi-account</v-icon>
              </v-avatar>
            </div>
            <div class="message-content" :class="{ 'table-container': msg.table }">
              <div v-if="msg.type == 'system' && msg.thinking" class="thinking-indicator">
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
              </div>
              <div v-else-if="msg.type == 'system' && msg.welcome" class="welcome-message">
                <div v-html="msg.content"></div>
                <div class="suggested-questions">
                  <v-chip v-for="(question, qIndex) in msg.suggestedQuestions" :key="qIndex" class="ma-1"
                    color="blue-lighten-5" variant="elevated" size="small" @click="askSuggestedQuestion(question)">
                    {{ question }}
                  </v-chip>
                </div>
              </div>
              <div v-else-if="msg.type == 'system' && msg.table" class="table-response">
                <div v-html="msg.content" class="mb-3"></div>
                <div class="table-responsive">
                  <v-card variant="flat" class="table-card">
                    <v-table v-if="msg.tableData" density="compact" class="rounded">
                      <thead class="table-header">
                        <tr>
                          <th v-for="(header, hIndex) in msg.tableHeaders" :key="hIndex" class="text-center">
                            {{ header }}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="(row, rIndex) in msg.tableData" :key="rIndex">
                          <td v-for="(cell, cIndex) in row" :key="cIndex" class="text-center">
                            {{ cell }}
                          </td>
                        </tr>
                      </tbody>
                    </v-table>
                  </v-card>
                </div>

                <!-- 额外内容 -->
                <div v-if="msg.additionalContent" class="mt-4" v-html="msg.additionalContent"></div>

                <!-- 额外表格 -->
                <div v-if="msg.additionalTableHeaders && msg.additionalTableData" class="table-responsive mt-3">
                  <v-card variant="flat" class="table-card">
                    <v-table density="compact" class="rounded">
                      <thead class="table-header">
                        <tr>
                          <th v-for="(header, hIndex) in msg.additionalTableHeaders" :key="hIndex" class="text-center">
                            {{ header }}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="(row, rIndex) in msg.additionalTableData" :key="rIndex">
                          <td v-for="(cell, cIndex) in row" :key="cIndex" class="text-center">
                            {{ cell }}
                          </td>
                        </tr>
                      </tbody>
                    </v-table>
                  </v-card>
                </div>
              </div>
              <div v-else class="formatted-content">
                <div v-html="msg.content"></div>
                <div v-if="msg.canceled" class="canceled-indicator">
                  <v-icon small color="grey">mdi-cancel</v-icon>
                  <span class="canceled-text">已停止</span>
                </div>
              </div>
              <!-- 如果有参考文献，显示在底部 -->
              <div v-if="msg.showReferences && msg.references && msg.references.length > 0 && !msg.thinking"
                class="references-container">
                <div class="references-title">
                  <v-icon size="small" color="grey" class="mr-1">mdi-book-open-variant</v-icon>
                  参考来源
                </div>
                <div class="references-list">
                  <div v-for="(reference, refIndex) in getUniqueReferences(msg.references)" :key="refIndex"
                    class="reference-item" @click="downloadDocument(reference.document_id)">
                    <v-icon size="x-small" color="grey" class="mr-1">mdi-file-document-outline</v-icon>
                    {{ reference.document_name || '未命名文档' }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="chat-input">
          <div class="chat-input-container">
            <div class="mode-selector">
              <v-select v-model="selectedMode" :items="modeOptions" variant="plain" density="compact" hide-details
                class="mode-select" :disabled="isProcessing" bg-color="transparent" return-object item-title="text"
                item-value="value" item-value-key="value">
                <template v-slot:selection="{ item }">
                  <div class="d-flex align-center mode-label">
                    <v-icon size="x-small" color="primary" class="mr-1">
                      {{ item.raw.icon }}
                    </v-icon>
                    {{ item.raw.text }}
                    <v-icon size="x-small" color="grey-darken-1" class="ml-1">mdi-chevron-down</v-icon>
                  </div>
                </template>
                <template v-slot:item="{ props, item }">
                  <v-list-item v-bind="props">
                    <template v-slot:prepend>
                      <v-icon size="x-small" color="primary">
                        {{ item.raw.icon }}
                      </v-icon>
                    </template>
                  </v-list-item>
                </template>
              </v-select>
            </div>

            <!-- 添加当前模式指示器 -->
            <div class="mode-indicator" :class="selectedMode.value == 'v2' ? 'mode-v2' : 'mode-r1'">
              <v-icon size="x-small" :color="selectedMode.value == 'v2' ? 'green-darken-1' : 'blue-darken-1'"
                class="mr-1">
                {{ selectedMode.value == 'v2' ? 'mdi-chart-bar' : 'mdi-chat-question' }}
              </v-icon>
              <span>{{ selectedMode.value == 'v2' ? '深度检索模式' : '日常问答模式' }}</span>
            </div>

            <v-text-field v-model="userMessage"
              :placeholder="selectedMode.value == 'v2' ? '请输入深度检索内容...' : '请输入您的问题...'"
              @keyup.enter="!isProcessing && userMessage.trim() && sendMessage()" hide-details variant="outlined"
              density="comfortable" bg-color="white" :disabled="false" class="input-field"
              :class="{ 'v2-input': selectedMode.value == 'v2' }">
            </v-text-field>

            <button class="send-button" :class="{ 'processing': isProcessing }" @click="handleButtonClick"
              :disabled="!userMessage.trim() && !isProcessing">
              {{ isProcessing ? '停止' : '发送' }}
            </button>
          </div>
          <div class="input-disclaimer" :class="{ 'v2-disclaimer': selectedMode.value == 'v2' }">
            <v-icon size="small" :color="selectedMode.value == 'v2' ? 'green-darken-1' : 'grey'">
              {{ selectedMode.value == 'v2' ? 'mdi-chart-box-outline' : 'mdi-information-outline' }}
            </v-icon>
            <span>{{ selectedMode.value == 'v2' ? '提示: 您可以深度检索项目知识库中的信息' : '提示: 您可以询问设备信息、系统操作或提供反馈' }}</span>
          </div>
        </div>
      </v-card>
    </div>

    <!-- 清除对话确认对话框 -->
    <v-dialog v-model="showClearConfirmDialog" :max-width="$vuetify.display.smAndDown ? '95%' : '400'">
      <v-card>
        <v-card-title class="bg-warning text-white">确认清除</v-card-title>
        <v-card-text class="pa-4 pt-5">
          是否确认清除当前对话？这将删除所有聊天记录，但会保留到历史记录中。
        </v-card-text>
        <v-card-actions class="pa-4 pt-0">
          <v-spacer></v-spacer>
          <v-btn color="grey" variant="text" @click="showClearConfirmDialog = false">取消</v-btn>
          <v-btn color="warning" @click="confirmClearConversation">确认清除</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Toast通知 -->
    <div v-if="toast.show" class="toast-container">
      <div class="toast" :class="toast.type">
        <v-icon size="small" color="white" class="mr-2">
          {{ toast.type == 'success' ? 'mdi-check-circle' : 'mdi-information' }}
        </v-icon>
        {{ toast.message }}
      </div>
    </div>

    <!-- 重命名对话框 -->
    <v-overlay v-model="showRenameDialog" class="align-center justify-center" scrim="rgba(0,0,0,0.5)">
      <div class="rename-dialog">
        <v-card min-width="400" max-width="90vw">
          <v-card-title class="bg-primary text-white d-flex align-center">
            <v-icon color="white" class="mr-2">mdi-pencil</v-icon>
            重命名对话
          </v-card-title>

          <v-card-text class="pa-4">
            <v-text-field v-model="newHistoryTitle" label="请输入新名称" variant="outlined" density="comfortable" hide-details
              autofocus @keyup.enter="newHistoryTitle.trim() && confirmRename()"
              @keyup.esc="showRenameDialog = false"></v-text-field>
          </v-card-text>

          <v-card-actions class="pa-4 pt-0">
            <v-spacer></v-spacer>
            <v-btn variant="text" @click="showRenameDialog = false">取消</v-btn>
            <v-btn color="primary" @click="confirmRename" :disabled="!newHistoryTitle.trim()">确认</v-btn>
          </v-card-actions>
        </v-card>
      </div>
    </v-overlay>

    <!-- 删除确认对话框 -->
    <v-dialog v-model="showDeleteConfirmDialog" class="delete-dialog align-center justify-center" max-width="400" max-height="170" persistent>
      <v-card class="delete-dialog-card">
        <v-card-title class="bg-error text-white">确认删除</v-card-title>
        <v-card-text class="pa-4 pt-5">
          是否确认删除该历史记录？此操作不可恢复。
        </v-card-text>
        <v-card-actions class="pa-4 pt-0">
          <v-spacer></v-spacer>
          <v-btn variant="text" @click="showDeleteConfirmDialog = false">取消</v-btn>
          <v-btn color="error" @click="confirmDelete">确认删除</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 修改菜单弹出层 -->
    <v-menu v-model="historyMenuOpen" :close-on-content-click="false" location="bottom" :position-x="menuPosition.x"
      :position-y="menuPosition.y" absolute min-width="180">
      <v-list density="compact" class="history-menu-list">
        <v-list-item @click="renameHistory(selectedHistoryItem)" class="history-menu-list-item">
          <template v-slot:prepend>
            <v-icon size="small" color="primary">mdi-pencil</v-icon>
          </template>
          <v-list-item-title>重命名</v-list-item-title>
        </v-list-item>
        <v-divider class="my-1"></v-divider>
        <v-list-item @click="deleteHistory(selectedHistoryItem)" class="history-menu-list-item">
          <template v-slot:prepend>
            <v-icon size="small" color="error">mdi-delete</v-icon>
          </template>
          <v-list-item-title class="text-error">删除</v-list-item-title>
        </v-list-item>
      </v-list>
    </v-menu>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch, computed } from 'vue';
import axios from 'axios';

// API 配置
import { CHAT_ID, AGENT_ID, AGENT_ID_NR, AGENT_ID_ZC, ENV } from '@/config/api.js';
import ragService from '@/utils/ragRequest';

// axios 请求头配置（仅保留Content-Type，Authorization和baseURL由ragService处理）
const axiosHeaders = {
  'Content-Type': 'application/json',
  'Accept': '*/*',
  'Connection': 'keep-alive'
};

const userMessage = ref('');
const sessionId = ref('');
const messages = ref([]);
const chatContainer = ref(null);
const isProcessing = ref(false);
const showClearConfirmDialog = ref(false);
const chatHistory = ref({
  recent7Days: [],
  recent30Days: [],
  before30Days: []
});
const selectedHistoryIndex = ref(-1);
const latestReferenceChunks = ref([]);
const typingCompleted = ref(false);
const toast = ref({
  show: false,
  message: '',
  type: 'success', // 'success' or 'info'
  timeout: null
});

// 模式选择器
const selectedMode = ref({
  icon: 'mdi-brain',
  text: '日常问答',
  value: 'r1'
});
const modeOptions = [
  {
    icon: 'mdi-brain',
    text: '日常问答',
    value: 'r1'
  },
  {
    icon: 'mdi-thought-bubble',
    text: '深度检索',
    value: 'v2'
  },
  {
    icon: 'mdi-pencil-outline',
    text: '内容创作',
    value: 'content_creation'
  },
  {
    icon: 'mdi-gavel',
    text: '政策问答',
    value: 'policy_qa'
  }
];

// 响应式设计相关
const isMobile = ref(false);
const isLandscape = ref(false);

// 欢迎消息 - 仅包含结构和建议问题，内容将由API响应填充
const welcomeMessage = {
  type: 'system',
  welcome: true,
  content: '', // 内容将从API响应中获取
  typingCompleted: true,
  showReferences: true,
  suggestedQuestions: [
    'BA系统有哪些设备?',
    '5号风机在哪在哪里?',
    '门禁系统有多少设备?',
    'BA系统平台使用要求什么?',
    'DDC控制柜KD-5200参数是什么?',
    '本项目的安防系统是如何设计的?',
    'BA系统是否有报警功能?',
    '智能照明系统如何控制?',
    '今日能耗统计是多少?',
    '本项目在建数据如何?',
    '当前工单处理情况?',
    '3楼办公区电表实时数据?'
  ]
};

// 创建新会话
const createNewSession = async () => {
  // 如果当前有对话内容且不是在查看历史记录，才保存到历史记录
  if (messages.value.length > 1 && selectedHistoryIndex.value == -1) {
    saveCurrentConversation();
  }

  // 重置选中的历史记录索引
  selectedHistoryIndex.value = -1;

  // 创建新会话
  isProcessing.value = true;
  const result = await createSession();
  isProcessing.value = false;

  if (result) {
    showToast('已创建新会话');
  }
};

// 创建会话
const createSession = async () => {
  try {
    const data = JSON.stringify({
      "name": "新对话",
      "env": ENV
    });

    // 根据当前选择的模式决定使用哪个API端点
    let agentId;
    switch (selectedMode.value.value) {
      case 'v2':
        agentId = AGENT_ID;
        break;
      case 'content_creation':
        agentId = AGENT_ID_NR;
        break;
      case 'policy_qa':
        agentId = AGENT_ID_ZC;
        break;
      case 'r1':
      default:
        agentId = CHAT_ID;
        break;
    }
    const url = `${ragService.baseURL}/api/v1/agents/${agentId}/sessions`;

    const config = {
      method: 'post',
      url: url,
      headers: axiosHeaders,
      data: data
    };

    const response = await axios(config);

    if (response.status !== 200) {
      throw new Error(`创建会话失败: ${response.status}`);
    }

    // 根据当前模式提取会话ID
    if (selectedMode.value.value == 'v2') {
      // 问数BOT模式的会话ID结构
      sessionId.value = response.data.data?.id || '';
      console.log('问数BOT会话ID:', sessionId.value);
    } else {
      // 问答BOT模式的会话ID结构
      sessionId.value = response.data.data?.id || '';
      console.log('问答BOT会话ID:', sessionId.value);
    }

    // 创建欢迎消息的基础结构
    const baseWelcomeMsg = {
      type: 'system',
      welcome: true,
      suggestedQuestions: welcomeMessage.suggestedQuestions,
      typingCompleted: true,
      showReferences: true
    };
    console.log('API响应:', response.data);

    // 根据不同模式获取正确的欢迎消息
    try {
      if (selectedMode.value.value == 'v2') {
        // 问数BOT使用 message 结构
        if (response.data.data.message.length > 0) {
          baseWelcomeMsg.content = response.data.data.message[0].content || '';
          console.log('问数BOT欢迎消息:', baseWelcomeMsg.content);
        } else {
          baseWelcomeMsg.content = '您好，我是数据查询助手，可以帮您查询各类数据。';
          console.log('问数BOT未找到欢迎消息，使用默认消息');
        }
      } else {
        // 问答BOT使用 data.messages 结构
        if (response.data.data.messages.length > 0) {
          baseWelcomeMsg.content = response.data.data.messages[0].content || '';
          console.log('问答BOT欢迎消息:', baseWelcomeMsg.content);
        } else {
          baseWelcomeMsg.content = '您好，我是ArchiMind智能助手，可以回答您关于项目设备、系统和图纸的各类问题。';
          console.log('问答BOT未找到欢迎消息，使用默认消息');
        }
      }
    } catch (error) {
      console.error('获取欢迎消息失败:', error);
      // 错误时使用默认消息
      baseWelcomeMsg.content = '您好，我是智能助手，可以回答您的问题。';
    }

    // 设置欢迎消息
    messages.value = [baseWelcomeMsg];

    // 如果是移动设备，调整建议问题
    if (isMobile.value) {
      adaptSuggestedQuestions();
    }

    return true;
  } catch (error) {
    console.error('创建会话失败:', error);
    showToast('会话创建失败，请刷新页面重试', 'error');
    return false;
  }
};

// 发送消息并处理流式响应
const sendMessageToAPI = async (question) => {
  if (!sessionId.value) {
    showToast('会话未创建，请刷新页面重试', 'error');
    return false;
  }

  try {
    // 添加系统思考状态消息
    const thinkingMessageIndex = messages.value.length;
    messages.value.push({ type: 'system', content: '', thinking: true });

    const data = JSON.stringify({
      question: question,
      stream: true,
      session_id: sessionId.value,
      env: ENV
    });

    // 处理流式响应
    const controller = new AbortController();
    activeController.value = controller; // 保存当前控制器引用
    const signal = controller.signal;

    // 创建一个变量来保存累积的响应文本
    let accumulatedText = '';
    let displayedLength = 0;
    let typingInterval = null;
    let isTypingStarted = false; // 标记是否已开始打字效果

    // 根据当前模式选择正确的API端点
    let agentId;
    switch (selectedMode.value.value) {
      case 'v2':
        agentId = AGENT_ID;
        break;
      case 'content_creation':
        agentId = AGENT_ID_NR;
        break;
      case 'policy_qa':
        agentId = AGENT_ID_ZC;
        break;
      case 'r1':
      default:
        agentId = CHAT_ID;
        break;
    }
    const apiUrl = `${ragService.baseURL}/api/v1/agents/${agentId}/completions`;

    // 使用axios设置
    const config = {
      method: 'post',
      url: apiUrl,
      headers: {
        'Authorization': `Bearer ${ragService.token}`,
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'Host': 'ragflow-4029862aab3ad76af3e3a564499a320b.zcznbj.com',
        'Connection': 'keep-alive'
      },
      data: data,
      responseType: 'stream', // 使用JSON模式接收响应
      onDownloadProgress: (progressEvent) => {
        try {
          // 获取已接收的数据
          const response = progressEvent.event.currentTarget.response;

          // 如果响应存在并且有数据
          if (response && typeof response == 'string') {
            // 分割多行SSE数据
            const lines = response.split('\n').filter(line => line.trim() !== '');

            // 检查最后一行是否是结束标志
            const isCompleted = lines.some(line => line == 'data:{"code": 0, "data": true}');

            // 提取所有有效回答以及最新的有效回答
            let latestAnswer = null;
            let validAnswers = [];

            // 处理所有行，获取有效回答序列和最新回答
            for (let i = 0; i < lines.length; i++) {
              const line = lines[i];
              if (line.startsWith('data:')) {
                try {
                  const jsonStr = line.substring(5); // 去掉"data:"前缀
                  const jsonData = JSON.parse(jsonStr);

                  if (jsonData.data && jsonData.data.answer) {
                    // 添加到有效回答列表
                    validAnswers.push(jsonData.data.answer);

                    // 更新最新回答
                    latestAnswer = jsonData.data.answer;

                    // 提取参考文献信息
                    if (jsonData.data.reference && jsonData.data.reference.chunks) {
                      latestReferenceChunks.value = jsonData.data.reference.chunks;
                      // 去重
                      latestReferenceChunks.value = deduplicateReferences(latestReferenceChunks.value);
                      console.log('找到参考文献:', latestReferenceChunks.value);
                    }
                  }
                } catch (e) {
                  continue;
                }
              }
            }

            // 如果找到了有效回答
            if (latestAnswer) {
              try {
                // 处理回答，保留<think>...</think>部分，并处理JavaScript代码块
                let processedText = latestAnswer;

                // 检测是否包含思考部分
                const hasThinking = processedText.includes('<think>');

                // 检测是否包含JavaScript代码块
                const hasJavaScriptCode = processedText.includes('```javascript') || processedText.includes('```js');

                // 如果包含JavaScript代码块，进行高亮和复制按钮处理
                if (hasJavaScriptCode && selectedMode.value.value == 'v2') {
                  console.log('检测到JavaScript代码块，进行高亮处理');

                  // 使用正则表达式匹配```javascript或```js开头，```结尾的代码块
                  const codeBlockRegex = /```(javascript|js)([\s\S]*?)```/g;
                  let codeBlockId = 0;

                  // 基本语法高亮函数
                  const highlightJs = (code) => {
                    // 定义正则模式
                    const patterns = {
                      keywords: /\b(const|let|var|function|return|if|else|for|while|class|import|export|from|try|catch|throw|new|this|async|await)\b/g,
                      strings: /(["'`])(.*?)\1/g,
                      numbers: /\b(\d+(\.\d+)?)\b/g,
                      comments: /(\/\/.*$)|(\/\*[\s\S]*?\*\/)/gm,
                      functions: /\b([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(/g,
                      operators: /([+\-*/%=&|^!<>]+)/g,
                      classNames: /\bclass\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\b/g
                    };

                    // HTML转义函数
                    const escapeHtml = (text) => {
                      return text
                        .replace(/&/g, "&amp;")
                        .replace(/</g, "&lt;")
                        .replace(/>/g, "&gt;")
                        .replace(/"/g, "&quot;")
                        .replace(/'/g, "&#039;");
                    };

                    // 转义代码
                    let highlightedCode = escapeHtml(code);

                    // 应用高亮
                    highlightedCode = highlightedCode
                      .replace(patterns.keywords, '<span class="keyword">$&</span>')
                      .replace(patterns.strings, '<span class="string">$&</span>')
                      .replace(patterns.numbers, '<span class="number">$&</span>')
                      .replace(patterns.comments, '<span class="comment">$&</span>')
                      .replace(patterns.functions, (match, p1) => {
                        return match.replace(p1, '<span class="function">' + p1 + '</span>');
                      })
                      .replace(patterns.operators, '<span class="operator">$&</span>')
                      .replace(patterns.classNames, (match, p1) => {
                        return match.replace(p1, '<span class="class-name">' + p1 + '</span>');
                      });

                    return highlightedCode;
                  };

                  // 替换所有代码块为带有高亮和复制按钮的HTML
                  processedText = processedText.replace(codeBlockRegex, (match, lang, code) => {
                    codeBlockId++;

                    // 对代码进行语法高亮
                    const highlightedCode = highlightJs(code.trim());

                    return `
                      <div class="code-block">
                        <div class="code-header">
                          <span class="code-language">JavaScript</span>
                          <button class="copy-button" onclick="copyCodeToClipboard('code-${codeBlockId}')">
                            <v-icon size="small">mdi-content-copy</v-icon> 复制
                          </button>
                        </div>
                        <pre id="code-${codeBlockId}" class="code-content"><code class="language-javascript">${highlightedCode}</code></pre>
                      </div>
                    `;
                  });

                  // 为页面添加复制功能逻辑
                  if (processedText.includes('code-block') && !window.copyCodeToClipboard) {
                    window.copyCodeToClipboard = function (codeId) {
                      const codeElement = document.getElementById(codeId);
                      if (codeElement) {
                        // 提取原始代码文本（不包含HTML标签）
                        const codeText = codeElement.textContent;

                        // 创建一个临时文本区域用于复制
                        const textarea = document.createElement('textarea');
                        textarea.value = codeText;
                        textarea.style.position = 'fixed';
                        textarea.style.opacity = '0';
                        document.body.appendChild(textarea);
                        textarea.select();

                        try {
                          // 尝试使用复制命令
                          const successful = document.execCommand('copy');
                          if (successful) {
                            // 显示复制成功的视觉反馈
                            const button = codeElement.closest('.code-block').querySelector('.copy-button');
                            const originalText = button.innerHTML;
                            button.innerHTML = '<v-icon size="small">mdi-check</v-icon> 已复制';
                            setTimeout(() => {
                              button.innerHTML = originalText;
                            }, 2000);
                          } else {
                            // 如果execCommand失败，尝试使用clipboard API
                            navigator.clipboard.writeText(codeText)
                              .then(() => {
                                const button = codeElement.closest('.code-block').querySelector('.copy-button');
                                const originalText = button.innerHTML;
                                button.innerHTML = '<v-icon size="small">mdi-check</v-icon> 已复制';
                                setTimeout(() => {
                                  button.innerHTML = originalText;
                                }, 2000);
                              })
                              .catch(err => console.error('复制失败:', err));
                          }
                        } catch (err) {
                          console.error('复制过程中出错:', err);
                        } finally {
                          // 清理临时元素
                          document.body.removeChild(textarea);
                        }
                      }
                    };

                    // 创建一个专门用于复制功能的全局脚本
                    if (!document.getElementById('copy-code-script')) {
                      const scriptElement = document.createElement('script');
                      scriptElement.id = 'copy-code-script';
                      scriptElement.textContent = `
                        // 该脚本仅用于确保copyCodeToClipboard函数在DOM中可访问
                        if (typeof window.copyCodeToClipboard !== 'function') {
                          window.copyCodeToClipboard = ${window.copyCodeToClipboard.toString()};
                        }
                      `;
                      document.body.appendChild(scriptElement);
                    }
                  }
                }

                // 如果包含思考部分，进行处理
                if (hasThinking) {
                  console.log('检测到思考文本，进行样式处理');

                  // 将<think>标签替换为带内联样式的span，而非使用CSS类
                  processedText = processedText
                    .replace(/<think>/g, '<span style="display:block; background-color:#f5f5f5; color:#666; padding:8px 10px; margin:4px 0; border-radius:4px;">')
                    .replace(/<\/think>/g, '</span>');

                  console.log('替换后的文本:', processedText);
                }

                // 处理换行符，确保正确显示，但保留代码块内的格式
                if (!hasJavaScriptCode) {
                  processedText = processedText.replace(/\n/g, '<br>');
                }

                accumulatedText = processedText;
              } catch (e) {
                // 如果处理失败，直接使用原始文本
                accumulatedText = latestAnswer;
                console.error('处理思考文本失败:', e);
              }

              // 针对问数模式(v2)的特殊处理 - 显示前6条消息的思考过程
              if (selectedMode.value.value == 'v2') {
                // 如果已完成响应，显示最终答案
                if (isCompleted) {
                  // 清除现有的定时器
                  if (typingInterval) {
                    clearInterval(typingInterval);
                  }

                  // 更新消息内容为最终完整答案
                  messages.value[thinkingMessageIndex] = {
                    type: 'system',
                    content: accumulatedText,
                    thinking: false,
                    references: latestReferenceChunks.value && latestReferenceChunks.value.length > 0 ? latestReferenceChunks.value : null,
                    typingCompleted: true,
                    showReferences: true
                  };

                  // 滚动到最新消息
                  nextTick(() => scrollToBottom());
                }
                // 如果还在接收数据，且有新的消息，就显示前6条作为思考过程
                else if (validAnswers.length > 0 && validAnswers.length <= 6) {
                  // 获取当前接收到的答案
                  const currentAnswer = validAnswers[validAnswers.length - 1];

                  // 构建思考过程展示文本
                  let thinkingContent = '';
                  for (let i = 0; i < validAnswers.length; i++) {
                    thinkingContent += `<span style="display:block; background-color:#f5f5f5; color:#666; padding:8px 10px; margin:4px 0; border-radius:4px;">思考过程 ${i + 1}/${validAnswers.length}:<br>${validAnswers[i].replace(/\n/g, '<br>')}</span>`;
                  }

                  // 更新消息显示思考过程
                  messages.value[thinkingMessageIndex] = {
                    type: 'system',
                    content: thinkingContent,
                    thinking: false,
                    references: null,
                    typingCompleted: false,
                    showReferences: false
                  };

                  // 滚动到最新消息
                  nextTick(() => scrollToBottom());
                }
              }
              // 非问数模式(r1)的原有逻辑
              else {
                // 如果已经完成响应，开始打字效果
                if (isCompleted && !isTypingStarted) {
                  // 清除现有的打字效果计时器
                  if (typingInterval) {
                    clearInterval(typingInterval);
                  }

                  // 重置显示长度和标记
                  displayedLength = 0;
                  isTypingStarted = true;

                  console.log('开始打字效果，完整答案:', accumulatedText);

                  // 启动打字效果
                  typingInterval = setInterval(() => {
                    if (displayedLength < accumulatedText.length) {
                      // 每次显示更多字符
                      const increment = Math.floor(Math.random() * 3) + 1;
                      displayedLength = Math.min(displayedLength + increment, accumulatedText.length);
                      const textToShow = accumulatedText.substring(0, displayedLength);

                      // 更新消息内容
                      messages.value[thinkingMessageIndex] = {
                        type: 'system',
                        content: textToShow,
                        thinking: false,
                        references: latestReferenceChunks.value && latestReferenceChunks.value.length > 0 ? latestReferenceChunks.value : null,
                        typingCompleted: false,
                        showReferences: false
                      };

                      // 滚动到最新消息
                      nextTick(() => scrollToBottom());
                    } else {
                      // 如果已经显示完整内容，清除定时器
                      clearInterval(typingInterval);
                      typingInterval = null;
                      // 标记打字效果完成
                      typingCompleted.value = true;
                      // 更新消息标记为打字完成，并显示参考文献
                      messages.value[thinkingMessageIndex] = {
                        ...messages.value[thinkingMessageIndex],
                        typingCompleted: true,
                        showReferences: true
                      };
                    }
                  }, 20);
                }
                // 如果没有开始打字效果，显示思考中状态
                else if (!isTypingStarted) {
                  messages.value[thinkingMessageIndex] = {
                    type: 'system',
                    content: '',
                    thinking: true  // 保持思考状态直到完成
                  };
                }
              }
            }
          }
        } catch (e) {
          console.error('处理响应数据失败:', e);
        }
      },
      signal // 引用上面已声明的signal变量，移除重复声明
    };

    try {
      const response = await axios(config)

      if (response.status !== 200) {
        throw new Error(`发送消息失败: ${response.status}`);
      }

      // 确保最终内容已完全显示
      if (accumulatedText) {
        messages.value[thinkingMessageIndex] = {
          type: 'system',
          content: accumulatedText,
          thinking: false,
          references: latestReferenceChunks.value && latestReferenceChunks.value.length > 0 ? latestReferenceChunks.value : null,
          typingCompleted: true,
          showReferences: true
        };
      } else if (response.data) {
        // 如果没有通过流获取到答案，尝试提取答案
        // 检查多种可能的响应结构
        let answer = null;

        // 打印原始响应数据，不做任何处理
        console.log('原始响应数据:', response.data);
        console.log('响应数据类型:', typeof response.data);
        console.log('响应对象结构:', Object.keys(response.data));

        // 暂时不进行任何处理，只显示正在加载
        messages.value[thinkingMessageIndex] = {
          type: 'system',
          content: "已接收到响应数据，请查看控制台输出",
          thinking: false,
          typingCompleted: true,
          showReferences: true
        };
      }

      await scrollToBottom();
      return true;
    } catch (error) {
      // 清除打字动画定时器
      if (typingInterval) {
        clearInterval(typingInterval);
      }
      throw error;
    } finally {
      activeController.value = null; // 请求完成后清除控制器引用
    }
  } catch (error) {
    console.error('发送消息失败:', error);
    // 移除思考状态，显示错误消息
    const lastIndex = messages.value.length - 1;
    if (messages.value[lastIndex] && messages.value[lastIndex].thinking) {
      messages.value[lastIndex] = {
        type: 'system',
        content: '抱歉，发送消息时出现错误，请稍后重试。',
        typingCompleted: true,
        showReferences: true
      };
    }
    return false;
  }
};

// 适配小屏幕设备的建议问题
const adaptSuggestedQuestions = () => {
  if (isMobile.value && messages.value.length > 0 && messages.value[0].welcome) {
    // 如果是移动设备，减少建议问题数量
    const mobileQuestions = [
      'BA系统有哪些设备?',
      '门禁系统有多少设备?',
      'DDC控制柜KD-5200参数是什么?',
      '3楼办公区电表实时数据?'
    ];
    messages.value[0].suggestedQuestions = mobileQuestions;
  }
};

// 检测设备类型和方向
const checkDeviceAndOrientation = () => {
  // 检测是否为移动设备（宽度小于768px）
  isMobile.value = window.innerWidth < 768;

  // 检测是否为横屏
  isLandscape.value = window.innerWidth > window.innerHeight;

  // 滚动到底部（避免屏幕旋转后消息不可见）
  nextTick(() => {
    scrollToBottom();
  });
};

// 生成时间戳
const generateTimestamp = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 清除当前对话
const clearConversation = () => {
  // 如果有对话内容，则显示确认对话框
  if (messages.value.length > 1) {
    showClearConfirmDialog.value = true;
  } else {
    // 如果只有欢迎消息，直接重置
    resetConversation();
  }
};

// 确认清除当前对话
const confirmClearConversation = async () => {
  // 保存当前对话到历史记录
  if (messages.value.length > 1) {
    saveCurrentConversation();
  }

  // 重新创建会话
  await createSession();

  // 关闭确认对话框
  showClearConfirmDialog.value = false;

  // 显示Toast消息
  showToast('对话已清除');
};

// 保存当前对话到历史记录
const saveCurrentConversation = () => {
  // 只有在不是查看历史记录时才保存
  if (selectedHistoryIndex.value !== -1) {
    return;
  }

  try {
    // 查找第一个用户消息作为标题
    const firstUserMessage = messages.value.find(msg => msg.type == 'user');
    const title = firstUserMessage ? firstUserMessage.content : '未命名对话';

    // 创建唯一ID
    const id = `chat_${Date.now().toString()}`;

    // 处理消息，保留参考文档数据
    const processedMessages = JSON.parse(JSON.stringify(messages.value)).map(msg => {
      // 保留基本属性和参考文档数据
      const { type, content, welcome, suggestedQuestions, references, showReferences } = msg;
      const processedMsg = { type, content };

      // 只为欢迎消息保留建议问题
      if (welcome) {
        processedMsg.welcome = true;
        processedMsg.suggestedQuestions = suggestedQuestions;
      }

      // 保留参考文档数据
      if (references && references.length > 0) {
        processedMsg.references = references;
        processedMsg.showReferences = showReferences;
      }

      return processedMsg;
    });

    const conversation = {
      id,
      title,
      messages: processedMessages,
      timestamp: generateTimestamp(),
      sessionId: sessionId.value,
      mode: selectedMode.value
    };

    // 添加到开头
    chatHistory.value.unshift(conversation);

    // 最多保留10条历史记录
    if (chatHistory.value.length > 10) {
      chatHistory.value = chatHistory.value.slice(0, 10);
    }

    // 保存到localStorage
    try {
      localStorage.setItem('chatHistory', JSON.stringify(chatHistory.value));
    } catch (e) {
      console.error('保存历史记录到localStorage失败:', e);
      // 尝试清理过大的历史记录
      const reducedHistory = chatHistory.value.map(h => ({
        id: h.id,
        title: h.title,
        timestamp: h.timestamp,
        sessionId: h.sessionId,
        mode: h.mode,
        // 只保留最多2条消息
        messages: h.messages.slice(0, 2)
      }));

      try {
        localStorage.setItem('chatHistory', JSON.stringify(reducedHistory));
      } catch (e) {
        console.error('即使在减少数据后仍无法保存历史记录:', e);
      }
    }
  } catch (e) {
    console.error('处理历史记录时出错:', e);
  }
};

// 重置对话
const resetConversation = async () => {
  // 创建新会话
  await createSession();
};

// 加载历史对话
const loadHistoryConversation = (history) => {
  try {
    // 加载历史对话
    let newSessionId = '';
    
    // 从多个可能的位置获取会话ID，优先使用conversationId（最准确对应后端会话）
    if (history.apiData && history.apiData.conversationId) {
      newSessionId = history.apiData.conversationId;
      console.log('使用apiData.conversationId作为会话ID:', newSessionId);
    } else if (history.sessionId) {
      newSessionId = history.sessionId;
      console.log('使用sessionId作为会话ID:', newSessionId);
    } else if (history.apiData && history.apiData.id) {
      newSessionId = `${history.apiData.id}`;
      console.log('使用apiData.id作为会话ID:', newSessionId);
    }
    
    // 输出详细的调试信息
    console.log('===== 历史记录加载信息 =====');
    console.log('历史记录对象:', history);
    console.log('选择的会话ID:', newSessionId);
    console.log('==========================');
    
    // 更新会话ID
    sessionId.value = newSessionId;
    
    // 同时更新localStorage中的会话ID
    if (newSessionId) {
      localStorage.setItem('current_conversation_id', newSessionId);
      console.log('设置的会话ID:', newSessionId, '并已保存到localStorage');
      console.log('更新后的sessionId:', sessionId.value);
      console.log('更新后的localStorage:', localStorage.getItem('current_conversation_id'));
    } else {
      console.warn('未能从历史记录中提取有效的会话ID');
    }

    // 处理历史消息，确保结构一致性
    const processedMessages = history.messages.map(msg => {
      // 确保基本属性存在
      const processedMsg = {
        type: msg.type || 'system',
        content: msg.content || '',
        typingCompleted: true,
        showReferences: msg.showReferences || false
      };

      // 如果是欢迎消息，添加欢迎消息特有属性
      if (msg.welcome) {
        processedMsg.welcome = true;
        processedMsg.suggestedQuestions = msg.suggestedQuestions || welcomeMessage.suggestedQuestions;
      }

      // 恢复参考文档数据
      if (msg.references && msg.references.length > 0) {
        processedMsg.references = msg.references;
        processedMsg.showReferences = true;
      }

      return processedMsg;
    });

    messages.value = processedMessages;

    // 设置选中的历史索引
    selectedHistoryIndex.value = chatHistory.value.findIndex(h => h.id == history.id);

    // 设置当前模式
    if (history.mode) {
      selectedMode.value = history.mode;
    } else {
      // 向后兼容，没有模式信息的旧历史记录
      selectedMode.value = modeOptions[0]; // 默认使用第一个模式
    }

    // 显示Toast消息
    showToast('已加载历史对话');

    // 如果没有会话ID，需要创建新会话
    if (!sessionId.value) {
      createSession();
    }
  } catch (e) {
    console.error('加载历史对话失败:', e);
    showToast('加载对话失败，创建新会话', 'error');
    createSession();
  }
};

// 清空历史记录
const clearHistory = () => {
  chatHistory.value = {
    recent7Days: [],
    recent30Days: [],
    before30Days: []
  };
  selectedHistoryIndex.value = -1;
  localStorage.removeItem('chatHistory');
  showToast('历史记录已清空');
};

// 获取历史记录标题
const getHistoryTitle = (history) => {
  // 优先使用API数据中的标题
  const title = history.apiData ? history.apiData.conversationTitle : history.title;
  if (!title) return '未命名对话';
  return title.length > 30 ? title.substring(0, 30) + '...' : title;
};

// 显示Toast消息
const showToast = (message, type = 'success') => {
  // 清除现有的timeout
  if (toast.value.timeout) {
    clearTimeout(toast.value.timeout);
  }

  // 设置新的toast
  toast.value = {
    show: true,
    message,
    type,
    timeout: null
  };

  // 3秒后自动隐藏
  toast.value.timeout = setTimeout(() => {
    toast.value.show = false;
  }, 3000);
};

// 滚动到最新消息
const scrollToBottom = async () => {
  await nextTick();
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
  }
};

// 发送消息
const sendMessage = async () => {
  if (!userMessage.value.trim() || isProcessing.value) return;

  // 特殊测试用例 - 用于测试思考样式
  if (userMessage.value == 'test-thinking') {
    messages.value.push({ type: 'user', content: '测试思考样式' });

    // 添加系统响应
    const thinkingStyle = 'display:block; background-color:#f5f5f5; color:#666; padding:8px 10px; margin:4px 0; border-radius:4px;';
    const testContent = `<span style="${thinkingStyle}">思考内容，浅灰色样式</span>实际回答，正常黑色样式`;

    messages.value.push({
      type: 'system',
      content: testContent,
      thinking: false
    });

    userMessage.value = '';
    await scrollToBottom();
    return;
  }

  isProcessing.value = true;

  // 重置参考文献
  latestReferenceChunks.value = [];

  // 添加用户消息
  messages.value.push({ type: 'user', content: userMessage.value });
  const question = userMessage.value;
  userMessage.value = '';

  // 滚动到最新消息
  await scrollToBottom();

  // 如果没有会话ID，先创建会话
  if (!sessionId.value) {
    const sessionCreated = await createSession();
    if (!sessionCreated) {
      isProcessing.value = false;
      return;
    }
  }

  // 调用API发送消息
  await sendMessageToAPI(question);

  isProcessing.value = false;
};

// 处理建议问题点击
const askSuggestedQuestion = async (question) => {
  if (isProcessing.value) return;

  isProcessing.value = true;

  // 重置参考文献
  latestReferenceChunks.value = [];

  // 添加用户消息
  messages.value.push({ type: 'user', content: question });

  // 滚动到最新消息
  await scrollToBottom();

  // 如果没有会话ID，先创建会话
  if (!sessionId.value) {
    const sessionCreated = await createSession();
    if (!sessionCreated) {
      isProcessing.value = false;
      return;
    }
  }

  // 调用API发送消息
  await sendMessageToAPI(question);

  isProcessing.value = false;
};

// 监听消息变化，自动滚动
watch(messages, async () => {
  await scrollToBottom();
}, { deep: true });

// 监听移动设备状态变化
watch(isMobile, (newValue) => {
  if (newValue && messages.value.length > 0 && messages.value[0].welcome) {
    adaptSuggestedQuestions();
  } else if (!newValue && messages.value.length > 0 && messages.value[0].welcome) {
    // 恢复完整建议问题
    messages.value[0].suggestedQuestions = welcomeMessage.suggestedQuestions;
  }
});

// 下载文档
const downloadDocument = (documentId) => {
  if (!documentId) {
    showToast('无法下载：缺少文档ID', 'error');
    return;
  }

  try {
    // 构建下载URL
    const downloadUrl = `${ragService.baseURL}/document/${documentId}?ext=docx&prefix=document`;

    // 创建一个隐形链接进行下载
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.setAttribute('download', `document_${documentId}.docx`);
    link.setAttribute('target', '_blank');

    // 添加到DOM并触发点击
    document.body.appendChild(link);
    link.click();

    // 清理DOM
    document.body.removeChild(link);

    showToast('开始下载文档');
  } catch (e) {
    console.error('下载文档失败:', e);
    showToast('下载文档失败', 'error');
  }
};

// 获取唯一参考文献
const getUniqueReferences = (references) => {
  if (!references || !Array.isArray(references)) {
    return [];
  }

  const uniqueRefs = [];
  const seen = new Set();

  for (const ref of references) {
    if (ref && ref.document_id && !seen.has(ref.document_id)) {
      uniqueRefs.push(ref);
      seen.add(ref.document_id);
    }
  }

  return uniqueRefs;
};

// 去重参考文献
const deduplicateReferences = (references) => {
  return getUniqueReferences(references);
};

// 在 script setup 部分添加新的变量和方法
const activeController = ref(null);
let typingInterval = null; // 声明在全局范围内以便在cancelRequest中访问

// 添加处理按钮点击的函数
const handleButtonClick = () => {
  if (isProcessing.value) {
    // 如果正在处理中，先强制设置处理状态为false以立即更新UI
    isProcessing.value = false;
    nextTick(() => {
      cancelRequest();
    });
  } else if (userMessage.value.trim()) {
    // 如果有输入内容且不在处理中，发送消息
    sendMessage();
  }
};

// 取消正在进行的请求
const cancelRequest = () => {
  console.log('开始取消请求');

  // 清除任何正在进行的定时器
  if (typingInterval) {
    console.log('清除打字动画定时器');
    clearInterval(typingInterval);
    typingInterval = null;
  }

  if (activeController.value) {
    console.log('中止请求控制器');
    try {
      // 中止请求
      activeController.value.abort();

      // 更新最后一条消息，显示请求已取消
      const lastIndex = messages.value.length - 1;
      if (messages.value[lastIndex] && messages.value[lastIndex].thinking) {
        console.log('更新消息为已取消状态');
        messages.value[lastIndex] = {
          type: 'system',
          content: '请求已取消',
          typingCompleted: true,
          showReferences: false,
          canceled: true // 标记为已取消
        };

        // 强制组件刷新
        nextTick(() => {
          console.log('消息更新后的状态:', messages.value[lastIndex]);
          // 确认最新消息的状态
          const latestMessage = messages.value[messages.value.length - 1];
          console.log('最新消息是否标记为已取消:', latestMessage.canceled);
        });
      } else {
        console.log('没有找到思考中的消息:', messages.value);
      }

      showToast('请求已取消', 'info');
    } catch (error) {
      console.error('取消请求时出错:', error);
    } finally {
      // 确保控制器引用被清除
      activeController.value = null;
    }
  } else {
    console.log('没有活动的请求控制器');
  }
};

// 添加这个手表监听器在 script setup 区域
watch(selectedMode, async (newMode, oldMode) => {
  // 只有当值真正变化时才显示提示
  if (oldMode && newMode.value !== oldMode.value) {
    // 显示切换模式的提示
    showToast(`已切换到${newMode.text}模式`, 'info');

    // 先保存当前对话
    if (messages.value.length > 1) {
      saveCurrentConversation();
    }

    // 重置会话ID，强制创建新会话
    sessionId.value = '';

    // 立即创建新会话
    isProcessing.value = true;
    const result = await createSession();
    isProcessing.value = false;

    if (result) {
      // 不再手动添加消息，由createSession函数处理API返回的欢迎消息
      // 滚动到底部显示新消息
      nextTick(() => scrollToBottom());
    }
  }
}, { deep: true });

// 添加计算属性，根据日期对历史记录进行分组
const recentHistory = computed(() => {
  // 获取7天前的时间戳
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

  return chatHistory.value.filter(history => {
    const historyDate = parseHistoryDate(history.timestamp);
    return historyDate >= sevenDaysAgo;
  });
});

const olderHistory = computed(() => {
  // 获取7天和30天前的时间戳
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  return chatHistory.value.filter(history => {
    const historyDate = parseHistoryDate(history.timestamp);
    return historyDate < sevenDaysAgo && historyDate >= thirtyDaysAgo;
  });
});

// 获取历史记录在完整列表中的索引
const getHistoryFullIndex = (history) => {
  // 在所有三个分组中查找历史记录
  let index = -1;
  let offset = 0;
  
  // 在recent7Days中查找
  index = chatHistory.value.recent7Days.findIndex(item => item.id == history.id);
  if (index !== -1) return index;
  
  // 在recent30Days中查找
  offset = chatHistory.value.recent7Days.length;
  index = chatHistory.value.recent30Days.findIndex(item => item.id == history.id);
  if (index !== -1) return offset + index;
  
  // 在before30Days中查找
  offset += chatHistory.value.recent30Days.length;
  index = chatHistory.value.before30Days.findIndex(item => item.id == history.id);
  if (index !== -1) return offset + index;
  
  return -1; // 未找到
};

// 解析历史记录的日期字符串
const parseHistoryDate = (dateStr) => {
  if (!dateStr) return new Date();

  // 假设格式为 "YYYY-MM-DD HH:MM:SS"
  try {
    const [datePart, timePart] = dateStr.split(' ');
    const [year, month, day] = datePart.split('-').map(Number);
    const [hours, minutes, seconds] = timePart ? timePart.split(':').map(Number) : [0, 0, 0];

    return new Date(year, month - 1, day, hours, minutes, seconds);
  } catch (e) {
    console.error('日期解析错误:', e);
    return new Date(); // 返回当前日期作为回退
  }
};

// 格式化历史记录日期显示
const formatHistoryDate = (dateStr) => {
  if (!dateStr) return '';

  // 处理API返回的日期格式或本地格式
  const historyDate = parseHistoryDate(dateStr);
  
  // 直接返回完整的日期时间格式
  const year = historyDate.getFullYear();
  const month = String(historyDate.getMonth() + 1).padStart(2, '0');
  const day = String(historyDate.getDate()).padStart(2, '0');
  const hours = historyDate.getHours().toString().padStart(2, '0');
  const minutes = historyDate.getMinutes().toString().padStart(2, '0');
  const seconds = historyDate.getSeconds().toString().padStart(2, '0');
  
  // 返回完整的日期时间格式：YYYY-MM-DD HH:MM:SS
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 添加模拟数据方法
const addMockHistoryData = () => {
  // 只有在历史记录为空时添加模拟数据，避免重复
  if (chatHistory.value.length === 0) {
    const today = new Date();

    // 创建一些模拟历史记录
    const mockData = [
      {
        id: 'mock_1',
        title: 'BA系统设备查询',
        messages: [
          { type: 'system', content: '您好，我是智能助手' },
          { type: 'user', content: 'BA系统有哪些设备?' },
          { type: 'system', content: 'BA系统包括温度传感器、湿度传感器、CO2浓度传感器等多种设备...' }
        ],
        timestamp: formatDateForHistory(today),
        sessionId: 'mock-session-1',
        mode: modeOptions[0]
      },
      {
        id: 'mock_2',
        title: '门禁系统设备数量',
        messages: [
          { type: 'system', content: '您好，我是智能助手' },
          { type: 'user', content: '门禁系统有多少设备?' },
          { type: 'system', content: '本项目门禁系统共包含72台门禁控制器和124个读卡器...' }
        ],
        timestamp: formatDateForHistory(new Date(today.getTime() - 2 * 24 * 60 * 60 * 1000)), // 2天前
        sessionId: 'mock-session-2',
        mode: modeOptions[0]
      },
      {
        id: 'mock_3',
        title: '3楼办公区电表数据查询',
        messages: [
          { type: 'system', content: '您好，我是数据查询助手' },
          { type: 'user', content: '3楼办公区电表实时数据?' },
          { type: 'system', content: '3楼办公区电表当前读数为...' }
        ],
        timestamp: formatDateForHistory(new Date(today.getTime() - 3 * 24 * 60 * 60 * 1000)), // 3天前
        sessionId: 'mock-session-3',
        mode: modeOptions[1] // v2模式
      },
      {
        id: 'mock_4',
        title: '当前工单处理情况分析',
        messages: [
          { type: 'system', content: '您好，我是数据查询助手' },
          { type: 'user', content: '当前工单处理情况?' },
          { type: 'system', content: '目前系统中有23个待处理工单，12个处理中工单，8个已完成工单...' }
        ],
        timestamp: formatDateForHistory(new Date(today.getTime() - 10 * 24 * 60 * 60 * 1000)), // 10天前
        sessionId: 'mock-session-4',
        mode: modeOptions[1] // v2模式
      },
      {
        id: 'mock_5',
        title: '智能照明系统控制方式',
        messages: [
          { type: 'system', content: '您好，我是智能助手' },
          { type: 'user', content: '智能照明系统如何控制?' },
          { type: 'system', content: '智能照明系统可通过中控面板、移动APP或语音助手进行控制...' }
        ],
        timestamp: formatDateForHistory(new Date(today.getTime() - 25 * 24 * 60 * 60 * 1000)), // 25天前
        sessionId: 'mock-session-5',
        mode: modeOptions[0]
      }
    ];

    chatHistory.value = mockData;

    // 保存到localStorage
    try {
      localStorage.setItem('chatHistory', JSON.stringify(chatHistory.value));
    } catch (e) {
      console.error('保存历史记录到localStorage失败:', e);
    }
  }
};

// 日期格式化工具函数
const formatDateForHistory = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 修改onMounted钩子，在加载后同时添加模拟数据
onMounted(async () => {
  // 从localStorage读取历史记录
  const savedHistory = localStorage.getItem('chatHistory');
  if (savedHistory) {
    try {
      const parsedHistory = JSON.parse(savedHistory);

      // 验证和修复历史记录数据
      const validatedHistory = parsedHistory.map(item => {
        // 确保基本属性存在
        const validItem = {
          ...item,
          id: item.id || `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: item.timestamp || generateTimestamp(),
          title: item.title || '未命名对话'
        };

        // 如果没有保存模式或模式格式旧，设置为默认模式
        if (!validItem.mode || typeof validItem.mode == 'string') {
          validItem.mode = modeOptions[0];
        }

        return validItem;
      });

      chatHistory.value = validatedHistory;

      // 如果解析出来但是数组为空，不用保存
      if (validatedHistory.length === 0) {
        localStorage.removeItem('chatHistory');
      }
      // 如果历史记录格式有更新，重新保存正确的格式
      else if (JSON.stringify(validatedHistory) !== savedHistory) {
        localStorage.setItem('chatHistory', JSON.stringify(validatedHistory));
      }
    } catch (e) {
      console.error('解析历史记录失败', e);
      // 清除可能损坏的历史记录
      localStorage.removeItem('chatHistory');
      chatHistory.value = [];
    }
  }

  // 添加模拟数据
  addMockHistoryData();

  // 初始检测设备类型和方向
  checkDeviceAndOrientation();

  // 添加窗口大小变化监听
  window.addEventListener('resize', checkDeviceAndOrientation);

  // 创建初始会话
  await createSession();

  scrollToBottom();
});

onUnmounted(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', checkDeviceAndOrientation);

  // 清除所有定时器
  if (toast.value.timeout) {
    clearTimeout(toast.value.timeout);
  }
});

// 添加菜单弹窗
const historyMenuOpen = ref(false);
const selectedHistoryItem = ref(null);
const historyMenuActivator = ref(null);
const newHistoryTitle = ref('');
const historyToRename = ref(null);

// 打开重命名对话框
const renameHistory = (history) => {
  if (!history) return;

  // 设置要重命名的历史记录和新标题
  historyToRename.value = history;
  newHistoryTitle.value = history.title || '';

  // 打开重命名对话框
  showRenameDialog.value = true;

  // 关闭菜单
  closeHistoryMenu();

  // 在下一个tick后聚焦输入框
  nextTick(() => {
    const input = document.querySelector('.rename-dialog-input');
    if (input) {
      input.focus();
    }
  });
};

// 确认重命名
const confirmRename = () => {
  if (!historyToRename.value || !newHistoryTitle.value.trim()) return;

  try {
    // 更新标题
    const index = chatHistory.value.findIndex(h => h.id === historyToRename.value.id);
    if (index !== -1) {
      chatHistory.value[index].title = newHistoryTitle.value.trim();

      // 如果重命名的是当前选中的对话，也更新选中状态
      if (selectedHistoryIndex.value === index) {
        selectedHistoryItem.value = chatHistory.value[index];
      }
    }

    // 保存更改到localStorage
    localStorage.setItem('chatHistory', JSON.stringify(chatHistory.value));

    // 关闭对话框
    showRenameDialog.value = false;
    historyToRename.value = null;
    newHistoryTitle.value = '';

    // 显示成功提示
    showToast('对话已重命名');
  } catch (e) {
    console.error('重命名失败:', e);
    showToast('重命名失败，请重试', 'error');
  }
};

// 删除历史记录
const deleteHistory = (history) => {
  historyToDelete.value = history;
  showDeleteConfirmDialog.value = true;
};

// 添加确认删除的方法
const confirmDelete = async () => {
  if (!historyToDelete.value) return;

  try {
    // 找到索引
    const index = chatHistory.value.findIndex(h => h.id == historyToDelete.value.id);
    if (index == -1) return;

    // 检查删除的是否是当前正在查看的历史记录
    const isViewingThisHistory = selectedHistoryIndex.value === index;

    console.log('删除历史记录 - 删除的是否是当前查看的记录:', isViewingThisHistory);

    // 删除项目
    chatHistory.value.splice(index, 1);

    // 如果删除的是当前选中的历史记录，清除选中状态
    if (selectedHistoryIndex.value == index) {
      selectedHistoryIndex.value = -1;
    }
    // 如果删除的不是当前选中的历史记录，但删除的索引在当前选中索引之前，需要调整当前选中索引
    else if (selectedHistoryIndex.value > index) {
      selectedHistoryIndex.value = selectedHistoryIndex.value - 1;
      console.log('删除的记录在当前选中记录之前，调整选中索引为:', selectedHistoryIndex.value);
    }

    // 保存到localStorage
    localStorage.setItem('chatHistory', JSON.stringify(chatHistory.value));

    // 如果删除的是当前正在查看的历史记录，需要清空当前内容并开启新对话
    if (isViewingThisHistory) {
      console.log('删除的是当前正在查看的历史记录，重新清空当前内容并开启新对话');
      try {
        // 调用创建新会话方法，这会清空当前内容并创建新会话
        await createNewSession();
        console.log('已成功清空当前内容并开启新对话');
      } catch (error) {
        console.error('开启新对话失败:', error);
        // 即使开启新对话失败，也要确保清空当前内容
        messages.value = [];
        selectedHistoryIndex.value = -1;
        console.log('已手动清空当前内容');
      }
    }

    // 显示提示
    showToast('历史记录已删除');

    // 关闭对话框
    showDeleteConfirmDialog.value = false;
    historyToDelete.value = null;
  } catch (e) {
    console.error('删除历史记录失败:', e);
    showToast('删除失败', 'error');

    // 确保对话框关闭
    showDeleteConfirmDialog.value = false;
    historyToDelete.value = null;
  }
};

// 添加菜单位置状态
const menuPosition = ref({ x: 0, y: 0 });

// 修改打开历史记录菜单的方法
const openHistoryMenu = (event, history) => {
  event.stopPropagation();
  // 设置选中的历史记录项
  selectedHistoryItem.value = history;
};

// 处理点击外部关闭菜单
const handleClickOutside = (event) => {
  const menu = document.querySelector('.v-menu__content');
  const button = event.target.closest('.history-menu-btn');

  // 如果点击的是按钮本身，不做处理（让openHistoryMenu处理）
  if (button) return;

  // 如果点击的不是菜单内部元素，则关闭菜单
  if (menu && !menu.contains(event.target)) {
    closeHistoryMenu();
  }
};

// 关闭历史记录菜单
const closeHistoryMenu = () => {
  historyMenuOpen.value = false;
  selectedHistoryItem.value = null;
  document.removeEventListener('click', handleClickOutside);
};

// 添加关闭菜单的监听
watch(historyMenuOpen, (newValue) => {
  if (!newValue) {
    // 菜单关闭时重置状态
    selectedHistoryItem.value = null;
    menuPosition.value = { x: 0, y: 0 };
  }
});

// 重命名对话框
const showRenameDialog = ref(false);

// 添加新的变量来控制历史记录组的展开状态
const groupExpanded = ref({
  recent: true,
  older: true,
  oldest: true
});

// 添加新的方法来切换历史记录组的展开状态
const toggleGroup = (group) => {
  groupExpanded.value[group] = !groupExpanded.value[group];
};

// 添加侧边栏折叠状态
const isSidebarCollapsed = ref(false);

// 切换侧边栏状态
const toggleSidebar = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value;
};

// 在 script setup 部分添加新的变量和方法
const showDeleteConfirmDialog = ref(false);
const historyToDelete = ref(null);

// 在script setup部分添加处理方法
// 添加下拉菜单命令处理方法
const handleCommand = (command) => {
  const { type, history } = command;
  if (type === 'rename') {
    renameHistory(history);
  } else if (type === 'delete') {
    deleteHistory(history);
  }
};

// 添加监听chatHistory变化的watch函数
watch(chatHistory, (newValue) => {
  console.log('历史记录已更新:', newValue);
}, { deep: true });

// 从API获取历史记录
const fetchHistoryFromAPI = async () => {
  try {
    console.log('从API获取历史记录');
    const response = await getMessage();
    
    if (response.code == 0 && response.data) {
      console.log('API返回的历史记录:', response.data);
      
      // 直接使用API返回的分组数据
      // 更新历史记录，保持分组结构
      chatHistory.value = {
        recent7Days: (response.data.recent7Days || []).map(enrichHistoryItem),
        recent30Days: (response.data.recent30Days || []).map(enrichHistoryItem),
        before30Days: (response.data.before30Days || []).map(enrichHistoryItem)
      };
      
      // 计算历史记录总数
      const totalCount = 
        chatHistory.value.recent7Days.length + 
        chatHistory.value.recent30Days.length + 
        chatHistory.value.before30Days.length;
      
      console.log('历史记录已更新，共', totalCount, '条，按分组格式存储');
      
      // 初始化分组展开状态
      if (!groupExpanded.value.hasOwnProperty('oldest')) {
        groupExpanded.value.oldest = true;
      }
    } else {
      console.error('暂无历史记录:', response?.msg || '未知错误');
      // showToast('获取历史记录失败', 'error');
    }
  } catch (error) {
    console.error('获取历史记录异常:', error);
    // showToast('获取历史记录失败', 'error');
  }
};

// 处理历史记录项，添加必要的属性
const enrichHistoryItem = (item) => {
  console.log('处理历史记录项，ID:', item.id, '会话ID:', item.conversationId);
  
  // 尝试解析聊天历史
  let chatHistoryData = [];
  try {
    if (item.chatHistory) {
      if (typeof item.chatHistory === 'string') {
        chatHistoryData = JSON.parse(item.chatHistory);
      } else {
        chatHistoryData = item.chatHistory;
      }
    }
  } catch (e) {
    console.error('解析聊天历史失败:', e);
    chatHistoryData = [];
  }
  
  // 设置id属性，确保与现有代码兼容
  return {
    ...item,
    id: `api_${item.id}`,
    title: item.conversationTitle || '未命名对话',
    sessionId: item.conversationId || item.id,
    apiData: item, // 保存原始API数据
    messages: chatHistoryData.map(msg => ({
      type: msg.role === 'user' ? 'user' : 'system',
      content: msg.content || '',
      typingCompleted: true,
      showReferences: false
    }))
  };
};

// 移除不再需要的计算属性
// const recentHistory = computed(() => {...});
// const olderHistory = computed(() => {...});
</script>

<style scoped>
.history-actions {
  display: none;
}

.history-menu-btn {
  display: none;
}

/* 删除以下样式 */
:deep(.v-menu) {
  display: none;
}

:deep(.v-overlay__content) {
  display: none;
}

.main-content {
  min-height: calc(100vh - 150px);
  overflow-y: auto;
  padding: 20px;
  gap: 24px;
  background-color: #ffffff;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

@media (min-width: 600px) {
  .main-content {
    padding: 32px;
    gap: 28px;
  }
}

/* 历史记录侧边栏样式 */
.history-sidebar {
  width: 320px;
  min-width: 320px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);
  max-height: 800px;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  transform-origin: left center;
}

.history-sidebar.collapsed {
  width: 0;
  min-width: 0;
  margin: 0;
  padding: 0;
  opacity: 0;
  transform: translateX(-40px) scale(0.95);
}

.history-header {
  padding: 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  background-color: rgba(255, 255, 255, 0.98);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(12px);
  position: relative;
  z-index: 2;
}

.history-item {
  padding: 10px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-left: 3px solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  margin: 4px 8px;
  border-radius: 12px;
  position: relative;
  overflow: visible;
  background-color: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
}

.history-item::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 12px;
  background: linear-gradient(120deg, rgba(26, 115, 232, 0.08), rgba(26, 115, 232, 0));
  opacity: 0;
  transition: all 0.3s ease;
}

.history-item:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.history-item:hover::before {
  opacity: 1;
}

.selected-history {
  background-color: rgba(26, 115, 232, 0.04) !important;
  border-left: 3px solid #1a73e8 !important;
  box-shadow: 0 4px 12px rgba(26, 115, 232, 0.1) !important;
}

.history-item-main {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  gap: 12px;
  cursor: pointer;
  padding: 4px 0;
}

.history-avatar {
  transition: transform 0.3s ease;
}

.history-item:hover .history-avatar {
  transform: scale(1.05);
}

.history-text {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.history-title {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  transition: color 0.3s ease;
}

.history-item:hover .history-title {
  color: #1a73e8;
}

.history-date {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #64748b;
  gap: 4px;
  opacity: 0.9;
}

.history-menu-btn {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  display: flex !important;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: all 0.3s ease;
  background-color: transparent;
  border: none;
  color: #666;
  margin-left: 8px;
  flex-shrink: 0;
}

.history-item:hover .history-menu-btn {
  opacity: 1;
}

.history-menu-btn:hover {
  background-color: rgba(0, 0, 0, 0.06);
  color: #1a73e8;
}

/* 历史记录组标题样式优化 */
.history-group-title {
  padding: 12px 20px;
  font-size: 13px;
  color: #64748b;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
  user-select: none;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 4px;
}

.history-group-title:hover {
  color: #1a73e8;
}

.history-count {
  font-size: 12px;
  color: #94a3b8;
  font-weight: 500;
  background-color: rgba(148, 163, 184, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.history-group-title:hover .history-count {
  background-color: rgba(26, 115, 232, 0.1);
  color: #1a73e8;
}

.collapse-icon {
  transition: transform 0.3s ease;
}

.collapse-icon.collapsed {
  transform: rotate(-90deg);
}

/* 历史记录组内容区域样式 */
.history-items {
  padding: 4px 0;
  transition: all 0.3s ease;
}

/* 菜单列表样式优化 */
:deep(.history-menu-list) {
  padding: 6px !important;
  border-radius: 12px;
  background-color: white !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12) !important;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

:deep(.history-menu-list-item) {
  border-radius: 8px;
  margin: 2px 0;
  min-height: 36px !important;
  transition: all 0.3s ease;
}

:deep(.history-menu-list-item:hover) {
  background-color: rgba(26, 115, 232, 0.08) !important;
  transform: translateX(2px);
}

:deep(.v-list-item-title) {
  font-size: 13px !important;
  font-weight: 500;
}

/* 新会话按钮样式优化 */
.new-chat-button {
  border-radius: 12px;
  height: 42px;
  font-weight: 600;
  letter-spacing: 0.3px;
  text-transform: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(26, 115, 232, 0.2);
}

.new-chat-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(26, 115, 232, 0.25);
}

.new-chat-button:active {
  transform: translateY(1px);
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);
  max-height: 800px;
  border-radius: 24px;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.98);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  flex: 1;
  border: 1px solid rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(20px);
  transform-origin: center;
}

.chat-container.expanded {
  margin-left: 0;
  transform: scale(1);
}

.message {
  margin-bottom: 32px;
  display: flex;
  align-items: flex-start;
  animation: messageSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.message-content {
  max-width: 85%;
  padding: 18px 24px;
  border-radius: 20px;
  background-color: #f8f9fc;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  color: #333;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  line-height: 1.7;
  font-size: 15px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.message-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(120deg, rgba(255, 255, 255, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.message-content:hover::before {
  opacity: 1;
}

.message.user .message-content {
  background-color: #1a73e8;
  color: white;
  box-shadow: 0 4px 20px rgba(26, 115, 232, 0.15);
  border: none;
  transform-origin: right center;
}

.message.user .message-content:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(26, 115, 232, 0.2);
}

.send-button {
  background-color: #1a73e8;
  color: white;
  border: none;
  border-radius: 14px;
  padding: 0 28px;
  height: 46px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  font-size: 15px;
  min-width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.send-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(120deg, rgba(255, 255, 255, 0.2), transparent);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.send-button:hover::before {
  opacity: 1;
}

.send-button:hover {
  background-color: #1557b0;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(26, 115, 232, 0.25);
}

.send-button:active {
  transform: translateY(1px);
  box-shadow: 0 4px 12px rgba(26, 115, 232, 0.2);
}

.send-button.processing {
  background-color: #dc3545;
  animation: pulseButton 2s infinite;
}

@keyframes pulseButton {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.98);
  }

  100% {
    transform: scale(1);
  }
}

.input-field :deep(.v-field) {
  border-radius: 14px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: #f8f9fc;
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
}

.input-field :deep(.v-field:hover) {
  background-color: #f0f2f5;
  border-color: rgba(26, 115, 232, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
}

.input-field :deep(.v-field:focus-within) {
  background-color: white;
  box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.15);
  border-color: #1a73e8;
  transform: translateY(-2px);
}

.thinking-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  gap: 10px;
}

.thinking-indicator .dot {
  width: 8px;
  height: 8px;
  background-color: #1a73e8;
  border-radius: 50%;
  animation: dotPulse 1.8s infinite cubic-bezier(0.4, 0, 0.2, 1);
}

.thinking-indicator .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.thinking-indicator .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dotPulse {

  0%,
  100% {
    transform: scale(0.8);
    opacity: 0.4;
  }

  50% {
    transform: scale(1.3);
    opacity: 1;
  }
}

.toast {
  padding: 16px 28px;
  border-radius: 16px;
  min-width: 320px;
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
  animation: toastSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
}

@keyframes toastSlideIn {
  from {
    transform: translateY(40px) scale(0.9);
    opacity: 0;
  }

  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.toast.success {
  background-color: rgba(26, 115, 232, 0.95);
}

.toast.info {
  background-color: rgba(46, 125, 50, 0.95);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  background-color: #ffffff;
  transition: all 0.3s ease;
  scroll-behavior: smooth;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.02);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.12);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}

.message {
  margin-bottom: 28px;
  display: flex;
  align-items: flex-start;
  animation: messageSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-avatar {
  margin-right: 16px;
  flex-shrink: 0;
  transition: transform 0.3s ease;
}

.message:hover .message-avatar {
  transform: scale(1.05);
}

.message.user {
  flex-direction: row-reverse;
}

.message.user .message-avatar {
  margin-right: 0;
  margin-left: 16px;
}

.message-content {
  max-width: 85%;
  padding: 16px 20px;
  border-radius: 18px;
  background-color: #f8f9fc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  color: #333;
  transition: all 0.3s ease;
  line-height: 1.6;
  font-size: 15px;
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.message.user .message-content {
  background-color: #1a73e8;
  color: white;
  box-shadow: 0 2px 8px rgba(26, 115, 232, 0.12);
  border: none;
}

.message.system .message-content {
  background-color: #f8f9fc;
  border-left: 4px solid #e0e6f0;
}

.welcome-message ul {
  margin: 14px 0 14px 22px;
  padding-left: 0;
}

.welcome-message li {
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.welcome-message li:hover {
  color: #1a73e8;
  transform: translateX(5px);
}

.suggested-questions {
  display: flex;
  flex-wrap: wrap;
  margin-top: 18px;
  gap: 10px;
}

.chat-input {
  padding: 20px;
  background-color: white;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.02);
}

.chat-input-container {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 12px;
}

.mode-selector {
  min-width: 120px;
  border-right: 1px solid rgba(0, 0, 0, 0.08);
  padding-right: 12px;
}

.mode-select {
  width: 120px;
}

.mode-select :deep(.v-field) {
  border-radius: 12px;
  transition: all 0.3s ease;
}

.mode-select :deep(.v-field:hover) {
  background-color: rgba(0, 0, 0, 0.02);
}

.mode-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
}

.input-field {
  flex: 1;
  margin-right: 12px;
}

.input-field :deep(.v-field) {
  border-radius: 12px;
  transition: all 0.3s ease;
  background-color: #f8f9fc;
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.input-field :deep(.v-field:hover) {
  background-color: #f0f2f5;
  border-color: rgba(0, 0, 0, 0.12);
}

.input-field :deep(.v-field:focus-within) {
  background-color: white;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.15);
  border-color: #1a73e8;
}

.send-button {
  background-color: #1a73e8;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 0 24px;
  height: 44px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  font-size: 15px;
  min-width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.send-button:hover {
  background-color: #1557b0;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(26, 115, 232, 0.2);
}

.send-button:active {
  transform: translateY(1px);
}

.send-button.processing {
  background-color: #dc3545;
}

.new-chat-button-container {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.new-chat-button {
  border-radius: 12px;
  color: white;
  font-weight: 500;
  height: 44px;
  text-transform: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: #1a73e8 !important;
  box-shadow: 0 4px 12px rgba(26, 115, 232, 0.2);
}

.new-chat-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(26, 115, 232, 0.25);
}

.new-chat-button:active {
  transform: translateY(1px);
}

.mode-indicator {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 12px;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
  gap: 6px;
}

.mode-r1 {
  background-color: rgba(26, 115, 232, 0.08);
  color: #1a73e8;
  border: 1px solid rgba(26, 115, 232, 0.2);
}

.mode-v2 {
  background-color: rgba(46, 125, 50, 0.08);
  color: #2e7d32;
  border: 1px solid rgba(46, 125, 50, 0.2);
}

.toast {
  padding: 16px 24px;
  border-radius: 12px;
  min-width: 300px;
  color: white;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  animation: toastSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

@keyframes toastSlideIn {
  from {
    transform: translateY(30px) scale(0.9);
    opacity: 0;
  }

  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.toast.success {
  background-color: #1a73e8;
}

.toast.info {
  background-color: #2e7d32;
}

.thinking-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  gap: 8px;
}

.thinking-indicator .dot {
  width: 8px;
  height: 8px;
  background-color: #1a73e8;
  border-radius: 50%;
  animation: dotPulse 1.5s infinite cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes dotPulse {

  0%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }

  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.references-container {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
}

.references-title {
  font-size: 13px;
  color: #666;
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
  gap: 6px;
}

.references-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.reference-item {
  font-size: 13px;
  color: #555;
  background-color: #f8f9fc;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.reference-item:hover {
  background-color: #f0f4ff;
  border-color: #1a73e8;
  color: #1a73e8;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(26, 115, 232, 0.1);
}

.reference-item:active {
  transform: translateY(1px);
}

:deep(.code-block) {
  margin: 24px 0;
  border-radius: 16px;
  overflow: hidden;
  background-color: #1e1e1e;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.code-header) {
  background-color: #2d2d2d;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #e0e0e0;
  font-size: 13px;
  border-bottom: 1px solid #404040;
}

:deep(.copy-button) {
  background-color: #404040;
  border: 1px solid #505050;
  color: #e0e0e0;
  padding: 6px 12px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  transition: all 0.3s ease;
}

:deep(.copy-button:hover) {
  background-color: #505050;
  border-color: #606060;
  transform: translateY(-1px);
}

:deep(.copy-button:active) {
  transform: translateY(1px);
}

:deep(.code-content) {
  margin: 0;
  padding: 20px 24px;
  overflow-x: auto;
  color: #e0e0e0;
  font-size: 14px;
  line-height: 1.6;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .history-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 100;
    height: 100vh;
    max-height: none;
    transform: translateX(0);
    width: 280px;
    min-width: 280px;
  }

  .history-sidebar.collapsed {
    transform: translateX(-100%);
  }

  .chat-container {
    margin-left: 0;
  }

  .message-content {
    max-width: 90%;
    padding: 14px 16px;
    font-size: 14px;
  }

  .send-button {
    min-width: 80px;
    padding: 0 16px;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .main-content {
    background-color: #ffffff;
  }

  .chat-container,
  .history-sidebar {
    background-color: #ffffff;
    border-color: rgba(0, 0, 0, 0.08);
  }

  .message-content,
  .input-field :deep(.v-field) {
    background-color: #f8f9fc;
    color: #333;
    border-color: rgba(0, 0, 0, 0.08);
  }

  .history-title,
  .mode-label {
    color: #333;
  }

  .history-date,
  .history-count {
    color: #757575;
  }
}

:deep(.v-menu) {
  position: fixed !important;
}

:deep(.v-overlay__content) {
  position: fixed !important;
  background-color: white !important;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  z-index: 9999;
}

:deep(.v-list) {
  padding: 8px 0;
  background-color: white !important;
}

:deep(.v-list-item) {
  min-height: 40px;
  padding: 8px 16px;
  cursor: pointer;
}

:deep(.v-list-item:hover) {
  background-color: rgba(0, 0, 0, 0.04) !important;
}

.history-groups {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  scrollbar-width: thin;
}

.history-groups::-webkit-scrollbar {
  width: 6px;
}

.history-groups::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.02);
  border-radius: 3px;
}

.history-groups::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.12);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.history-groups::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}

.history-group {
  margin-bottom: 18px;
  border-bottom: 1px solid transparent;
}

.history-group:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  padding-bottom: 8px;
}

.history-group-title {
  padding: 12px 20px;
  font-size: 13.5px;
  color: #444;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: space-between;
  user-select: none;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 6px;
}

.history-group-title:hover {
  background-color: rgba(0, 0, 0, 0.03);
  color: #1a73e8;
}

.history-count {
  margin-left: 8px;
  color: #888;
  font-size: 11.5px;
  font-weight: normal;
  opacity: 0.8;
}

.history-item-main {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  gap: 14px;
  cursor: pointer;
}

.history-text {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 修改菜单按钮样式 */
.history-menu-btn {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: all 0.3s ease;
  background-color: transparent;
  border: none;
  color: #666;
  margin-left: 8px;
  /* 与主内容保持一定间距 */
  flex-shrink: 0;
}

.history-item:hover .history-menu-btn {
  opacity: 1;
}

.history-menu-btn:hover {
  background-color: rgba(0, 0, 0, 0.06);
  color: #1a73e8;
}

/* 添加菜单样式 */
.history-menu {
  position: absolute;
  left: -8px;
  top: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 160px;
  padding: 8px 0;
}

.history-menu-item {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #333;
  font-size: 14px;
}

.history-menu-item:hover {
  background-color: rgba(26, 115, 232, 0.08);
  color: #1a73e8;
}

/* 添加菜单列表样式 */
:deep(.history-menu-list) {
  padding: 4px !important;
  border-radius: 8px;
  background-color: white !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

:deep(.history-menu-list-item) {
  border-radius: 6px;
  margin: 2px 0;
  min-height: 40px !important;
}

:deep(.history-menu-list-item:hover) {
  background-color: rgba(26, 115, 232, 0.08) !important;
}

:deep(.v-list-item-title) {
  font-size: 14px !important;
  font-weight: 500;
}

/* 确保菜单显示在最上层 */
:deep(.v-menu) {
  z-index: 1000 !important;
}

:deep(.v-overlay__content) {
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

/* 菜单样式优化 */
:deep(.v-menu) {
  display: block !important;
}

:deep(.v-menu__content) {
  position: fixed !important;
  background-color: white !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  overflow: hidden !important;
  z-index: 1000 !important;
}

:deep(.history-menu-list) {
  padding: 6px !important;
  background: transparent !important;
}

:deep(.history-menu-list-item) {
  border-radius: 8px !important;
  margin: 2px 0 !important;
  min-height: 40px !important;
  transition: all 0.3s ease !important;
}

:deep(.history-menu-list-item:hover) {
  background-color: rgba(26, 115, 232, 0.08) !important;
  transform: translateX(2px);
}

:deep(.v-list-item-title) {
  font-size: 14px !important;
  font-weight: 500 !important;
}

/* 确保菜单按钮可见 */
.history-menu-btn {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex !important;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: all 0.3s ease;
  background-color: transparent;
  border: none;
  color: #64748b;
  margin-left: 8px;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.history-item:hover .history-menu-btn {
  opacity: 1;
}

.history-menu-btn:hover {
  background-color: rgba(26, 115, 232, 0.08);
  color: #1a73e8;
  transform: scale(1.05);
}

/* 重命名对话框样式 - 使用更具体的选择器 */
:deep(.rename-dialog.v-dialog) {
  position: fixed;
  z-index: 9999;
  height: auto !important;
}

:deep(.rename-dialog.v-dialog > .v-card) {
  position: relative;
  z-index: 10000;
  max-width: 100%;
  margin: 24px;
  display: block;
  height: auto !important;
  width: 400px;
}

:deep(.rename-dialog .v-overlay__content) {
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto !important;
  height: auto !important;
}

:deep(.rename-dialog .v-card) {
  display: flex;
  flex-direction: column;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
  max-height: fit-content !important;
}

:deep(.rename-dialog .v-card-title) {
  padding: 16px 24px;
  font-size: 18px;
  font-weight: 500;
  line-height: 1.5;
}

:deep(.rename-dialog .v-card-text) {
  padding: 16px 24px;
  flex-grow: 0;
}

:deep(.rename-dialog .v-card-actions) {
  padding: 16px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.rename-dialog .v-overlay__content) {
  display: block !important;
  position: relative !important;
}

:deep(.rename-dialog.v-dialog--active) {
  display: block !important;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 10000 !important;
  height: auto !important;
}

:deep(.rename-dialog .rename-dialog-input) {
  margin: 0;
}

:deep(.rename-dialog .rename-dialog-input .v-field) {
  border-radius: 8px;
  transition: all 0.3s ease;
  background-color: #f8f9fc;
}

:deep(.rename-dialog .rename-dialog-input .v-field:hover) {
  background-color: #f0f2f5;
  border-color: rgba(26, 115, 232, 0.3);
}

:deep(.rename-dialog .rename-dialog-input .v-field:focus-within) {
  background-color: white;
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

:deep(.rename-dialog .v-btn) {
  text-transform: none;
  font-weight: 500;
  letter-spacing: 0.3px;
  border-radius: 8px;
  height: 36px;
  min-width: 80px;
}

:deep(.rename-dialog .v-btn--variant-text) {
  opacity: 0.8;
}

:deep(.rename-dialog .v-btn--variant-text:hover) {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.05);
}

:deep(.rename-dialog .v-btn.v-btn--density-default) {
  height: 36px;
}

:deep(.rename-dialog .v-btn.v-btn--variant-elevated) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.rename-dialog .v-btn.v-btn--variant-elevated:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

/* 重命名对话框样式 */
.rename-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.rename-dialog.v-dialog) {
  position: relative;
  width: 400px !important;
  margin: auto;
  height: auto !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.rename-dialog .v-card) {
  width: 100%;
  max-width: 400px;
  margin: 24px;
  border-radius: 16px;
  background: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
}

:deep(.rename-dialog .v-card-title) {
  padding: 16px 24px;
  font-size: 18px;
  font-weight: 500;
  line-height: 1.5;
  background-color: var(--v-primary-base);
  color: white;
}

:deep(.rename-dialog .v-card-text) {
  padding: 20px 24px;
}

:deep(.rename-dialog .v-card-actions) {
  padding: 16px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.rename-dialog .rename-dialog-input) {
  width: 100%;
}

:deep(.rename-dialog .rename-dialog-input .v-field) {
  border-radius: 8px;
  transition: all 0.3s ease;
  background-color: #f8f9fc;
}

:deep(.rename-dialog .rename-dialog-input .v-field:hover) {
  background-color: #f0f2f5;
  border-color: rgba(26, 115, 232, 0.3);
}

:deep(.rename-dialog .rename-dialog-input .v-field:focus-within) {
  background-color: white;
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

:deep(.rename-dialog .v-btn) {
  text-transform: none;
  font-weight: 500;
  letter-spacing: 0.3px;
  border-radius: 8px;
  height: 36px;
  min-width: 80px;
}

:deep(.rename-dialog .v-btn--variant-text) {
  opacity: 0.8;
}

:deep(.rename-dialog .v-btn--variant-text:hover) {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.05);
}

/* 确保对话框显示在正确的位置 */
:deep(.v-overlay__content) {
  position: fixed;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
  width: auto !important;
  max-width: calc(100vw - 48px) !important;
}

:deep(.v-overlay__scrim) {
  background: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(4px);
}

/* 重命名对话框样式 */
:deep(.rename-dialog-card) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.rename-dialog-card .v-card-title) {
  font-size: 16px;
  padding: 16px;
  line-height: 1.5;
}

:deep(.rename-dialog-card .v-card-text) {
  padding-bottom: 0;
}

:deep(.rename-dialog-card .v-card-actions) {
  padding: 16px;
}

:deep(.rename-dialog-card .v-btn) {
  min-width: 80px;
  text-transform: none;
}

:deep(.v-overlay__content) {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

/* 重命名对话框样式 */
.rename-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

:deep(.rename-dialog .v-card) {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

:deep(.rename-dialog .v-card-title) {
  font-size: 16px;
  padding: 16px;
  line-height: 1.5;
}

:deep(.rename-dialog .v-text-field) {
  margin-top: 8px;
}

:deep(.rename-dialog .v-btn) {
  min-width: 80px;
  text-transform: none;
}

:deep(.v-overlay__scrim) {
  backdrop-filter: blur(4px);
}

:deep(.v-overlay__content) {
  position: fixed;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 删除对话框样式 */
:deep(.delete-dialog) {
  height: auto !important;
}

:deep(.delete-dialog-card) {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
}

:deep(.delete-dialog-card .v-card-title) {
  font-size: 16px;
  padding: 16px 24px;
  line-height: 1.5;
}

:deep(.delete-dialog-card .v-card-text) {
  padding: 20px 24px;
  font-size: 14px;
  color: #333;
}

:deep(.delete-dialog-card .v-card-actions) {
  padding: 16px 24px 24px;
}

:deep(.delete-dialog-card .v-btn) {
  min-width: 80px;
  text-transform: none;
  font-weight: 500;
}

:deep(.delete-dialog .v-overlay__content) {
  width: auto !important;
  height: auto !important;
  max-height: none !important;
}
</style>