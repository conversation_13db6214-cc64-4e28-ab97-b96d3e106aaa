import router from './router'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'
import { isHttp } from '@/utils/validate'
import { isRelogin } from '@/utils/request'
import useUserStore from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'
import auth from '@/plugins/auth'

// 开发环境设置：设置为true表示跳过权限检查（仅限开发使用）
const DEV_MODE_SKIP_PERMISSION_CHECK = false;

NProgress.configure({ showSpinner: false });

const whiteList = ['/login', '/register'];

// 过滤掉空name的路由
const filterEmptyNameRoutes = (routes) => {
  if (!routes) return [];

  return routes.filter(route => {
    // 过滤空name或只包含空格的路由
    if (!route.name || route.name === '' || route.name.trim() === '') {
      console.warn(`已过滤空name或只有空格的路由: ${route.path}`);
      return false;
    }

    // 递归处理子路由
    if (route.children && route.children.length) {
      route.children = filterEmptyNameRoutes(route.children);
    }

    return true;
  });
};

router.beforeEach((to, from, next) => {
  NProgress.start()
  if (getToken()) {
    to.meta.title && useSettingsStore().setTitle(to.meta.title)
    /* has token*/
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      if (useUserStore().roles.length === 0) {
        isRelogin.show = true
        // 判断当前用户是否已拉取完user_info信息
        useUserStore().getInfo().then((res) => {
          console.log('检查res', res);
          let apiKey = res.apiKey;
          let apiUrl = res.apiUrl
          let publicKey = res.publicKey;
          let publicBucketName = res.publicBucketName;
          // 存储用户权限
          if (res.permissions) {
            useUserStore().setPermissions(res.permissions);
          }
          // 存储API密钥和API地址
          if (apiKey) {
            useUserStore().setApiKey(apiKey);
          }
          if (apiUrl) {
            useUserStore().setApiUrl(apiUrl);
          }
          // 存储publicKey
          if (publicKey) {
            useUserStore().setPublicKey(publicKey);
          }
          // 存储publicBucketName
          if (publicBucketName) {
            useUserStore().setPublicBucketName(publicBucketName);
          }
          isRelogin.show = false
          usePermissionStore().generateRoutes().then(accessRoutes => {
            console.log('检查accessRoutes', accessRoutes);
            // 根据roles权限生成可访问的路由表
            try {
              // 过滤掉空name的路由
              const filteredRoutes = filterEmptyNameRoutes(accessRoutes);
              console.log('过滤后的路由', filteredRoutes);

              // 动态添加可访问路由表
              filteredRoutes.forEach(route => {
                if (!isHttp(route.path)) {
                  router.addRoute(route);
                }
              });
              next({ ...to, replace: true });
            } catch (error) {
              console.error('Route generation error:', error);
              ElMessage.error(`路由生成错误: ${error.message}`);
              useUserStore().logOut().then(() => {
                next({ path: '/' });
              });
            }
          });
        }).catch(err => {
          useUserStore().logOut().then(() => {
            ElMessage.error(err)
            next({ path: '/' })
          })
        })
      } else {
        // 检查用户是否有权限访问当前路由
        const hasPermission = checkRoutePermission(to);
        if (hasPermission) {
          next();
        } else {
          ElMessage.error('没有权限访问该页面');
          next({ path: '/401' });
        }
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

// 检查用户是否有权限访问路由
function checkRoutePermission(route) {
  const userStore = useUserStore();

  // 开发环境跳过权限检查
  if (DEV_MODE_SKIP_PERMISSION_CHECK) {
    return true;
  }

  // 白名单路由，无需检查权限
  if (whiteList.includes(route.path)) {
    return true;
  }

  // 错误页面始终允许访问
  if (route.path === '/401' || route.path === '/404') {
    return true;
  }

  // 如果是系统管理员，拥有所有权限
  if (userStore.roles.includes('admin')) {
    return true;
  }

  // 如果用户拥有超级管理员权限 *:*:*
  if (userStore.permissions.includes('*:*:*')) {
    return true;
  }

  // 获取当前路由所需权限
  const requiredPermissions = route.meta?.permissions || [];

  // 如果路由没有设置权限要求，检查navItems中是否定义了该路由的权限
  if (!requiredPermissions.length) {
    try {
      // 动态导入TheHeader.vue中定义的navItems
      const navItemPermission = getNavItemPermission(route.path);
      if (navItemPermission) {
        if (Array.isArray(navItemPermission)) {
          return auth.hasPermiOr(navItemPermission);
        }
        return auth.hasPermi(navItemPermission);
      }
    } catch (error) {
      console.error('检查navItems权限出错:', error);
    }

    return true; // 如果没有设置权限要求，默认放行
  }

  // 检查用户是否拥有路由所需的任一权限
  return auth.hasPermiOr(requiredPermissions);
}

// 从TheHeader.vue的navItems中获取权限
function getNavItemPermission(path) {
  // 预定义的权限映射关系，与TheHeader.vue中的navItems保持一致
  const pathPermissionMap = {
    '/index': ['ai:knowledge:cockpit'],
    '/chat': ['ai:knowledge:answer'],
    '/documents': 'ai:knowledge:file',
    '/system/user': 'system:user:list',
    '/system/role': 'system:role:list',
    '/system/dept': 'system:dept:list',
    '/system/menu': 'system:menu:list'
  };

  return pathPermissionMap[path];
}

router.afterEach(() => {
  NProgress.done()
})
