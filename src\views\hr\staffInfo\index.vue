<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="请输入护理单元名称"
            clearable
            prefix-icon="Search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deptOptions"
            :props="{ label: 'label', children: 'children' }"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="deptTreeRef"
            node-key="id"
            highlight-current
            default-expand-all
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <el-col :span="20" :xs="24">
        <el-form
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          v-show="showSearch"
          label-width="68px"
        >
          <!-- <el-form-item label="所属护理单元id" prop="deptId">
                <el-input
                  v-model="queryParams.deptId"
                  placeholder="请输入所属护理单元id"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="所属护理单元名称" prop="deptName">
                <el-input
                  v-model="queryParams.deptName"
                  placeholder="请输入所属护理单元名称"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item> -->
          <el-form-item label="用户名称" prop="nickName">
            <el-input
              v-model="queryParams.nickName"
              placeholder="请输入用户名称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <!-- <el-form-item label="用户工号" prop="userCode">
                <el-input
                  v-model="queryParams.userCode"
                  placeholder="请输入用户工号"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item> -->
          <el-form-item label="联系电话" prop="contactNumber">
            <el-input
              v-model="queryParams.contactNumber"
              placeholder="请输入联系电话"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="Plus"
              @click="handleAdd"
              v-hasPermi="['hr:staffInfo:add']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="Edit"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['hr:staffInfo:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['hr:staffInfo:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="Download"
              @click="handleExport"
              v-hasPermi="['hr:staffInfo:export']"
              >导出</el-button
            >
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="staffInfoList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="用户名称" align="center" prop="nickName" />
          <el-table-column label="护理单元" align="center" prop="deptName" />
          <el-table-column label="用户工号" align="center" prop="userCode" />
          <el-table-column label="护理能级" align="center" prop="level">
            <template #default="scope">
              <dict-tag :options="hr_staff_level" :value="scope.row.level" />
            </template>
          </el-table-column>
          <el-table-column label="科室名称" align="center" prop="affiliatedDeptName" />
          <el-table-column label="病区名称" align="center" prop="wardName" v-if="false" />
          <el-table-column
            label="联系电话"
            align="center"
            prop="contactNumber"
            width="110"
          />
          <el-table-column label="工作照" align="center" prop="workPhoto" width="100">
            <template #default="scope">
              <image-preview :src="scope.row.workPhoto" :width="50" :height="50" />
            </template>
          </el-table-column>
          <!-- <el-table-column label="编制科室" align="center" prop="compileDeptId" /> -->
          <!-- <el-table-column label="编制科室名称" align="center" prop="compileDeptName" /> -->
          <!-- <el-table-column label="所属科室" align="center" prop="affiliatedDeptId" /> -->
          <!-- <el-table-column label="所属病区" align="center" prop="wardId" /> -->
          <!-- <el-table-column label="所属护理组" align="center" prop="nurseGroupId" /> -->
          <!-- <el-table-column label="护理组内排序" align="center" prop="nurseGroupSort" />
              <el-table-column label="医生临床科室" align="center" prop="clinicalDeptId" />
              <el-table-column label="医生临床科室名称" align="center" prop="clinicalDeptName" />
              <el-table-column label="专科角色" align="center" prop="specializedRole" />
              <el-table-column label="病区角色" align="center" prop="wardRole" />
              <el-table-column label="职业类别" align="center" prop="professionCategory" />
              <el-table-column label="员工类型" align="center" prop="employeeType" />
              <el-table-column label="员工来源" align="center" prop="employeeSource" />
              <el-table-column label="员工来源单位" align="center" prop="employeeSourceUnit" />
              <el-table-column label="职称" align="center" prop="jobTitle" /> -->
          <!-- <el-table-column label="聘任职称" align="center" prop="appointedJob" /> -->
          <!-- <el-table-column label="拼音简称" align="center" prop="pinyinShort" />
              <el-table-column label="拼音全拼" align="center" prop="pinyin" />
              <el-table-column label="民族" align="center" prop="ethnicity">
                <template #default="scope">
                  <dict-tag :options="hr_staff_nation" :value="scope.row.ethnicity"/>
                </template>
              </el-table-column>
              <el-table-column label="政治面貌" align="center" prop="politicalStatus">
                <template #default="scope">
                  <dict-tag :options="hr_staff_politicaloutlook" :value="scope.row.politicalStatus"/>
                </template>
              </el-table-column> -->
          <!-- <el-table-column label="生活照" align="center" prop="homePhoto" width="100">
                <template #default="scope">
                  <image-preview :src="scope.row.homePhoto" :width="50" :height="50"/>
                </template>
              </el-table-column> -->
          <!-- <el-table-column label="身高" align="center" prop="height" />
              <el-table-column label="体重" align="center" prop="weight" />
              <el-table-column label="鞋码" align="center" prop="shoeSize" />
              <el-table-column label="出生日期" align="center" prop="birthDate" width="180">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.birthDate, '{y}-{m}-{d}') }}</span>
                </template>
              </el-table-column> -->
          <!-- <el-table-column label="婚姻状态" align="center" prop="maritalStatus">
                <template #default="scope">
                  <dict-tag :options="hr_staff_marital" :value="scope.row.maritalStatus"/>
                </template>
              </el-table-column> -->
          <!-- <el-table-column label="健康状况" align="center" prop="healthStatus">
                <template #default="scope">
                  <dict-tag :options="hr_staff_health_status" :value="scope.row.healthStatus"/>
                </template>
              </el-table-column> -->
          <!-- <el-table-column label="身份证号" align="center" prop="idCardNumber" /> -->
          <!-- <el-table-column label="医院长号码" align="center" prop="longNumber" />
              <el-table-column label="医院短号码" align="center" prop="shortNumber" />
              <el-table-column label="医院邮箱" align="center" prop="hospitalEmail" /> -->
          <!-- <el-table-column label="家庭地址" align="center" prop="homeAddress" />
              <el-table-column label="家庭邮编" align="center" prop="homeZipCode" />
              <el-table-column label="家庭电话" align="center" prop="homePhone" />
              <el-table-column label="户口性质" align="center" prop="houseType" />
              <el-table-column label="户籍" align="center" prop="houseRegistration" />
              <el-table-column label="籍贯" align="center" prop="nativePlace" /> -->
          <!-- <el-table-column label="现从事行业" align="center" prop="currentIndustry" /> -->
          <!-- <el-table-column label="起始学历" align="center" prop="startEducation" />
              <el-table-column label="起始学位" align="center" prop="startDegree" /> -->
          <!-- <el-table-column label="最高学历" align="center" prop="highestEducation">
                <template #default="scope">
                  <dict-tag :options="hr_staff_education" :value="scope.row.highestEducation"/>
                </template>
              </el-table-column>
              <el-table-column label="最高学位" align="center" prop="highestDegree">
                <template #default="scope">
                  <dict-tag :options="hr_staff_academicdegree" :value="scope.row.highestDegree"/>
                </template>
              </el-table-column> -->
          <!-- <el-table-column label="起始毕业学校" align="center" prop="graduationSchool" />
              <el-table-column label="起始所学专业" align="center" prop="majorStudied" />
              <el-table-column label="起始毕业日期" align="center" prop="graduationDate" width="180">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.graduationDate, '{y}-{m}-{d}') }}</span>
                </template>
              </el-table-column> -->
          <!-- <el-table-column label="最终毕业学校" align="center" prop="finalGraduationSchool" />
              <el-table-column label="最终所学专业" align="center" prop="finalMajorStudied" />
              <el-table-column label="最终毕业日期" align="center" prop="finalGraduationDate" width="180">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.finalGraduationDate, '{y}-{m}-{d}') }}</span>
                </template>
              </el-table-column>
              <el-table-column label="总工龄" align="center" prop="totalTenure" />
              <el-table-column label="本院参加工作日期" align="center" prop="currentEmploymentDate" width="180">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.currentEmploymentDate, '{y}-{m}-{d}') }}</span>
                </template>
              </el-table-column>
              <el-table-column label="本单位工龄" align="center" prop="currentTenure" /> -->
          <!-- <el-table-column label="护理证书编号" align="center" prop="certificateNumber" /> -->
          <!-- <el-table-column label="护理证书有效期" align="center" prop="certificateExpiry" width="180">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.certificateExpiry, '{y}-{m}-{d}') }}</span>
                </template>
              </el-table-column> -->
          <!-- <el-table-column label="个人擅长" align="center" prop="personalExpertise" /> -->
          <!-- <el-table-column label="二三胎标志" align="center" prop="secondChildFlag">
                <template #default="scope">
                  <dict-tag :options="sys_common_flag" :value="scope.row.secondChildFlag"/>
                </template>
              </el-table-column> -->
          <!-- <el-table-column label="图章号" align="center" prop="sealNumber" />
              <el-table-column label="签名字体" align="center" prop="signatureFont" /> -->
          <!-- <el-table-column label="在编标志" align="center" prop="inService">
                <template #default="scope">
                  <dict-tag :options="sys_common_flag" :value="scope.row.inService"/>
                </template>
              </el-table-column>
              <el-table-column label="定科" align="center" prop="departmentFixed">
                <template #default="scope">
                  <dict-tag :options="sys_common_flag" :value="scope.row.departmentFixed"/>
                </template>
              </el-table-column> -->
          <!-- <el-table-column label="离职日期" align="center" prop="resignationDate" width="180">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.resignationDate, '{y}-{m}-{d}') }}</span>
                </template>
              </el-table-column> -->
          <el-table-column label="在岗状态" align="center" prop="onLeave" >
            <template #default="scope">
              <el-text >{{scope.row.onLeave==1?"是":"否"}}</el-text>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            width="130"
          >
            <template #default="scope">
              <el-button
                link
                type="primary"
                icon="Edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['hr:staffInfo:edit']"
                >修改</el-button
              >
              <el-button
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['hr:staffInfo:remove']"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
    <!-- 添加或修改员工基本信息抽屉 -->
    <el-drawer v-model="open" append-to-body size="60%" :show-close="false">
      <template #header>
        <div class="top_card_content">
          <el-col :span="20" style="display: flex">
            <el-form-item label-width="0" style="width: 70px">
              <el-avatar :size="70" :src="form.homePhoto"> </el-avatar>
            </el-form-item>
            <div style="margin-left: 16px">
              <el-text class="nickname_input">
                {{ form.nickName ? form.nickName : "用户名称" }}
              </el-text>
              <div style="width: 100%; display: flex; margin-top: 10px">
                <div style="display: flex; align-items: center; min-width: 70px">
                  <el-icon style="margin-right: 6px"><User /></el-icon>
                  <el-text v-if="haveSomething(sys_user_sex, form.sex).length > 0">{{
                    haveSomething(sys_user_sex, form.sex)[0].label
                  }}</el-text>
                  <el-text v-else>性别</el-text>
                </div>
                <div style="display: flex; align-items: center; min-width: 130px">
                  <el-icon style="margin-right: 6px"><Iphone /></el-icon>
                  <el-text>{{
                    form.contactNumber ? form.contactNumber : "手机号码"
                  }}</el-text>
                </div>
                <div style="display: flex; align-items: center">
                  <el-icon style="margin-right: 6px"><Message /></el-icon>
                  <el-text style="width: 180px">{{
                    form.hospitalEmail ? form.hospitalEmail : "邮箱"
                  }}</el-text>
                </div>
              </div>
            </div>
          </el-col>
        </div>
        <div class="dialog-footer">
          <el-button type="primary" @click="changeDisabled" v-if="isDisabled"
            >编 辑</el-button
          >
          <el-button type="primary" @click="submitForm" v-if="!isDisabled"
            >确 定</el-button
          >
          <el-button @click="cancel" v-if="!isDisabled">取 消</el-button>
        </div>
      </template>
      <el-form ref="staffInfoRef" :model="form" :rules="rules" label-width="130px">
        <div class="top_card">
          <!-- tabs切换 -->
          <div>
            <el-tabs
              v-model="activeName"
              class="demo-tabs gz-tags-scroller"
              @tab-click="changeTabs"
            >
              <el-tab-pane label="基本信息" name="first">
                <el-row>
                  <el-col :span="8">
                    <el-form-item label="用户名称" prop="nickName">
                      <el-input
                        v-model="form.nickName"
                        placeholder="请输入用户名称"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{ form.nickName ? form.nickName : "-" }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="用户工号" prop="userCode">
                      <el-input
                        v-model="form.userCode"
                        placeholder="请输入用户工号"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{ form.userCode ? form.userCode : "-" }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="联系电话" prop="contactNumber">
                      <el-input
                        v-model="form.contactNumber"
                        placeholder="请输入联系电话"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.contactNumber ? form.contactNumber : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="医院邮箱" prop="hospitalEmail">
                      <el-input
                        v-model="form.hospitalEmail"
                        placeholder="请输入医院邮箱"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.hospitalEmail ? form.hospitalEmail : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="性别" prop="sex">
                      <el-select
                        v-model="form.sex"
                        placeholder="请选择性别"
                        v-if="!isDisabled"
                      >
                        <el-option
                          v-for="dict in sys_user_sex"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                      <div v-else>
                        <el-text
                          v-if="haveSomething(sys_user_sex, form.sex).length > 0"
                          >{{ haveSomething(sys_user_sex, form.sex)[0].label }}</el-text
                        >
                        <el-text v-else>-</el-text>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="籍贯" prop="nativePlace">
                      <el-input
                        v-model="form.nativePlace"
                        placeholder="请输入籍贯"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.nativePlace ? form.nativePlace : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="身份证号" prop="idCardNumber">
                      <el-input
                        v-model="form.idCardNumber"
                        placeholder="请输入身份证号"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.idCardNumber ? form.idCardNumber : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="民族" prop="ethnicity">
                      <el-select
                        v-model="form.ethnicity"
                        placeholder="请选择民族"
                        v-if="!isDisabled"
                      >
                        <el-option
                          v-for="dict in hr_staff_nation"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                      <div v-else>
                        <el-text
                          v-if="haveSomething(hr_staff_nation, form.ethnicity).length > 0"
                          >{{
                            haveSomething(hr_staff_nation, form.ethnicity)[0].label
                          }}</el-text
                        >
                        <el-text v-else>-</el-text>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="政治面貌" prop="politicalStatus">
                      <el-select
                        v-model="form.politicalStatus"
                        placeholder="请选择政治面貌"
                        v-if="!isDisabled"
                      >
                        <el-option
                          v-for="dict in hr_staff_politicaloutlook"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                      <div v-else>
                        <el-text
                          v-if="
                            haveSomething(hr_staff_politicaloutlook, form.politicalStatus)
                              .length > 0
                          "
                          >{{
                            haveSomething(
                              hr_staff_politicaloutlook,
                              form.politicalStatus
                            )[0].label
                          }}</el-text
                        >
                        <el-text v-else>-</el-text>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="出生日期" prop="birthDate">
                      <el-date-picker
                        clearable
                        v-model="form.birthDate"
                        type="date"
                        value-format="YYYY-MM-DD"
                        placeholder="请选择出生日期"
                        v-if="!isDisabled"
                      >
                      </el-date-picker>
                      <el-text v-else>{{
                        form.birthDate ? form.birthDate : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="婚姻状态" prop="maritalStatus">
                      <el-select
                        v-model="form.maritalStatus"
                        placeholder="请选择婚姻状态"
                        v-if="!isDisabled"
                      >
                        <el-option
                          v-for="dict in hr_staff_marital"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                      <div v-else>
                        <el-text
                          v-if="
                            haveSomething(hr_staff_marital, form.maritalStatus).length > 0
                          "
                          >{{
                            haveSomething(hr_staff_marital, form.maritalStatus)[0].label
                          }}</el-text
                        >
                        <el-text v-else>-</el-text>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="二三胎标志" prop="secondChildFlag">
                      <el-select
                        v-model="form.secondChildFlag"
                        placeholder="请选择健康状况"
                        v-if="!isDisabled"
                      >
                        <el-option
                          v-for="dict in sys_common_flag"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                      <div v-else>
                        <el-text
                          v-if="
                            haveSomething(sys_common_flag, form.secondChildFlag).length >
                            0
                          "
                          >{{
                            haveSomething(sys_common_flag, form.secondChildFlag)[0].label
                          }}</el-text
                        >
                        <el-text v-else>-</el-text>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="健康状况" prop="healthStatus">
                      <el-select
                        v-model="form.healthStatus"
                        placeholder="请选择健康状况"
                        v-if="!isDisabled"
                      >
                        <el-option
                          v-for="dict in hr_staff_health_status"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                      <div v-else>
                        <el-text
                          v-if="
                            haveSomething(hr_staff_health_status, form.healthStatus)
                              .length > 0
                          "
                          >{{
                            haveSomething(hr_staff_health_status, form.healthStatus)[0]
                              .label
                          }}</el-text
                        >
                        <el-text v-else>-</el-text>
                      </div>
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :span="8">
                    <el-form-item label="拼音简称" prop="pinyinShort">
                      <el-input v-model="form.pinyinShort" placeholder="请输入拼音简称" />
                    </el-form-item>
                  </el-col> -->
                  <!-- <el-col :span="8">
                    <el-form-item label="拼音全拼" prop="pinyin">
                      <el-input v-model="form.pinyin" placeholder="请输入拼音全拼" />
                    </el-form-item>
                  </el-col> -->
                  <el-col :span="8">
                    <el-form-item label="身高体重鞋码" prop="height">
                      <div style="display: flex" v-if="!isDisabled">
                        <el-input v-model="form.height" placeholder="身高" />
                        <el-input v-model="form.weight" placeholder="体重" />
                        <el-input v-model="form.shoeSize" placeholder="鞋码" />
                      </div>
                      <el-text v-else>{{
                        form.height || form.weight || form.shoeSize
                          ? form.height + "-" + form.weight + "-" + form.shoeSize
                          : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" v-if="false">
                    <el-form-item label="体重" prop="weight">
                      <el-input
                        v-model="form.weight"
                        placeholder="请输入体重"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{ form.weight ? form.weight : "-" }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" v-if="false">
                    <el-form-item label="鞋码" prop="shoeSize">
                      <el-input
                        v-model="form.shoeSize"
                        placeholder="请输入鞋码"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{ form.shoeSize ? form.shoeSize : "-" }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" v-if="false">
                    <el-form-item label="现从事行业" prop="currentIndustry">
                      <el-input
                        v-model="form.currentIndustry"
                        placeholder="请输入现从事行业"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.currentIndustry ? form.currentIndustry : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="图章号" prop="sealNumber">
                      <el-input
                        v-model="form.sealNumber"
                        placeholder="请输入图章号"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.sealNumber ? form.sealNumber : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="签名字体" prop="signatureFont">
                      <el-input
                        v-model="form.signatureFont"
                        placeholder="请输入签名字体"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.signatureFont ? form.signatureFont : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="个人擅长" prop="personalExpertise">
                      <el-input
                        v-model="form.personalExpertise"
                        type="textarea"
                        placeholder="请输入内容"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.personalExpertise ? form.personalExpertise : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="备注" prop="remark">
                      <el-input
                        v-model="form.remark"
                        type="textarea"
                        placeholder="请输入内容"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{ form.remark ? form.remark : "-" }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="工作照" prop="workPhoto">
                      <image-upload
                        v-model="form.workPhoto"
                        :limit="8"
                        v-if="!isDisabled"
                      />
                      <image-preview
                        :src="form.workPhoto"
                        :width="100"
                        :height="100"
                        v-else
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="生活照/头像" prop="homePhoto">
                      <image-upload
                        v-model="form.homePhoto"
                        :limit="1"
                        v-if="!isDisabled"
                      />
                      <image-preview
                        :src="form.homePhoto"
                        :width="100"
                        :height="100"
                        v-else
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-tab-pane>
              <el-tab-pane label="任职信息" name="second">
                <el-row>
                  <el-col :span="8">
                    <el-form-item label="护理单元" prop="deptId">
                      <el-tree-select
                        v-model="form.deptId"
                        :data="deptOptions"
                        :props="{ value: 'id', label: 'label', children: 'children' }"
                        value-key="id"
                        placeholder="请选择护理单元"
                        check-strictly
                        @node-click="handleClick($event, 'deptName')"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{ form.deptName ? form.deptName : "-" }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="护理能级" prop="level">
                      <el-select
                        v-model="form.level"
                        placeholder="请选择护理能级"
                        v-if="!isDisabled"
                      >
                        <el-option
                          v-for="dict in hr_staff_level"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                      <div v-else>
                        <el-text
                          v-if="haveSomething(hr_staff_level, form.level).length > 0"
                          >{{
                            haveSomething(hr_staff_level, form.level)[0].label
                          }}</el-text
                        >
                        <el-text v-else>-</el-text>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="编制科室" prop="compileDeptId">
                      <el-tree-select
                        v-model="form.compileDeptId"
                        :data="deptOptions"
                        :props="{ value: 'id', label: 'label', children: 'children' }"
                        value-key="id"
                        placeholder="请选择编制科室"
                        check-strictly
                        @node-click="handleClick($event, 'clinicalDeptName')"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.clinicalDeptName ? form.clinicalDeptName : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" v-if="false">
                    <el-form-item label="编制科室名称" prop="compileDeptName">
                      <el-input
                        v-model="form.compileDeptName"
                        placeholder="请输入编制科室名称"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.compileDeptName ? form.compileDeptName : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" v-if="false">
                    <el-form-item label="所属科室" prop="affiliatedDeptId">
                      <el-input
                        v-model="form.affiliatedDeptId"
                        placeholder="请输入所属科室"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.affiliatedDeptId ? form.affiliatedDeptId : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="所属科室名称" prop="affiliatedDeptName">
                      <el-input
                        v-model="form.affiliatedDeptName"
                        placeholder="请输入所属科室名称"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.affiliatedDeptName ? form.affiliatedDeptName : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" v-if="false">
                    <el-form-item label="所属病区" prop="wardId">
                      <el-input
                        v-model="form.wardId"
                        placeholder="请输入所属病区"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{ form.wardId ? form.wardId : "-" }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" v-if="false">
                    <el-form-item label="所属病区名称" prop="wardName">
                      <el-input
                        v-model="form.wardName"
                        placeholder="请输入所属病区名称"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{ form.wardName ? form.wardName : "-" }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="所属护理组" prop="nurseGroupId">
                      <el-select
                        v-model="form.nurseGroupId"
                        placeholder="请选择护理组"
                        @change="handleChange($event, 'nurseGroupName')"
                        v-if="!isDisabled"
                      >
                        <el-option
                          v-for="dict in workGroupList"
                          :key="dict.ids"
                          :label="dict.groupName"
                          :value="dict.id"
                        ></el-option>
                      </el-select>
                      <el-text v-else>{{
                        form.nurseGroupName ? form.nurseGroupName : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="护理组内排序" prop="nurseGroupSort">
                      <el-input
                        v-model="form.nurseGroupSort"
                        placeholder="请输入护理组内排序"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.nurseGroupSort ? form.nurseGroupSort : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" v-if="false">
                    <el-form-item label="医生临床科室" prop="clinicalDeptId">
                      <el-input
                        v-model="form.clinicalDeptId"
                        placeholder="请输入医生临床科室"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.clinicalDeptId ? form.clinicalDeptId : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="医生临床科室名称" prop="clinicalDeptName">
                      <el-input
                        v-model="form.clinicalDeptName"
                        placeholder="请输入医生临床科室名称"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.clinicalDeptName ? form.clinicalDeptName : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="专科角色" prop="specializedRole">
                      <el-input
                        v-model="form.specializedRole"
                        placeholder="请输入专科角色"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.specializedRole ? form.specializedRole : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="病区角色" prop="wardRole">
                      <el-input
                        v-model="form.wardRole"
                        placeholder="请输入病区角色"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{ form.wardRole ? form.wardRole : "-" }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="职业类别" prop="professionCategory">
                      <el-select
                        v-model="form.professionCategory"
                        placeholder="请选择职业类型"
                        @change="handleChange($event, 'professionCategory')"
                        v-if="!isDisabled"
                      >
                        <el-option
                          v-for="dict in hr_profession_category"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                      <div v-else>
                        <el-text
                          v-if="haveSomething(hr_profession_category, form.professionCategory).length > 0"
                          >{{
                            haveSomething(hr_profession_category, form.professionCategory)[0].label
                          }}</el-text
                        >
                        <el-text v-else>-</el-text>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="员工来源" prop="employeeSource">
                      <el-input
                        v-model="form.employeeSource"
                        placeholder="请输入员工来源"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.employeeSource ? form.employeeSource : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="员工来源单位" prop="employeeSourceUnit">
                      <el-input
                        v-model="form.employeeSourceUnit"
                        placeholder="请输入员工来源单位"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.employeeSourceUnit ? form.employeeSourceUnit : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="职称" prop="jobTitle">
                      <el-select
                        v-model="form.jobTitle"
                        placeholder="请选择职称"
                        v-if="!isDisabled"
                      >
                        <el-option
                          v-for="dict in hr_staff_title"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                      <div v-else>
                        <el-text
                          v-if="haveSomething(hr_staff_title, form.jobTitle).length > 0"
                          >{{
                            haveSomething(hr_staff_title, form.jobTitle)[0].label
                          }}</el-text
                        >
                        <el-text v-else>-</el-text>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="聘任职称" prop="appointedJob">
                      <el-select
                        v-model="form.appointedJob"
                        placeholder="请选择职称"
                        v-if="!isDisabled"
                      >
                        <el-option
                          v-for="dict in hr_staff_title"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                      <div v-else>
                        <el-text
                          v-if="
                            haveSomething(hr_staff_title, form.appointedJob).length > 0
                          "
                          >{{
                            haveSomething(hr_staff_title, form.appointedJob)[0].label
                          }}</el-text
                        >
                        <el-text v-else>-</el-text>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="医院长号码" prop="longNumber">
                      <el-input
                        v-model="form.longNumber"
                        placeholder="请输入医院长号码"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.longNumber ? form.longNumber : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="医院短号码" prop="shortNumber">
                      <el-input
                        v-model="form.shortNumber"
                        placeholder="请输入医院短号码"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.shortNumber ? form.shortNumber : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="总工龄" prop="totalTenure">
                      <el-input
                        v-model="form.totalTenure"
                        placeholder="请输入总工龄"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.totalTenure ? form.totalTenure : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="本院参加工作日期" prop="currentEmploymentDate">
                      <el-date-picker
                        clearable
                        v-model="form.currentEmploymentDate"
                        type="date"
                        value-format="YYYY-MM-DD"
                        placeholder="请选择本院参加工作日期"
                        v-if="!isDisabled"
                      >
                      </el-date-picker>
                      <el-text v-else>{{
                        form.currentEmploymentDate ? form.currentEmploymentDate : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="本单位工龄" prop="currentTenure">
                      <el-input
                        v-model="form.currentTenure"
                        placeholder="请输入本单位工龄"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.currentTenure ? form.currentTenure : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="护理证书编号" prop="certificateNumber">
                      <el-input
                        v-model="form.certificateNumber"
                        placeholder="请输入护理证书编号"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.certificateNumber ? form.certificateNumber : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="护理证书有效期" prop="certificateExpiry">
                      <el-date-picker
                        clearable
                        v-model="form.certificateExpiry"
                        type="date"
                        value-format="YYYY-MM-DD"
                        placeholder="请选择护理证书有效期"
                        v-if="!isDisabled"
                      >
                      </el-date-picker>
                      <el-text v-else>{{
                        form.certificateExpiry ? form.certificateExpiry : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="在编标志" prop="inService">
                      <el-radio-group v-model="form.inService" v-if="!isDisabled">
                        <el-radio
                          v-for="dict in sys_common_flag"
                          :key="dict.value"
                          :label="dict.value"
                          >{{ dict.label }}</el-radio
                        >
                      </el-radio-group>
                      <div v-else>
                        <el-text
                          v-if="haveSomething(sys_common_flag, form.inService).length > 0"
                          >{{
                            haveSomething(sys_common_flag, form.inService)[0].label
                          }}</el-text
                        >
                        <el-text v-else>-</el-text>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="定科" prop="departmentFixed">
                      <el-radio-group v-model="form.departmentFixed" v-if="!isDisabled">
                        <el-radio
                          v-for="dict in sys_common_flag"
                          :key="dict.value"
                          :label="dict.value"
                          >{{ dict.label }}</el-radio
                        >
                      </el-radio-group>
                      <div v-else>
                        <el-text
                          v-if="
                            haveSomething(sys_common_flag, form.departmentFixed).length >
                            0
                          "
                          >{{
                            haveSomething(sys_common_flag, form.departmentFixed)[0].label
                          }}</el-text
                        >
                        <el-text v-else>-</el-text>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="离职日期" prop="resignationDate">
                      <el-date-picker
                        clearable
                        v-model="form.resignationDate"
                        type="date"
                        value-format="YYYY-MM-DD"
                        placeholder="请选择离职日期"
                        v-if="!isDisabled"
                      >
                      </el-date-picker>
                      <el-text v-else>{{
                        form.resignationDate ? form.resignationDate : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="在岗状态" prop="onLeave">
                      <el-radio-group v-model="form.onLeave" v-if="!isDisabled">
                        <el-radio
                          v-for="dict in sys_common_flag"
                          :key="dict.value"
                          :label="dict.value"
                          >{{ dict.label }}</el-radio
                        >
                      </el-radio-group>
                      <div v-else>
                        <el-text
                          v-if="haveSomething(sys_common_flag, form.onLeave).length > 0"
                          >{{
                            haveSomething(sys_common_flag, form.onLeave)[0].label
                          }}</el-text
                        >
                        <el-text v-else>-</el-text>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-tab-pane>
              <el-tab-pane label="家庭信息" name="third">
                <el-row>
                  <el-col :span="8">
                    <el-form-item label="家庭地址" prop="homeAddress">
                      <el-input
                        v-model="form.homeAddress"
                        placeholder="请输入家庭地址"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.homeAddress ? form.homeAddress : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="家庭邮编" prop="homeZipCode">
                      <el-input
                        v-model="form.homeZipCode"
                        placeholder="请输入家庭邮编"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.homeZipCode ? form.homeZipCode : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="家庭电话" prop="homePhone">
                      <el-input
                        v-model="form.homePhone"
                        placeholder="请输入家庭电话"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.homePhone ? form.homePhone : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="户籍" prop="houseRegistration">
                      <el-input
                        v-model="form.houseRegistration"
                        placeholder="请输入户籍"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.houseRegistration ? form.houseRegistration : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-tab-pane>
              <el-tab-pane label="学历信息" name="fourth">
                <el-row>
                  <el-col :span="8">
                    <el-form-item label="起始学历" prop="startEducation">
                      <el-input
                        v-model="form.startEducation"
                        placeholder="请输入起始学历"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.startEducation ? form.startEducation : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="起始学位" prop="startDegree">
                      <el-input
                        v-model="form.startDegree"
                        placeholder="请输入起始学位"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.startDegree ? form.startDegree : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="最高学历" prop="highestEducation">
                      <el-select
                        v-model="form.highestEducation"
                        placeholder="请选择最高学历"
                        v-if="!isDisabled"
                      >
                        <el-option
                          v-for="dict in hr_staff_education"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                      <el-text v-else>{{
                        form.highestEducation ? form.highestEducation : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="最高学位" prop="highestDegree">
                      <el-select
                        v-model="form.highestDegree"
                        placeholder="请选择最高学位"
                        v-if="!isDisabled"
                      >
                        <el-option
                          v-for="dict in hr_staff_academicdegree"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                      <el-text v-else>{{
                        form.highestDegree ? form.highestDegree : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="起始毕业学校" prop="graduationSchool">
                      <el-input
                        v-model="form.graduationSchool"
                        placeholder="请输入起始毕业学校"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.graduationSchool ? form.graduationSchool : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="起始所学专业" prop="majorStudied">
                      <el-input
                        v-model="form.majorStudied"
                        placeholder="请输入起始所学专业"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.majorStudied ? form.majorStudied : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="起始毕业日期" prop="graduationDate">
                      <el-date-picker
                        clearable
                        v-model="form.graduationDate"
                        type="date"
                        value-format="YYYY-MM-DD"
                        placeholder="请选择起始毕业日期"
                        v-if="!isDisabled"
                      >
                      </el-date-picker>
                      <el-text v-else>{{
                        form.graduationDate ? form.graduationDate : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="最终毕业学校" prop="finalGraduationSchool">
                      <el-input
                        v-model="form.finalGraduationSchool"
                        placeholder="请输入最终毕业学校"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.finalGraduationSchool ? form.finalGraduationSchool : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="最终所学专业" prop="finalMajorStudied">
                      <el-input
                        v-model="form.finalMajorStudied"
                        placeholder="请输入最终所学专业"
                        v-if="!isDisabled"
                      />
                      <el-text v-else>{{
                        form.finalMajorStudied ? form.finalMajorStudied : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="最终毕业日期" prop="finalGraduationDate">
                      <el-date-picker
                        clearable
                        v-model="form.finalGraduationDate"
                        type="date"
                        value-format="YYYY-MM-DD"
                        placeholder="请选择最终毕业日期"
                        v-if="!isDisabled"
                      >
                      </el-date-picker>
                      <el-text v-else>{{
                        form.finalGraduationDate ? form.finalGraduationDate : "-"
                      }}</el-text>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-form>
    </el-drawer>
  </div>
</template>

<script setup name="StaffInfo">
import {
  listStaffInfo,
  getStaffInfo,
  delStaffInfo,
  addStaffInfo,
  updateStaffInfo,
} from "@/api/hr/staffInfo";
import { deptTreeSelect } from "@/api/system/user";
import { listWorkGroup } from "@/api/arr/workGroup";

const { proxy } = getCurrentInstance();
const {
  hr_staff_nation,
  hr_staff_education,
  hr_staff_politicaloutlook,
  hr_staff_marital,
  hr_staff_academicdegree,
  hr_staff_health_status,
  sys_common_flag,
  hr_staff_level,
  sys_user_sex,
  hr_staff_title,
  hr_profession_category
} = proxy.useDict(
  "hr_staff_nation",
  "hr_staff_education",
  "hr_staff_politicaloutlook",
  "hr_staff_marital",
  "hr_staff_academicdegree",
  "hr_staff_health_status",
  "sys_common_flag",
  "hr_staff_level",
  "sys_user_sex",
  "hr_staff_title",
  "hr_profession_category"
);

const staffInfoList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const deptName = ref("");
const data = reactive({
  form: { deptId: null },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    nickName: null,
    deptId: null,
    deptName: null,
    userCode: null,
    level: null,
    compileDeptId: null,
    compileDeptName: null,
    affiliatedDeptId: null,
    affiliatedDeptName: null,
    wardId: null,
    wardName: null,
    nurseGroupId: null,
    nurseGroupSort: null,
    clinicalDeptId: null,
    clinicalDeptName: null,
    specializedRole: null,
    wardRole: null,
    professionCategory: null,
    employeeType: null,
    employeeSource: null,
    employeeSourceUnit: null,
    jobTitle: null,
    appointedJob: null,
    pinyinShort: null,
    pinyin: null,
    ethnicity: null,
    politicalStatus: null,
    workPhoto: null,
    homePhoto: null,
    height: null,
    weight: null,
    shoeSize: null,
    birthDate: null,
    maritalStatus: null,
    healthStatus: null,
    idCardNumber: null,
    contactNumber: null,
    longNumber: null,
    shortNumber: null,
    hospitalEmail: null,
    homeAddress: null,
    homeZipCode: null,
    homePhone: null,
    houseType: null,
    houseRegistration: null,
    nativePlace: null,
    currentIndustry: null,
    startEducation: null,
    startDegree: null,
    highestEducation: null,
    highestDegree: null,
    graduationSchool: null,
    majorStudied: null,
    graduationDate: null,
    finalGraduationSchool: null,
    finalMajorStudied: null,
    finalGraduationDate: null,
    totalTenure: null,
    currentEmploymentDate: null,
    currentTenure: null,
    certificateNumber: null,
    certificateExpiry: null,
    personalExpertise: null,
    secondChildFlag: null,
    sealNumber: null,
    signatureFont: null,
    inService: null,
    departmentFixed: null,
    resignationDate: null,
    onLeave: null,
  },
  rules: {
    nickName: [{ required: true, message: "用户名称不能为空", trigger: "blur" }],
    userCode: [{ required: true, message: "用户工号不能为空", trigger: "blur" }],
    contactNumber: [
      { required: true, message: "联系电话不能为空", trigger: "blur" },
      {
        pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        message: "请输入正确的手机号码",
        trigger: "blur",
      },
    ],
    deptId: [{ required: true, message: "护理单元不能为空", trigger: "blur" }],
    level: [{ required: true, message: "护理能级不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

// 单层多数据筛选
function haveSomething(value1, value2) {
  return value1.filter((item) => item.value == value2);
}

// 多层多数据筛选
function haveSomethingAll(value1, value2) {
  console.log(value1);
  console.log(value1.map((item) => item.id == value2));
}

/** 节点单击事件 */
function handleNodeClick(data) {
  console.log(data);
  queryParams.value.deptId = data.id;
  queryParams.value.deptName = data.label;
  handleQuery();
}
/** 查询员工基本信息列表 */
function getList() {
  loading.value = true;
  listStaffInfo(queryParams.value).then((response) => {
    staffInfoList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  isDisabled.value = true;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    nickName: null,
    userId: null,
    deptId: null,
    deptName: null,
    userCode: null,
    level: null,
    compileDeptId: null,
    compileDeptName: null,
    affiliatedDeptId: null,
    affiliatedDeptName: null,
    wardId: null,
    wardName: null,
    nurseGroupId: null,
    nurseGroupName: null,
    nurseGroupSort: null,
    clinicalDeptId: null,
    clinicalDeptName: null,
    specializedRole: null,
    wardRole: null,
    professionCategory: null,
    employeeType: null,
    employeeSource: null,
    employeeSourceUnit: null,
    jobTitle: null,
    appointedJob: null,
    pinyinShort: null,
    pinyin: null,
    ethnicity: null,
    politicalStatus: null,
    workPhoto: null,
    homePhoto: null,
    height: null,
    weight: null,
    shoeSize: null,
    birthDate: null,
    maritalStatus: null,
    healthStatus: null,
    idCardNumber: null,
    contactNumber: null,
    longNumber: null,
    shortNumber: null,
    hospitalEmail: null,
    homeAddress: null,
    homeZipCode: null,
    homePhone: null,
    houseType: null,
    houseRegistration: null,
    nativePlace: null,
    currentIndustry: null,
    startEducation: null,
    startDegree: null,
    highestEducation: null,
    highestDegree: null,
    graduationSchool: null,
    majorStudied: null,
    graduationDate: null,
    finalGraduationSchool: null,
    finalMajorStudied: null,
    finalGraduationDate: null,
    totalTenure: null,
    currentEmploymentDate: null,
    currentTenure: null,
    certificateNumber: null,
    certificateExpiry: null,
    personalExpertise: null,
    secondChildFlag: null,
    sealNumber: null,
    signatureFont: null,
    inService: null,
    departmentFixed: null,
    resignationDate: null,
    onLeave: null,
    createBy: null,
    createId: null,
    createTime: null,
    updateBy: null,
    updateId: null,
    updateTime: null,
    tenantId: null,
    delFlag: null,
    remark: null,
  };
  proxy.resetForm("staffInfoRef");
}

/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};
/** 根据名称筛选部门树 */
watch(deptName, (val) => {
  proxy.$refs["deptTreeRef"].filter(val);
});
/** 查询部门下拉树结构 */
const deptOptions = ref(undefined);
function getDeptTree() {
  deptTreeSelect().then((response) => {
    // let obj = {
    //   id: null,
    //   label: "全部",
    // };
    // response.data.unshift(obj);
    deptOptions.value = response.data;
  });
}

/** 获取护理单元选中节点信息 */
function handleClick(node, name) {
  form.value[name] = node.label;
  if (name == "deptName") {
    form.value.nurseGroupId = null;
    form.value.nurseGroupName = null;
    getWorkGroup(node.id);
  }
}
/* 所属护理组事件 */
function handleChange(value, name) {
  if (name == "nurseGroupName") {
    form.value[name] = workGroupList.value.filter(
      (item) => item.id == value
    )[0].groupName;
    console.log(form.value);
  }
  // else if(name == "professionCategory"){
  //   form.value[name] = JSON.parse(value)
  // }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加员工基本信息";
  form.value.deptId = queryParams.value.deptId;
  form.value.deptName = queryParams.value.deptName;
  isDisabled.value = false;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _userId = row.userId || ids.value;
  getStaffInfo(_userId).then((response) => {
    console.log(response);
    form.value = response.data;
    open.value = true;
    title.value = "修改员工基本信息";
    getWorkGroup(form.value.deptId);
    // isDisabled?form.value.homePhoto = form.value.homePhoto.split(","):form.value.homePhoto
  });
}

/* 表单tabs切换相关事件 */
// 是否可以编辑内容
const isDisabled = ref(true);
function changeDisabled(e) {
  isDisabled.value = !isDisabled.value;
}
// tabs当前所在位置
const activeName = ref("first");
function changeTabs(e) {
  activeName.value = e.props.name;
}

/* 表单tabs切换相关事件 */

/** 提交按钮 */
function submitForm() {
  console.log(form.value);
  proxy.$refs["staffInfoRef"].validate((valid) => {
    if (valid) {
      if (form.value.userId != null) {
        updateStaffInfo(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          isDisabled.value = true;
          getList();
        });
      } else {
        addStaffInfo(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          isDisabled.value = true;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _userIds = row.userId || ids.value;
  proxy.$modal
    .confirm('是否确认删除员工基本信息编号为"' + _userIds + '"的数据项？')
    .then(function () {
      return delStaffInfo(_userIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "hr/staffInfo/export",
    {
      ...queryParams.value,
    },
    `staffInfo_${new Date().getTime()}.xlsx`
  );
}

/** 查询排班护理组列表 */
const workGroupList = ref([]);
function getWorkGroup(deptId) {
  let data = {
    pageNum: 1,
    pageSize: 20,
    // groupName: null,
    deptId: deptId,
    deptName: form.value.deptName,
    // status: null,
    // orderNum: null,
  };
  console.log(data);
  listWorkGroup(data).then((response) => {
    console.log("护理组：", response);
    workGroupList.value = response.rows;
  });
}

getList();
getDeptTree();
</script>

<style lang="scss">
.el-drawer__header {
  display: flex;
  align-items: flex-start;
  background-color: #fff;
  padding: 20px 4% 0px 4%;
  margin-bottom: 0;
}
.el-drawer__body {
  padding: 0;
}
.top_card {
  width: 100%;
  min-height: 160px;
  padding: 20px 4% 0 4%;
  background-color: #fff;
  .el-form-item__label {
    color: #8a8a8a;
  }

  .top_card_content {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .el-tabs__content {
    padding: 32px 0;
    font-size: 32px;
    font-weight: 600;
  }
  .el-text {
    color: #000;
  }
}

.nickname_input {
  width: 40%;
  font-weight: bold;
  font-size: 24px;
  color: #000;
}
</style>
