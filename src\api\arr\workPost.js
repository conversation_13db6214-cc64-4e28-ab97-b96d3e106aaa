import request from '@/utils/request'

// 查询排班岗位管理列表
export function listWorkPost(query) {
  return request({
    url: '/arr/workPost/list',
    method: 'get',
    params: query
  })
}

// 查询排班岗位管理详细
export function getWorkPost(id) {
  return request({
    url: '/arr/workPost/' + id,
    method: 'get'
  })
}

// 新增排班岗位管理
export function addWorkPost(data) {
  return request({
    url: '/arr/workPost',
    method: 'post',
    data: data
  })
}

// 修改排班岗位管理
export function updateWorkPost(data) {
  return request({
    url: '/arr/workPost/edit',
    method: 'post',
    data: data
  })
}

// 删除排班岗位管理
export function delWorkPost(id) {
  return request({
    url: '/arr/workPost/del/' + id,
    method: 'post'
  })
}

// 停用/启用排班岗位管理
export function changeStatus(status,id) {
  return request({
    url: `/arr/workPost/changeStatus/${status}/${id}`,
    method: 'get'
  })
}

