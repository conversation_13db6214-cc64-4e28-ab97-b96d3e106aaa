import request from '@/utils/request';

// 创建
export function AgentChat(data) {
    return request({
        url: '/api/v1/agents/ec0ee168345811f090410242ac120004/sessions',
        method: 'post',
        data
    });
}

// 登录接口
export function AgentCompletion(data) {
    return request({
        url: '/api/v1/chats/ec0ee168345811f090410242ac120004/completions',
        method: 'post',
        data
    });
}

// 历史记录创建
export function addHistory(data) {
    return request({
        url: 'app/api/system/chatConversationsApi/create-conversation',
        method: 'post',
        data
    });
}

// 历史记录添加消息，每次发一句话就调用一次
export function addMessage(data) {
    return request({
        url: 'app/api/system/chatConversationsApi/add-message',
        method: 'post',
        data
    });
}

// 历史记录获取消息
export function getMessageOld(data) {
    return request({
        url: 'app/api/system/chatConversationsApi/list-by-user',
        method: 'get',
        data
    });
}

// 历史记录获取消息
export function getMessage(conversationType,searchValue) {
    return request({
        url: `app/api/system/chatConversationsApi/list-by-user-grouped/${conversationType}/${searchValue}`,
        method: 'get'
    });
}

// 历史记录删除
export function removeMessage(data) {
    return request({
        url: 'app/api/system/chatConversationsApi/remove',
        method: 'post',
        data
    });
}
