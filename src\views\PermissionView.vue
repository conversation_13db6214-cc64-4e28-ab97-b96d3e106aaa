<template>
  <div class="permission-container">
    <div class="page-header">
      <div class="title">
        <v-icon size="28" color="primary">mdi-shield-account</v-icon>
        <h2>权限管理</h2>
      </div>
      <div class="description">
        管理系统用户、角色、菜单权限和部门组织结构
      </div>
    </div>

    <v-tabs v-model="activeTab" color="primary" align-tabs="start" class="permission-tabs">
      <v-tab value="user">
        <v-icon start>mdi-account-multiple</v-icon>
        用户管理
      </v-tab>
      <v-tab value="role">
        <v-icon start>mdi-account-group</v-icon>
        角色管理
      </v-tab>
      <v-tab value="menu">
        <v-icon start>mdi-menu</v-icon>
        菜单管理
      </v-tab>
      <v-tab value="dept">
        <v-icon start>mdi-domain</v-icon>
        部门管理
      </v-tab>
    </v-tabs>

    <v-window v-model="activeTab">
      <!-- 用户管理 -->
      <v-window-item value="user">
        <UserManagement />
      </v-window-item>

      <!-- 角色管理 -->
      <v-window-item value="role">
        <RoleManagement />
      </v-window-item>

      <!-- 菜单管理 -->
      <v-window-item value="menu">
        <MenuManagement />
      </v-window-item>

      <!-- 部门管理 -->
      <v-window-item value="dept">
        <DeptManagement />
      </v-window-item>
    </v-window>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import UserManagement from '../components/permission/UserManagement.vue';
import RoleManagement from '../components/permission/RoleManagement.vue';
import MenuManagement from '../components/permission/MenuManagement.vue';
import DeptManagement from '../components/permission/DeptManagement.vue';

// 当前激活的标签页
const activeTab = ref('user');

// 页面加载时的处理
onMounted(() => {
  // 可以在这里添加页面初始化逻辑
});
</script>

<style scoped>
.permission-container {
  padding: 20px 0;
}

.page-header {
  margin-bottom: 24px;
}

.title {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.title h2 {
  font-size: 22px;
  font-weight: 600;
  margin: 0 0 0 8px;
  color: var(--text-primary);
}

.description {
  color: var(--text-secondary);
  font-size: 14px;
}

.permission-tabs {
  margin-bottom: 20px;
}

:deep(.v-tab) {
  text-transform: none !important;
  letter-spacing: normal !important;
  font-weight: 500 !important;
  font-size: 14px !important;
}

:deep(.v-tab .v-icon) {
  margin-right: 4px;
}
</style>
