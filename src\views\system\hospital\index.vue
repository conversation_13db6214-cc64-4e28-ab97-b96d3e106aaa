<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <!-- <el-form-item label="机构编码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入机构编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="医院编码" prop="hospitalCode">
        <el-input
          v-model="queryParams.hospitalCode"
          placeholder="请输入医院编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="医院名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入医院名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="医院简称" prop="abbreviation">
        <el-input
          v-model="queryParams.abbreviation"
          placeholder="请输入医院简称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="所属省份" prop="province">
        <el-input
          v-model="queryParams.province"
          placeholder="请输入所属省份"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="所属城市" prop="city">
        <el-input
          v-model="queryParams.city"
          placeholder="请输入所属城市"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="医院地址" prop="address">
        <el-input
          v-model="queryParams.address"
          placeholder="请输入医院地址"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="医院电话" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入医院电话"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="所属院区" prop="area">
        <el-input
          v-model="queryParams.area"
          placeholder="请输入所属院区"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="总院标志" prop="generalFlag">
        <el-input
          v-model="queryParams.generalFlag"
          placeholder="请输入总院标志"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="医院邮编" prop="zipcode">
        <el-input
          v-model="queryParams.zipcode"
          placeholder="请输入医院邮编"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- 表单功能栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:hospital:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:hospital:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:hospital:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:hospital:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <!-- 表单 -->
    <el-table
      v-loading="loading"
      :data="hospitalList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="id" align="center" prop="deptId" /> -->
      <!-- <el-table-column label="机构编码" align="center" prop="code" /> -->
      <el-table-column label="医院编码" align="center" prop="hospitalCode" />
      <el-table-column label="医院名称" align="center" prop="name" />
      <el-table-column label="医院简称" align="center" prop="abbreviation" />
      <el-table-column label="所属省份" align="center" prop="province" />
      <el-table-column label="所属城市" align="center" prop="city" />
      <!-- <el-table-column label="医院照片" align="center" prop="photo" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.photo" :width="50" :height="50"/>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="医院介绍" align="center" prop="introduction" /> -->
      <!-- <el-table-column label="医院地址" align="center" prop="address" /> -->
      <el-table-column label="医院电话" align="center" prop="phone" />
      <!-- <el-table-column label="所属院区" align="center" prop="area" /> -->
      <!-- <el-table-column label="总院标志" align="center" prop="generalFlag" /> -->
      <!-- <el-table-column label="医院邮编" align="center" prop="zipcode" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:hospital:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:hospital:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 页码 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改医院信息对话框 -->
    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-form ref="hospitalRef" :model="form" :rules="rules" label-width="80px" inline>
        <el-row class="dialog_row">
          <el-col :span="12">
            <el-form-item label="机构编码" prop="code">
              <el-input v-model="form.code" placeholder="请输入机构编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="医院编码" prop="hospitalCode">
              <el-input v-model="form.hospitalCode" placeholder="请输入医院编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="医院名称" prop="name">
              <!-- <el-input v-model="form.name" placeholder="请输入医院名称" /> -->
              <el-select
                v-model="form.name"
                filterable
                remote
                reserve-keyword
                placeholder="请输入医院名称"
                :remote-method="remoteMethod"
                :loading="hosLoading"
                style="width: 100%"
              >
                <el-option
                  v-for="item in hospitalArr"
                  :key="item.index"
                  :label="item.name"
                  :value="item.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="医院简称" prop="abbreviation">
              <el-input v-model="form.abbreviation" placeholder="请输入医院简称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属省份" prop="province">
              <el-select
                v-model="form.province"
                placeholder="请选择省份"
                @change="handleProvinceSelect"
              >
                <el-option
                  v-for="item in regionData"
                  :key="item.value"
                  :label="item.label"
                  :value="item.label"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属城市" prop="city">
              <el-select v-model="form.city" placeholder="请选择城市">
                <el-option
                  v-for="item in cityList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.label"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="医院地址" prop="address">
              <el-input v-model="form.address" placeholder="请输入医院地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="医院电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入医院电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属院区" prop="area">
              <el-input v-model="form.area" placeholder="请输入所属院区" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总院标志" prop="generalFlag">
              <el-select
                v-model="form.generalFlag"
                placeholder="请选择总院标志"
                clearable
              >
                <el-option
                  v-for="dict in sys_common_flag"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="医院邮编" prop="zipcode">
              <el-input v-model="form.zipcode" placeholder="请输入医院邮编" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="医院照片" prop="photo">
              <!-- <image-upload v-model="form.photo"/> -->
              <imgUpload
                :modelValue="form.photo"
                @update:modelValue="getFiles"
              ></imgUpload>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="医院介绍" prop="introduction">
              <el-input
                v-model="form.introduction"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Hospital">
import { ref, toRefs, reactive } from "vue";
import {
  listHospital,
  getHospital,
  delHospital,
  addHospital,
  updateHospital,
} from "../../../api/system/hospital";
import { hosBaseList } from "@/api/system/dept";

const { proxy } = getCurrentInstance();
const { sys_common_flag } = proxy.useDict("sys_common_flag");

import imgUpload from "../../../components/ImageUpload/index.vue";
import { regionData } from "element-china-area-data";

const hospitalList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    code: null,
    hospitalCode: null,
    name: null,
    abbreviation: null,
    province: null,
    city: null,
    photo: null,
    introduction: null,
    address: null,
    phone: null,
    area: null,
    generalFlag: null,
    zipcode: null,
  },
  rules: {
    // hospitalCode: [{ required: true, message: '请填写医院编码',  trigger: 'blur' }],
    name: [{ required: true, message: "请填写医院名称", trigger: "blur" }],
    // province: [{ required: true, message: '请选择所属省份',  trigger: 'blur' }],
    // city: [{ required: true, message: '请选择所属城市',  trigger: 'blur' }],
    // address: [{ required: true, message: '请填写医院地址',  trigger: 'blur' }],
    // phone: [{ required: true, message: '请填写医院电话',  trigger: 'blur' }],
    // phone: [{ required: true, message: '请填写医院编码',  trigger: 'blur' }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询医院信息列表 */
function getList() {
  loading.value = true;
  listHospital(queryParams.value).then((response) => {
    console.log(response);
    hospitalList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    deptId: null,
    code: null,
    hospitalCode: null,
    name: null,
    abbreviation: null,
    province: null,
    city: "",
    photo: null,
    introduction: null,
    address: null,
    phone: null,
    area: null,
    generalFlag: null,
    zipcode: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    tenantId: null,
    delFlag: null,
    remark: null,
  };
  proxy.resetForm("hospitalRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.deptId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加医院信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _deptId = row.deptId || ids.value;
  getHospital(_deptId).then((response) => {
    response.data.photo = response.data.photo ? response.data.photo.split(",") : [];
    form.value = response.data;
    open.value = true;
    title.value = "修改医院信息";
  });
}

/** 提交按钮 */
function submitForm() {
  if (form.value.photo && typeof form.value.photo != "string") {
    form.value.photo = form.value.photo.join(",");
  }
  proxy.$refs["hospitalRef"].validate((valid) => {
    if (valid) {
      if (form.value.deptId != null) {
        updateHospital(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addHospital(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _deptIds = row.deptId || ids.value;
  proxy.$modal
    .confirm('是否确认删除医院信息编号为"' + _deptIds + '"的数据项？')
    .then(function () {
      return delHospital(_deptIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "system/hospital/export",
    {
      ...queryParams.value,
    },
    `hospital_${new Date().getTime()}.xlsx`
  );
}

getList();

/* 省份、城市相关事件 */
//二级城市列表
const cityList = ref([
  {
    value: "",
    children: [],
    label: "",
  },
]);

function handleProvinceSelect(e) {
  form.value.city = "";
  let temp = regionData.find((item) => {
    return item.label == e;
  });
  cityList.value = temp.children;
}

/* 省份、城市相关事件 */

/* 图片相关事件 */
function getFiles(e) {
  form.value.photo = e;
}
/* 图片相关事件 */

/** 服务端远程搜索功能 */
const hospitalArr = ref([]);
const hosLoading = ref(false);
const isRemote = ref(true);
function remoteMethod(row) {
  console.log(row);
  if (row) {
    form.value.name = row;
    hosLoading.value = true;
    hosBaseList(row).then((res) => {
      if (res.code == 0) {
        hosLoading.value = false;
        hospitalArr.value = res.data;
      } else {
        hosLoading.value = false;
      }
    });
  } else {
    hospitalArr.value = [];
  }
}
</script>
<style scoped lang="scss">
.dialog_row {
  .el-form-item {
    width: 90%;
    .el-select {
      width: 100%;
    }
  }
}
</style>
