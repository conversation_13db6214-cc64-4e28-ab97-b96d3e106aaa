import request from '@/utils/request'

// 查询租户管理列表
export function listTenant(query) {
  return request({
    url: '/system/tenant/list',
    method: 'get',
    params: query
  })
}

// 查询租户管理详细
export function getTenant(id) {
  return request({
    url: '/system/tenant/' + id,
    method: 'get'
  })
}

// 新增租户管理
export function addTenant(data) {
  return request({
    url: '/system/tenant',
    method: 'post',
    data: data
  })
}

// 修改租户管理
export function updateTenant(data) {
  return request({
    url: '/system/tenant/edit',
    method: 'post',
    data: data
  })
}

// 删除租户
export function delTenant(id) {
  return request({
    url: '/system/tenant/del/' + id,
    method: 'post'
  })
}

// 导出租户管理
export function exportTenant(query) {
  return request({
    url: '/system/tenant/export',
    method: 'get',
    params: query
  })
}

// 用户密码重置
export function resetUserPwd(id, password) {
  const data = {
    id,
    password
  }
  return request({
    url: '/system/tenant/resetPwd',
    method: 'post',
    data: data
  })
}

// 修改角色
export function grantAuth(data) {
  return request({
    url: '/system/tenant/grantAuth',
    method: 'post',
    data: data
  })
}

export function changeTenantStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/system/tenant/changeStatus',
    method: 'post',
    data: data
  })
}

