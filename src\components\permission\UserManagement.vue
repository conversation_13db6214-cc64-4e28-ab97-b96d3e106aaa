<template>
  <div class="user-management">
    <!-- 搜索和工具栏 -->
    <div class="search-bar">
      <v-row>
        <v-col cols="12" sm="6" md="3" lg="2">
          <v-text-field
            v-model="queryParams.userName"
            label="用户名称"
            placeholder="请输入用户名称"
            clearable
            hide-details
            density="compact"
            variant="outlined"
            @keyup.enter="handleQuery"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="3" lg="2">
          <v-text-field
            v-model="queryParams.phonenumber"
            label="手机号码"
            placeholder="请输入手机号码"
            clearable
            hide-details
            density="compact"
            variant="outlined"
            @keyup.enter="handleQuery"
          ></v-text-field>
        </v-col>
        <v-col cols="12" sm="6" md="3" lg="2">
          <v-select
            v-model="queryParams.status"
            :items="statusOptions"
            item-title="label"
            item-value="value"
            label="状态"
            clearable
            hide-details
            density="compact"
            variant="outlined"
          ></v-select>
        </v-col>
        <v-col cols="12" sm="6" md="3" lg="2">
          <v-btn color="primary" @click="handleQuery" class="mr-2">
            <v-icon start>mdi-magnify</v-icon>搜索
          </v-btn>
          <v-btn @click="resetQuery" variant="outlined">
            <v-icon start>mdi-refresh</v-icon>重置
          </v-btn>
        </v-col>
      </v-row>
    </div>

    <!-- 操作按钮区 -->
    <div class="table-toolbar">
      <v-btn color="primary" @click="handleAdd" class="mr-2">
        <v-icon start>mdi-plus</v-icon>新增
      </v-btn>
      <v-btn color="error" @click="handleBatchDelete" :disabled="selectedItems.length === 0" class="mr-2">
        <v-icon start>mdi-delete</v-icon>删除
      </v-btn>
      <v-btn color="success" @click="handleExport" class="mr-2">
        <v-icon start>mdi-file-export</v-icon>导出
      </v-btn>
      <v-btn @click="handleRefresh" variant="outlined">
        <v-icon start>mdi-refresh</v-icon>刷新
      </v-btn>
    </div>

    <!-- 数据表格 -->
    <v-data-table
      v-model="selectedItems"
      :headers="headers"
      :items="userList"
      :loading="loading"
      :items-per-page="queryParams.pageSize"
      :page="queryParams.pageNum"
      :server-items-length="total"
      item-value="userId"
      show-select
      class="elevation-1 mt-4"
      @update:page="handlePageChange"
      @update:items-per-page="handlePageSizeChange"
    >
      <!-- 状态列 -->
      <template #[`item.status`]="{ item }">
        <v-chip
          :color="item.status === '0' ? 'success' : 'error'"
          :text="item.status === '0' ? '正常' : '停用'"
          size="small"
        ></v-chip>
      </template>

      <!-- 操作列 -->
      <template #[`item.actions`]="{ item }">
        <v-btn icon size="small" color="primary" @click="handleEdit(item)" class="mr-1">
          <v-icon>mdi-pencil</v-icon>
        </v-btn>
        <v-btn icon size="small" color="error" @click="handleDelete(item)" class="mr-1">
          <v-icon>mdi-delete</v-icon>
        </v-btn>
        <v-btn icon size="small" color="warning" @click="handleResetPwd(item)">
          <v-icon>mdi-key</v-icon>
        </v-btn>
      </template>
    </v-data-table>

    <!-- 用户表单对话框 -->
    <v-dialog v-model="dialog.visible" max-width="700px">
      <v-card>
        <v-card-title>
          <span class="text-h6">{{ dialog.title }}</span>
        </v-card-title>
        <v-card-text>
          <v-form ref="formRef" v-model="formValid">
            <v-container>
              <v-row>
                <v-col cols="12" sm="6">
                  <v-text-field
                    v-model="form.userName"
                    label="用户名称"
                    required
                    :rules="[v => !!v || '用户名称不能为空']"
                    :readonly="dialog.type === 'edit'"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-text-field
                    v-model="form.nickName"
                    label="用户昵称"
                    required
                    :rules="[v => !!v || '用户昵称不能为空']"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-select
                    v-model="form.deptId"
                    :items="deptOptions"
                    item-title="deptName"
                    item-value="deptId"
                    label="所属部门"
                    required
                    :rules="[v => !!v || '所属部门不能为空']"
                  ></v-select>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-text-field
                    v-model="form.phonenumber"
                    label="手机号码"
                    required
                    :rules="[
                      v => !!v || '手机号码不能为空',
                      v => /^1[3-9]\d{9}$/.test(v) || '手机号码格式不正确'
                    ]"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-text-field
                    v-model="form.email"
                    label="邮箱"
                    required
                    :rules="[
                      v => !!v || '邮箱不能为空',
                      v => /.+@.+\..+/.test(v) || '邮箱格式不正确'
                    ]"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-radio-group v-model="form.status" row label="状态">
                    <v-radio label="正常" value="0"></v-radio>
                    <v-radio label="停用" value="1"></v-radio>
                  </v-radio-group>
                </v-col>
                <v-col cols="12" sm="6" v-if="dialog.type === 'add'">
                  <v-text-field
                    v-model="form.password"
                    label="密码"
                    type="password"
                    required
                    :rules="[v => !!v || '密码不能为空']"
                  ></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-select
                    v-model="form.roleIds"
                    :items="roleOptions"
                    item-title="roleName"
                    item-value="roleId"
                    label="角色"
                    multiple
                    chips
                    required
                    :rules="[v => v.length > 0 || '角色不能为空']"
                  ></v-select>
                </v-col>
                <v-col cols="12">
                  <v-textarea
                    v-model="form.remark"
                    label="备注"
                    rows="3"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" @click="submitForm">确定</v-btn>
          <v-btn @click="dialog.visible = false">取消</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 重置密码对话框 -->
    <v-dialog v-model="resetPwdDialog.visible" max-width="500px">
      <v-card>
        <v-card-title>
          <span class="text-h6">重置密码</span>
        </v-card-title>
        <v-card-text>
          <v-form ref="resetPwdFormRef" v-model="resetPwdFormValid">
            <v-container>
              <v-row>
                <v-col cols="12">
                  <v-text-field
                    v-model="resetPwdForm.password"
                    label="新密码"
                    type="password"
                    required
                    :rules="[v => !!v || '新密码不能为空']"
                  ></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model="resetPwdForm.confirmPassword"
                    label="确认密码"
                    type="password"
                    required
                    :rules="[
                      v => !!v || '确认密码不能为空',
                      v => v === resetPwdForm.password || '两次输入的密码不一致'
                    ]"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" @click="submitResetPwd">确定</v-btn>
          <v-btn @click="resetPwdDialog.visible = false">取消</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { userApi, roleApi, deptApi } from '../../utils/mock';

// 表格列定义
const headers = [
  { title: '用户编号', key: 'userId' },
  { title: '用户名称', key: 'userName' },
  { title: '用户昵称', key: 'nickName' },
  { title: '部门', key: 'dept.deptName' },
  { title: '手机号码', key: 'phonenumber' },
  { title: '状态', key: 'status' },
  { title: '创建时间', key: 'createTime' },
  { title: '操作', key: 'actions', sortable: false }
];

// 状态选项
const statusOptions = [
  { label: '正常', value: '0' },
  { label: '停用', value: '1' }
];

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  userName: '',
  phonenumber: '',
  status: ''
});

// 表单参数
const form = reactive({
  userId: undefined,
  userName: '',
  nickName: '',
  deptId: '',
  phonenumber: '',
  email: '',
  status: '0',
  password: '',
  roleIds: [],
  remark: ''
});

// 重置密码表单
const resetPwdForm = reactive({
  userId: undefined,
  password: '',
  confirmPassword: ''
});

// 对话框控制
const dialog = reactive({
  visible: false,
  title: '',
  type: '' // add 或 edit
});

// 重置密码对话框
const resetPwdDialog = reactive({
  visible: false
});

// 数据相关
const loading = ref(false);
const userList = ref([]);
const total = ref(0);
const selectedItems = ref([]);
const formRef = ref(null);
const formValid = ref(false);
const resetPwdFormRef = ref(null);
const resetPwdFormValid = ref(false);

// 部门和角色选项
const deptOptions = ref([]);
const roleOptions = ref([]);

// 获取用户列表
const getList = async () => {
  loading.value = true;
  try {
    const res = await userApi.list(queryParams);
    userList.value = res.data.rows;
    total.value = res.data.total;
  } catch (error) {
    console.error('获取用户列表失败', error);
    ElMessage.error('获取用户列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取部门列表
const getDeptOptions = async () => {
  try {
    const res = await deptApi.list({});
    // 递归处理部门数据为扁平结构
    const flattenDepts = (depts, prefix = '') => {
      let result = [];
      depts.forEach(dept => {
        const deptName = prefix ? `${prefix} / ${dept.deptName}` : dept.deptName;
        result.push({ ...dept, deptName });
        if (dept.children && dept.children.length > 0) {
          result = result.concat(flattenDepts(dept.children, deptName));
        }
      });
      return result;
    };
    deptOptions.value = flattenDepts(res.data);
  } catch (error) {
    console.error('获取部门列表失败', error);
    ElMessage.error('获取部门列表失败');
  }
};

// 获取角色列表
const getRoleOptions = async () => {
  try {
    const res = await roleApi.list({});
    roleOptions.value = res.data.rows;
  } catch (error) {
    console.error('获取角色列表失败', error);
    ElMessage.error('获取角色列表失败');
  }
};

// 处理查询
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

// 重置查询
const resetQuery = () => {
  queryParams.userName = '';
  queryParams.phonenumber = '';
  queryParams.status = '';
  handleQuery();
};

// 处理页码变化
const handlePageChange = (page) => {
  queryParams.pageNum = page;
  getList();
};

// 处理每页条数变化
const handlePageSizeChange = (pageSize) => {
  queryParams.pageSize = pageSize;
  getList();
};

// 处理刷新
const handleRefresh = () => {
  getList();
};

// 处理新增
const handleAdd = () => {
  resetForm();
  dialog.visible = true;
  dialog.title = '添加用户';
  dialog.type = 'add';
};

// 处理编辑
const handleEdit = async (row) => {
  resetForm();
  dialog.visible = true;
  dialog.title = '编辑用户';
  dialog.type = 'edit';
  
  try {
    const res = await userApi.get(row.userId);
    if (res.data) {
      Object.assign(form, res.data);
      form.roleIds = res.data.roles.map(role => role.roleId);
    }
  } catch (error) {
    console.error('获取用户详情失败', error);
    ElMessage.error('获取用户详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除用户"${row.userName}"吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await userApi.delete(row.userId);
      ElMessage.success('删除成功');
      getList();
    } catch (error) {
      console.error('删除用户失败', error);
      ElMessage.error('删除用户失败');
    }
  }).catch(() => {});
};

// 处理批量删除
const handleBatchDelete = () => {
  const userNames = selectedItems.value.map(item => item.userName).join('、');
  ElMessageBox.confirm(`确认删除用户"${userNames}"吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 实际应用中应该使用批量删除API
      for (const item of selectedItems.value) {
        await userApi.delete(item.userId);
      }
      ElMessage.success('批量删除成功');
      getList();
      selectedItems.value = [];
    } catch (error) {
      console.error('批量删除用户失败', error);
      ElMessage.error('批量删除用户失败');
    }
  }).catch(() => {});
};

// 处理导出
const handleExport = () => {
  // 实际应用中应该调用导出API
  ElMessage.success('导出成功');
};

// 处理重置密码
const handleResetPwd = (row) => {
  resetPwdForm.userId = row.userId;
  resetPwdForm.password = '';
  resetPwdForm.confirmPassword = '';
  resetPwdDialog.visible = true;
};

// 提交重置密码
const submitResetPwd = async () => {
  if (!resetPwdFormValid.value) {
    return;
  }
  
  try {
    await userApi.resetPwd(resetPwdForm.userId, resetPwdForm.password);
    ElMessage.success('密码重置成功');
    resetPwdDialog.visible = false;
  } catch (error) {
    console.error('密码重置失败', error);
    ElMessage.error('密码重置失败');
  }
};

// 提交表单
const submitForm = async () => {
  if (!formValid.value) {
    return;
  }
  
  try {
    if (dialog.type === 'add') {
      await userApi.add(form);
      ElMessage.success('添加成功');
    } else {
      await userApi.update(form);
      ElMessage.success('修改成功');
    }
    dialog.visible = false;
    getList();
  } catch (error) {
    console.error('提交表单失败', error);
    ElMessage.error('提交表单失败');
  }
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.reset();
  }
  Object.assign(form, {
    userId: undefined,
    userName: '',
    nickName: '',
    deptId: '',
    phonenumber: '',
    email: '',
    status: '0',
    password: '',
    roleIds: [],
    remark: ''
  });
};

// 页面初始化
onMounted(() => {
  getList();
  getDeptOptions();
  getRoleOptions();
});
</script>

<style scoped>
.user-management {
  width: 100%;
}

.search-bar {
  background-color: var(--card-background);
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px var(--shadow-color);
}

.table-toolbar {
  margin-bottom: 16px;
  display: flex;
  flex-wrap: wrap;
}

.table-toolbar .v-btn {
  margin-bottom: 8px;
}

:deep(.v-data-table) {
  background-color: var(--card-background) !important;
  border-radius: 4px;
  box-shadow: 0 1px 2px var(--shadow-color) !important;
}

:deep(.v-data-table-header) {
  background-color: var(--background-color);
}
</style> 