<template>
  <div class="markdown-demo">
    <h2>Markdown解析功能演示</h2>
    
    <div class="demo-section">
      <h3>输入区域</h3>
      <textarea 
        v-model="markdownText" 
        placeholder="请输入Markdown文本进行测试..."
        rows="10"
        class="markdown-input"
      ></textarea>
      <button @click="processMarkdown" class="process-btn">解析Markdown</button>
    </div>

    <div class="demo-section">
      <h3>解析结果</h3>
      <div class="markdown-output" v-html="processedHtml"></div>
    </div>

    <div class="demo-section">
      <h3>示例文本</h3>
      <button @click="loadExample" class="example-btn">加载示例</button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { marked } from 'marked';
import hljs from 'highlight.js/lib/common';

// 配置marked选项
marked.setOptions({
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value;
      } catch (err) {
        console.error('代码高亮失败:', err);
      }
    }
    return hljs.highlightAuto(code).value;
  },
  langPrefix: 'hljs language-',
  breaks: true,
  gfm: true,
  tables: true,
  sanitize: false,
  smartLists: true,
  smartypants: true
});

const markdownText = ref('');
const processedHtml = ref('');

// 处理思考内容的函数
const processThinkingContent = (content) => {
  if (!content) return '';
  return content.trim().replace(/\n/g, '<br>');
};

// 处理Markdown内容
const processContent = (text) => {
  if (!text) return '';

  let processedText = text;

  // 处理思考块
  const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
  const thinkingRegex = /<thinking>([\s\S]*?)<\/thinking>/g;
  const thinkingBracketRegex = /\[THINKING\]([\s\S]*?)\[\/THINKING\]/g;

  // 处理 <think> 标签
  if (thinkRegex.test(processedText)) {
    processedText = processedText.replace(thinkRegex, (match, thinkContent) => {
      const cleanedContent = processThinkingContent(thinkContent);
      return `<div style="background-color: #f5f5f5; color: #888; padding: 12px 16px; margin: 8px 0; border-radius: 6px; font-style: italic; border-left: 3px solid #ddd;">
        ${cleanedContent}
      </div>`;
    });
  }

  // 处理 <thinking> 标签
  if (processedText.includes('<thinking>')) {
    processedText = processedText.replace(thinkingRegex, (match, thinkContent) => {
      const cleanedContent = processThinkingContent(thinkContent);
      return `<div style="background-color: #f5f5f5; color: #888; padding: 12px 16px; margin: 8px 0; border-radius: 6px; font-style: italic; border-left: 3px solid #ddd;">
        ${cleanedContent}
      </div>`;
    });
  }

  // 处理 [THINKING] 标签
  if (processedText.includes('[THINKING]')) {
    processedText = processedText.replace(thinkingBracketRegex, (match, thinkContent) => {
      const cleanedContent = processThinkingContent(thinkContent);
      return `<div style="background-color: #f5f5f5; color: #888; padding: 12px 16px; margin: 8px 0; border-radius: 6px; font-style: italic; border-left: 3px solid #ddd;">
        ${cleanedContent}
      </div>`;
    });
  }

  // 处理未闭合的思考块
  if (processedText.includes('<think>') && !processedText.includes('</think>')) {
    processedText = processedText.replace(/<think>([^]*)$/, (match, thinkContent) => {
      const cleanedContent = processThinkingContent(thinkContent || '');
      return `<div style="background-color: #f5f5f5; color: #888; padding: 12px 16px; margin: 8px 0; border-radius: 6px; font-style: italic; border-left: 3px solid #ddd;">
        ${cleanedContent || '思考中...'}
      </div>`;
    });
  }

  // 使用marked解析Markdown内容
  try {
    processedText = marked.parse(processedText);
  } catch (error) {
    console.error('marked解析失败:', error);
  }

  return `<div style="max-width: 100%; word-wrap: break-word; overflow-wrap: break-word;">${processedText}</div>`;
};

const processMarkdown = () => {
  processedHtml.value = processContent(markdownText.value);
};

const loadExample = () => {
  markdownText.value = `# 2025年6月东胜区文明城市创建报告分析

<think>
我需要分析这份报告的内容，包括总体情况、问题分类、重点区域等方面。这是一个复杂的分析任务，需要仔细梳理数据和问题。
</think>

## 一、总体情况

1. **案件总量与整改率**：
   - 全市（含东胜区）共上报案件793件，其中市级文明办督察案件776件，旗区自查17件。
   - 东胜区整改率为85.25%（完成676件），但仍有部分问题未解决。

2. **巡查范围**：
   - 涉及居民小区、背街小巷、主次干道等7类点位，共195个具体位置。

---

## 二、主要问题分类与占比

| 问题类别 | 数量（件） | 占比 | 主要表现 |
|----------|------------|------|----------|
| 市容市貌类 | 272 | 34.30% | **乱堆乱放、不文明养宠、占道经营**等 |
| 基础设施类 | 206 | 25.98% | 消防器材缺失/损坏等问题突出 |
| 宣传展示类 | 117 | 14.75% | **公益广告破损、字体缺损** |

这里是正常的内容，不在思考块内。

## 三、代码示例

\`\`\`javascript
// 数据分析函数
function analyzeData(cases) {
  const totalCases = cases.length;
  const completedCases = cases.filter(c => c.status === 'completed').length;
  const completionRate = (completedCases / totalCases * 100).toFixed(2);

  return {
    total: totalCases,
    completed: completedCases,
    rate: completionRate + '%'
  };
}
\`\`\`

## 四、改进建议

- **强化基层责任体系**：要求街道、社区制定月度巡查计划
- **分类治理重点问题**：对市容类问题开展专项整治
- **提升宣传展示质量**：定期检查公益广告维护情况

> 东胜区在市级层面整改率较高，但基层单位执行力严重不足。

**总结**：需通过制度化管理压实责任，实现长效创城目标。`;
  
  processMarkdown();
};
</script>

<style scoped>
.markdown-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.demo-section {
  margin-bottom: 30px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
}

.markdown-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  resize: vertical;
}

.process-btn, .example-btn {
  background-color: #1976d2;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.process-btn:hover, .example-btn:hover {
  background-color: #1565c0;
}

.markdown-output {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 20px;
  background-color: #fafafa;
  min-height: 200px;
}

/* Markdown样式 */
.markdown-output h1, .markdown-output h2, .markdown-output h3 {
  color: #333;
  margin-top: 20px;
  margin-bottom: 10px;
}

.markdown-output table {
  border-collapse: collapse;
  width: 100%;
  margin: 15px 0;
}

.markdown-output th, .markdown-output td {
  border: 1px solid #ddd;
  padding: 8px 12px;
  text-align: left;
}

.markdown-output th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.markdown-output blockquote {
  border-left: 4px solid #ddd;
  margin: 15px 0;
  padding-left: 15px;
  color: #666;
  font-style: italic;
}

.markdown-output code {
  background-color: #f4f4f4;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.markdown-output pre {
  background-color: #f8f8f8;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  overflow-x: auto;
}

.markdown-output pre code {
  background: none;
  padding: 0;
}
</style>
