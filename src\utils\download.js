// utils/download.js
import axios from 'axios'
import { ElMessage } from 'element-plus'

export const downloadFile = async (id) => {
    try {
        const response = await axios({
            url: `/app/api/system/filesApi/download/${id}`,
            method: 'GET',
            responseType: 'blob',
        })

        // 获取文件名
        const contentDisposition = response.headers['content-disposition']
        let fileName = '下载文件'
        let fileExtension = ''
        
        // 从响应头中提取文件名
        if (contentDisposition) {
            const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
            if (fileNameMatch && fileNameMatch[1]) {
                const extractedName = decodeURIComponent(fileNameMatch[1].replace(/['"]/g, ''))
                if (extractedName) {
                    fileName = extractedName
                }
            }
        }
        
        // 从文件名或Content-Type中获取文件扩展名
        const fileNameParts = fileName.split('.')
        if (fileNameParts.length > 1) {
            fileExtension = fileNameParts.pop().toLowerCase()
        } else {
            // 如果文件名中没有扩展名，从Content-Type中获取
            const contentType = response.headers['content-type']
            if (contentType) {
                // 根据常见MIME类型映射到对应的文件扩展名
                if (contentType.includes('application/pdf')) {
                    fileExtension = 'pdf'
                } else if (contentType.includes('image/jpeg')) {
                    fileExtension = 'jpg'
                } else if (contentType.includes('image/png')) {
                    fileExtension = 'png'
                } else if (contentType.includes('text/plain')) {
                    fileExtension = 'txt'
                } else if (contentType.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document')) {
                    fileExtension = 'docx'
                } else if (contentType.includes('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')) {
                    fileExtension = 'xlsx'
                } else if (contentType.includes('application/vnd.openxmlformats-officedocument.presentationml.presentation')) {
                    fileExtension = 'pptx'
                }
                
                // 如果从Content-Type中确定了扩展名，添加到文件名中
                if (fileExtension && !fileName.endsWith(`.${fileExtension}`)) {
                    fileName = `${fileName}.${fileExtension}`
                }
            }
        }

        // 创建下载
        const blob = new Blob([response.data], { type: response.headers['content-type'] })
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(blob)
        link.download = fileName
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(link.href)

        ElMessage.success('下载成功')
        return true
    } catch (error) {
        console.error('下载失败:', error)
        ElMessage.error(error.response?.data?.message || '下载失败')
        return false
    }
}