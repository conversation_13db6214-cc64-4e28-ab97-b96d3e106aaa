<template>
  <div class="file-list-container">
    <!-- 知识库内容区域 - 上半部分 -->
    <div class="main-container1">
      <!-- 知识库模块 -->
      <div class="knowledge-header">
        <div class="knowledge-title">{{ currentKnowledgeBasePath }}</div>
        <div class="knowledge-actions">
          <v-btn v-if="canShowUploadButton" color="primary" class="upload-btn" prepend-icon="mdi-plus" @click="openUploadDialog">上传文件</v-btn>
          <v-btn variant="text" class="ai-btn" style="border: 1px solid #1677ff;" color="grey-darken-1"
            @click="scrollToChatContainer">智能问答</v-btn>
        </div>
      </div>

      <!-- 修改文件列表样式 -->
      <div class="file-list" ref="fileListContainer" @scroll="handleFileListScroll" v-infinite-scroll="loadMoreFiles"
        :infinite-scroll-disabled="loading || noMoreFiles" :infinite-scroll-distance="10"
        :infinite-scroll-immediate="false" :infinite-scroll-delay="200">
        <!-- 修改文件列表头部，添加操作按钮 -->
        <div class="file-list-header">
          <div class="file-list-header-left">
            <span>共{{ fileList.length }}个文件/{{ formattedTotalFileSize }}</span>
          </div>
          <div style="display: flex;align-items: center;justify-content: space-between;gap: 16px;">
            <v-checkbox v-if="selectableFiles.length > 0" v-model="selectAllFiles" hide-details class="select-all-checkbox"
              label="全选" @change="toggleSelectAllFiles" :indeterminate="selectAllFiles === null"></v-checkbox>
            <!-- 当有文件被选中时显示的操作按钮 -->
            <div class="batch-actions" v-if="hasSelectedFiles">
              <!-- 批量共享/取消共享按钮 -->
              <button v-if="hasAnySharedFilesSelected" class="batch-action-btn" title="取消共享"
                @click="batchShareFiles('REPO')">
                <v-icon>mdi-link-off</v-icon>
              </button>
              <button v-if="hasAnyPrivateFilesSelected" class="batch-action-btn" title="分享"
                @click="batchShareFiles('UNIT')">
                <v-icon>mdi-share-variant-outline</v-icon>
              </button>
              <button class="batch-action-btn" title="删除" v-if="canShowBatchDeleteButton"
                @click="deleteSelectedFiles">
                <v-icon>mdi-delete-outline</v-icon>
              </button>
              <button class="batch-action-btn" title="下载" @click="downloadSelectedFiles">
                <v-icon>mdi-download-outline</v-icon>
              </button>
            </div>
          </div>
        </div>

        <!-- 缺省图 - 当没有文件数据时显示 -->
        <div class="empty-file-list" v-if="fileList.length === 0 && !loading">
          <div class="empty-illustration">
            <div class="empty-icon">
              <v-icon size="80" color="primary" class="empty-main-icon">mdi-file-document-outline</v-icon>
              <v-icon size="40" color="#FFB800" class="empty-sub-icon">mdi-magnify</v-icon>
            </div>
          </div>
          <div class="empty-text">暂无文件</div>
          <div class="empty-subtext" v-if="canShowUploadButton">当前知识库中暂时没有任何文件，可以点击"上传文件"添加</div>
          <div class="empty-subtext" v-else>当前知识库中暂时没有任何文件</div>
        </div>

        <!-- 文件项 - 新样式 -->
        <div class="file-item" v-for="(file, index) in fileList" :key="index">
          <div class="file-item-content">
            <!-- 文件图标 -->
            <div class="file-icon"
              style="display: flex; align-items: center; justify-content: center; background-color: rgba(0, 0, 0, 0.05); border-radius: 8px; padding: 8px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
              <v-icon size="26" :color="getFileIconColor(file.fileName || '')">{{ getFileIconByExtension(file.fileName || '') }}</v-icon>
            </div>

            <!-- 文件信息 -->
            <div class="file-info">
              <!-- 文件名 - 点击预览文件 -->
              <div class="file-name"
                :class="{ 'has-warning': file.status == 'PARSING', 'clickable': file.status == 'PARSE_SUCCESS' }"
                @click="file.status == 'PARSE_SUCCESS' ? viewFileInline(file) : null"
                :title="file.status == 'PARSE_SUCCESS' ? '点击预览原始文件' : null">
                {{ file.fileName }}
                <v-icon v-if="file.status == 'PARSE_SUCCESS'" size="small" color="#1976d2"
                  class="preview-icon">mdi-file-eye-outline</v-icon>
              </div>

              <!-- 上传状态显示 -->
              <div v-if="file.status == 'UPLOADING' || file.progress < 100" class="file-upload-status">
                <div class="upload-status-text" :class="{'animating': file.progress >= 99 && file.progress < 100}">{{ getFileUploadStatusText(file) }}</div>
                <div class="upload-progress-container">
                  <v-progress-linear v-model="file.progress" color="primary" height="6"
                    class="upload-progress" :active="file.status === 'UPLOADING'" :indeterminate="file.status === 'UPLOADING' && file.progress < 5"></v-progress-linear>
                  <v-btn v-if="file.status == 'UPLOADING'" size="x-small" color="error" class="cancel-upload-btn" @click="deleteFile(file)">
                    取消上传
                  </v-btn>
                </div>
              </div>

              <!-- 上传失败状态 -->
              <div v-if="file.status == 'UPLOAD_ERROR' || file.status == 'UPLOAD_TIMEOUT'" class="file-upload-error">
                <v-icon size="small" color="error" class="error-icon">mdi-alert-circle</v-icon>
                <span class="error-text">{{ file.status == 'UPLOAD_TIMEOUT' ? '上传超时' : '上传失败' }}</span>
                <v-btn size="x-small" color="primary" class="ml-2 retry-btn" @click="retryUpload(file)">重新上传</v-btn>
              </div>

              <!-- 文件基本信息 -->
              <div class="file-meta">
                <span class="meta-item"><v-icon size="small" color="#9E9E9E">mdi-file-outline</v-icon> 文件大小: {{ formatFileSize(file.fileSize || 0) }}</span>
                <span class="meta-item"><v-icon size="small" color="#9E9E9E">mdi-clock-outline</v-icon> 创建时间: {{ file.createTime || '-' }}</span>
                <span class="meta-item"><v-icon size="small" color="#9E9E9E">mdi-account-outline</v-icon> 上传者: {{ file.createBy || '-' }}</span>
                <span class="meta-item"><v-icon size="small" color="#9E9E9E">mdi-folder-outline</v-icon> 文件位置: {{ getFileLocation(file) || '-' }}</span>
              </div>

              <!-- 文件描述 -->
              <div class="file-description">
                <div class="description-label">文档摘要:</div>   
                <div style="width: 86%;margin-left: 10px;">
                  <Ellipsis :line="2">
                    {{ file.documentSummary ? file.documentSummary: '暂无摘要' }}
                    <template #tooltip>
                      <div style="text-align: center">
                        {{ file.documentSummary ? file.documentSummary: '暂无摘要' }}
                      </div>
                    </template>
                  </Ellipsis>
                </div> 
              </div>
              <div class="file-description" v-if="file.keywords">
                <div class="description-label">标签:</div>   
                <div style="width: 86%;margin-left: 10px;">
                  <!-- 文件标签 -->
                  <div class="file-tags" v-if="file.keywords">
                    <span class="tag"
                      v-for="(tag, tagIndex) in (typeof file.keywords === 'string' ? file.keywords.split(',').slice(0, 5) : file.keywords)"
                      :key="tagIndex">{{ tag }}</span>
                  </div>
                  <div class="file-tags" v-else>
                    <span class="description-label">暂无标签</span>
                  </div>
                </div> 
              </div>
            </div>
            
            <div style="display: flex; justify-content: space-between;flex-direction: column;align-items: flex-end;">
              <!-- 添加文件状态标签 -->
              <div style="display: flex; gap: 8px; margin-bottom: 8px;">
                <!-- 文件权限标签 -->
                <div :style="{
                  backgroundColor: (file.permissionType == 'REPO' || file.operationPermission == 'EDIT') ? '#FCE7DC' : '#E1F5FE',
                  color: (file.permissionType == 'REPO' || file.operationPermission == 'EDIT') ? '#F34343' : '#0288D1',
                  width: '72px',
                  height: '32px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }">
                  {{ (file.permissionType == 'REPO' || file.operationPermission == 'EDIT') ? '部门私有' : '单位共享' }}
                </div>

                <!-- 文件状态标签 -->
                <div :style="{
                  backgroundColor: getStatusBgColor(file.status),
                  color: getStatusTextColor(file.status),
                  minWidth: '72px',
                  height: '32px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  padding: '0 8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }">
                  {{ getStatusText(file.status) }}
                </div>
              </div>
              
              <!-- 文件操作按钮 -->
              <div class="file-actions">
                <slot name="file-actions" :file="file" :index="index">
                  <!-- 默认操作按钮，可以被父组件覆盖 -->
                </slot>
                
                <v-checkbox v-if="selectableFiles.includes(file)" 
                  v-model="file.selected"
                  hide-details class="file-checkbox" @change="handleFileSelectionChange"></v-checkbox>
              </div>
            </div>
          </div>
        </div>

        <!-- 加载更多指示器 -->
        <div v-if="loading" class="loading-more">
          <el-skeleton style="width: 100%" :rows="3" animated />
        </div>

        <!-- 下拉刷新指示器 -->
        <div v-if="isRefreshing" class="refresh-indicator">
          <v-progress-circular indeterminate color="primary" size="24" width="2"></v-progress-circular>
          <span class="refresh-text">正在刷新...</span>
        </div>

        <!-- 没有更多数据提示 -->
        <div v-if="noMoreFiles && fileList.length > 0" class="no-more-files">
          <span>—— 已经到底了 ——</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Ellipsis } from 'vue-amazing-ui'
import 'vue-amazing-ui/es/ellipsis/Ellipsis.css'
import { 
  getFileIconByExtension, 
  getFileIconColor, 
  formatFileSize, 
  getStatusText, 
  getStatusBgColor, 
  getStatusTextColor,
  getFileLocation
} from '@/utils/fileUtils'
import { getFileUploadStatusText } from '@/utils/chatUtils'

// Props
const props = defineProps({
  currentKnowledgeBasePath: {
    type: String,
    default: ''
  },
  canShowUploadButton: {
    type: Boolean,
    default: false
  },
  fileList: {
    type: Array,
    default: () => []
  },
  formattedTotalFileSize: {
    type: String,
    default: '0 B'
  },
  selectableFiles: {
    type: Array,
    default: () => []
  },
  selectAllFiles: {
    type: [Boolean, null],
    default: false
  },
  hasSelectedFiles: {
    type: Boolean,
    default: false
  },
  hasAnySharedFilesSelected: {
    type: Boolean,
    default: false
  },
  hasAnyPrivateFilesSelected: {
    type: Boolean,
    default: false
  },
  canShowBatchDeleteButton: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  isRefreshing: {
    type: Boolean,
    default: false
  },
  noMoreFiles: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'openUploadDialog',
  'scrollToChatContainer',
  'handleFileListScroll',
  'loadMoreFiles',
  'toggleSelectAllFiles',
  'batchShareFiles',
  'deleteSelectedFiles',
  'downloadSelectedFiles',
  'viewFileInline',
  'deleteFile',
  'retryUpload',
  'handleFileSelectionChange'
])

// Methods
const openUploadDialog = () => {
  emit('openUploadDialog')
}

const scrollToChatContainer = () => {
  emit('scrollToChatContainer')
}

const handleFileListScroll = (event) => {
  emit('handleFileListScroll', event)
}

const loadMoreFiles = () => {
  emit('loadMoreFiles')
}

const toggleSelectAllFiles = () => {
  emit('toggleSelectAllFiles')
}

const batchShareFiles = (type) => {
  emit('batchShareFiles', type)
}

const deleteSelectedFiles = () => {
  emit('deleteSelectedFiles')
}

const downloadSelectedFiles = () => {
  emit('downloadSelectedFiles')
}

const viewFileInline = (file) => {
  emit('viewFileInline', file)
}

const deleteFile = (file) => {
  emit('deleteFile', file)
}

const retryUpload = (file) => {
  emit('retryUpload', file)
}

const handleFileSelectionChange = () => {
  emit('handleFileSelectionChange')
}
</script>

<style scoped>
/* 文件列表容器样式 */
.file-list-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.main-container1 {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 知识库头部样式 */
.knowledge-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.knowledge-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.knowledge-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.upload-btn {
  background-color: #1677ff !important;
  color: white !important;
}

.ai-btn {
  border: 1px solid #1677ff !important;
  color: #1677ff !important;
}

/* 文件列表样式 */
.file-list {
  flex: 1;
  overflow-y: auto;
  background-color: #f8f9fa;
}

.file-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.file-list-header-left {
  font-size: 14px;
  color: #666;
}

.select-all-checkbox {
  margin-right: 16px;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.batch-action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 6px;
  background-color: #f5f5f5;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.batch-action-btn:hover {
  background-color: #e0e0e0;
  color: #333;
}

/* 空状态样式 */
.empty-file-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-illustration {
  position: relative;
  margin-bottom: 24px;
}

.empty-icon {
  position: relative;
  display: inline-block;
}

.empty-main-icon {
  display: block;
}

.empty-sub-icon {
  position: absolute;
  bottom: -8px;
  right: -8px;
  background-color: white;
  border-radius: 50%;
  padding: 4px;
}

.empty-text {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.empty-subtext {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* 文件项样式 */
.file-item {
  background-color: #fff;
  margin: 8px 16px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.file-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.file-item-content {
  display: flex;
  padding: 16px;
  gap: 16px;
}

.file-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-name.clickable {
  cursor: pointer;
  color: #1976d2;
}

.file-name.clickable:hover {
  text-decoration: underline;
}

.file-name.has-warning {
  color: #ff9800;
}

.preview-icon {
  opacity: 0.7;
}

.file-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.file-description {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}

.description-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  min-width: 60px;
}

.file-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

/* 上传状态样式 */
.file-upload-status {
  margin: 8px 0;
}

.upload-status-text {
  font-size: 12px;
  color: #1976d2;
  margin-bottom: 4px;
  font-weight: 500;
}

.upload-status-text.animating {
  color: #f44336;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.upload-progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-progress {
  flex: 1;
  border-radius: 4px;
}

.cancel-upload-btn {
  font-size: 11px !important;
  height: 24px !important;
}

.file-upload-error {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #ffebee;
  padding: 4px 8px;
  border-radius: 4px;
  margin: 8px 0;
  width: fit-content;
}

.error-text {
  font-size: 12px;
  color: #f44336;
  font-weight: 500;
}

.retry-btn {
  font-weight: 500;
}

/* 文件操作区域 */
.file-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.file-checkbox {
  margin-top: auto;
}

/* 加载状态样式 */
.loading-more {
  padding: 20px;
  background-color: #fff;
  margin: 8px 16px;
  border-radius: 8px;
}

.refresh-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  background-color: #fff;
  margin: 8px 16px;
  border-radius: 8px;
}

.refresh-text {
  font-size: 14px;
  color: #666;
}

.no-more-files {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .knowledge-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .knowledge-actions {
    justify-content: center;
  }

  .file-item-content {
    flex-direction: column;
    gap: 12px;
  }

  .file-meta {
    flex-direction: column;
    gap: 8px;
  }

  .batch-actions {
    flex-wrap: wrap;
  }
}
</style>
