<template>
    <el-dialog v-model="visibleExpected" width="1100px" top="5vh" append-to-body @close="getClose">
        <template #header="{ titleId,titleClass  }">
            <div class="my-header" :id="titleId">
                <div :class="titleClass">
                    <el-icon :size="20" color="#0052D9">
                        <StarFilled />
                    </el-icon>期望排班列表
                </div>
            </div>
        </template>
        <div style="position: relative">
            <el-table class="table_box" :data="tableData" style="width: 100%; height: 500px; padding-bottom: 4px"
                :max-height="tableHeight" border :header-row-style="{ height: '60px' }" :cell-style="cellStyle"
                :row-style="{ maxHeight: '50px' }" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column :prop="item.prop" :label="item.label" v-for="(item, index) in tableHeader" :key="index"
                    width="100" fixed align="center">
                </el-table-column>
                <el-table-column :prop="item.prop" :label="item.label" v-for="(item, index) in dateHeader" :key="index"
                    :width="130" align="center">
                    <template #default="scope">
                        <div class="editable-row-span" v-for="(citem, cindex) in scope.row[item.prop]" :key="cindex">
                            <span>{{ citem.shiftsName }} <span v-if="citem.postName">({{ citem.postName }})</span>
                            </span>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="colseExpected">取消</el-button>
                <el-button type="primary" @click="addTable">填入表格</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
    import contextMenu from "./contextMenu.vue";
    import largeMenu from "./largeMenu.vue";
    import miniMenu from "./miniMenu.vue";
    import {
        arrWorkList,
        desireWorkList
    } from "@/api/arr/scheduleSheet";
    import {
        listWorkShifts
    } from "@/api/arr/workShifts";
    import {
        listPost
    } from "@/api/system/post";
    import {
        listWorkShiftsPost
    } from "@/api/arr/workShiftsPost";
import { ref } from "vue";

    const {
        proxy
    } = getCurrentInstance();
    const props = defineProps({
        visibleExpected: {
            type: Boolean,
        },
        dates: {
            type: Array,
        },
        appTop: {
            type: Number,
        },
        deptId: {
            type: String,
        }
    });

    const tableHeight = ref(window.innerHeight - 140)
    const data = reactive({
        form: {},
        queryParams: {
            deptId: props.deptId,
        },
        rules: {},
    });
    const {
        queryParams,
        form,
        rules
    } = toRefs(data);

    /* 默认数据 */
    const tableHeader = ref([{
        prop: "nickName",
        label: "姓名",
        editable: false,
        flag: true,
    }]);
    const tableData = ref([])
    const dateHeader = ref([]);
    const dateRang = ref([]);

    // 监听时间范围(dates)的变化，有变化则计算遍历始末日期间的所有日期
    watch(
        () => props.dates,
        (newValue, oldValue) => {
            if (newValue) {
                let newDates = JSON.parse(JSON.stringify(newValue));
                let newList = [];

                dateRang.value = [];
                for (let index in newDates) {
                    let obj = {
                        prop: newDates[index],
                        // prop: "recordMap",
                        label: getWeekday(newDates[index]),
                        editable: true,
                        flag: true,
                    };
                    newList.push(obj);
                    if (index == 0) {
                        dateRang.value.push(newDates[index]);
                    } else if (index == newDates.length - 1) {
                        dateRang.value.push(newDates[index]);
                    }
                }
                dateHeader.value = newList;
                getList();
            }
        }
    );
    // 监听期望排版列表弹窗打开关闭
    const visibleExpected = ref(false)
    watch(
        () => props.visibleExpected,
        (newValue, oldValue) => {
            if (newValue) {
                // console.log(newValue)
                visibleExpected.value = newValue;
            }
        }
    );
    const emit = defineEmits(["closeSetExpected", "fillInTable"]);
    // 关闭规则弹窗组件
    function getClose() {
        emit("closeSetExpected");
    }

    function colseExpected() {
        visibleExpected.value = false
    }
    /* 默认一周日期范围计算 start */
    // 计算距离今天七天后的日期
    const nextday = (e) => {
        let date2 = new Date(new Date());
        date2.setDate(new Date().getDate() + 6);
        return date2;
    };

    /* 计算当前日期的星期数 */
    const getWeekday = (dateStr) => {
        const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
        const dateObj = new Date(dateStr);
        return dateStr + " " + weekdays[dateObj.getDay()];
    };

    /* 计算默认一周的日期 */
    const ComputeInterval = () => {
        let list = [];
        let currentDate = new Date();
        while (currentDate <= nextday()) {
            list.push(
                `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(
          2,
          "0"
        )}-${String(currentDate.getDate()).padStart(2, "0")}`
            );
            currentDate.setDate(currentDate.getDate() + 1);
        }
        let newList = [];
        for (let index in list) {
            let obj = {
                prop: list[index],
                // prop: "recordMap",
                label: getWeekday(list[index]),
                editable: true,
                flag: true,
            };
            newList.push(obj);
            if (index == 0) {
                dateRang.value.push(list[index]);
            } else if (index == list.length - 1) {
                dateRang.value.push(list[index]);
            }
        }
        dateHeader.value = newList;
        getList();
    };

    ComputeInterval();
    /* 默认一周日期范围计算 end */

    const currentValue = ref("");

    //单元格的 style 的回调方法
    const cellStyle = ({
        row,
        column,
        rowIndex,
        columnIndex
    }) => {
        if (columnIndex < 1) {
            return {
                backgroundColor: "#f8f8f9"
            };
        } else if (`${row.id}_${column.property}` === currentValue.value) {
            return {
                border: "1px solid #597EF7"
            };
        }
    };

    /* 点击单元格相关事件 end*/

    /** 查询排班申请休假列表 */
    function getList() {
        // loading.value = true;
        desireWorkList(proxy.addDateRange(queryParams.value, dateRang.value)).then((response) => {
            console.log('期望排版列表', response.data);
            response.data.map((item, index) => {
                item["id"] = index;
                // item.nickName = "名字"+item["id"]
                if (item.recordMap) {
                    Object.keys(item.recordMap).map((child) => {
                        item[child] = item.recordMap[child];
                    });
                }
            });
            tableData.value = response.data;
        });
    }


    /* 页面相关内容接口 start */

    const tableParams = {
        pageNum: 1,
        pageSize: 100,
    };
    /** 查询班次管理列表 */
    const classesList = ref([]);

    function getClasses() {
        listWorkShifts(tableParams.value).then((response) => {
            classesList.value = response.rows;
        });
    }

    /** 查询岗位列表 */
    const postList = ref([]);

    function getPostList() {
        listPost(tableParams.value).then((response) => {
            postList.value = response.rows;
        });
    }

    /** 查询排班班次岗位组合列表 */
    const combinationsList = ref([]);

    function getCombinations() {
        listWorkShiftsPost(tableParams.value).then((response) => {
            combinationsList.value = response.rows;
        });
    }

    // 多选框选中数据
    const selectTableData = ref([])
    function handleSelectionChange(selection) {
        selectTableData.value = selection
    }
    // 全部填入表格
    function addTable() {
        if(selectTableData.value.length == 0) {
            return proxy.$modal.msgWarning("请选择期望排班列表");
        } else {
            emit("fillInTable", JSON.parse(JSON.stringify(tableData.value)))
        }
    }

    getClasses();
    getPostList();
    getCombinations();
    /* 页面相关内容接口 end */
</script>

<style lang="scss" scoped>
    .table_box {
        th {
            padding: 0 !important;
            height: 48px;
            line-height: 48px;
        }

        td {
            padding: 0 !important;
            height: 48px;
            line-height: 48px;
        }
    }

    .el-dialog__title {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center
    }
</style>