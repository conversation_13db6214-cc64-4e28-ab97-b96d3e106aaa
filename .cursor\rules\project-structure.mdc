---
description: 
globs: 
alwaysApply: false
---
# Project Structure Guide

This is a Vue.js-based knowledge base web application. Here's the key structure:

## Core Files
- [main.js](mdc:src/main.js) - Application entry point
- [App.vue](mdc:src/App.vue) - Root Vue component
- [vite.config.js](mdc:vite.config.js) - Vite build configuration

## Key Directories
- `src/` - Main application source code
  - `api/` - API service integrations
  - `assets/` - Static assets (images, fonts, etc.)
  - `components/` - Reusable Vue components
  - `directive/` - Custom Vue directives
  - `layout/` - Layout components
  - `plugins/` - Vue plugins and extensions
  - `router/` - Vue Router configuration
  - `store/` - Vuex store modules
  - `utils/` - Utility functions
  - `views/` - Page components

## Configuration
- `.env.*.development/staging/production` - Environment-specific configurations
- [settings.js](mdc:src/settings.js) - Application settings
- [permission.js](mdc:src/permission.js) - Authentication and authorization logic

