<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px" @submit.native.prevent>
      <el-form-item label="用户id" prop="userId" v-if="false">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户id"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属护理单元/科室id" prop="deptId" v-if="false">
        <el-input
          v-model="queryParams.deptId"
          placeholder="请输入所属护理单元/科室id"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属护理单元/科室名称" prop="deptName" label-width="160px" v-if="false">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入所属护理单元/科室名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime" v-if="false">
        <!-- <el-date-picker clearable
          v-model="queryParams.startTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择开始时间">
        </el-date-picker> -->
        <el-time-picker v-model="queryParams.startTime" placeholder="请选择开始时间" />
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime" v-if="false">
        <!-- <el-date-picker clearable
          v-model="queryParams.endTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择结束时间">
        </el-date-picker> -->
        <el-time-picker v-model="queryParams.endTime" placeholder="请选择开始时间" />
      </el-form-item>
      <el-form-item label="班次日期" prop="shiftsDate" v-if="false">
        <!-- <el-input
          v-model="queryParams.shiftsDate"
          placeholder="请输入班次日期"
          clearable
          @keyup.enter="handleQuery"
        /> -->
        <el-date-picker clearable
          v-model="queryParams.shiftsDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择班次日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="班次id" prop="shiftsId" v-if="false">
        <el-input
          v-model="queryParams.shiftsId"
          placeholder="请输入班次id"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="班次名称" prop="shiftsName" style="width:260px;">
        <el-input
          v-model="queryParams.shiftsName"
          placeholder="请输入班次名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请理由" prop="reason" v-if="false">
        <el-input
          v-model="queryParams.reason"
          placeholder="请输入申请理由"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="排班申请类别" prop="takeType" v-if="false">
        <el-select v-model="queryParams.takeType" placeholder="请选择排班申请类别" clearable>
          <el-option
            v-for="dict in arr_holiday_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态(是否有效)" prop="status" v-if="false">
        <el-select v-model="queryParams.status" placeholder="请选择状态(是否有效)" clearable>
          <el-option
            v-for="dict in sys_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="拒绝日期" prop="rejectDate" v-if="false">
        <el-date-picker clearable
          v-model="queryParams.rejectDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择拒绝日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="取消日期" prop="cancelDate" v-if="false">
        <el-date-picker clearable
          v-model="queryParams.cancelDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择取消日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['arr:takeHoliday:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['arr:takeHoliday:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['arr:takeHoliday:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['arr:takeHoliday:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="takeHolidayList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" v-if="false"/>
      <el-table-column label="用户id" align="center" prop="userId" v-if="false"/>
      <el-table-column label="所属护理单元/科室id" align="center" prop="deptId"  v-if="false"/>
      <el-table-column label="所属护理单元/科室名称" align="center" prop="deptName" />
      <el-table-column label="班次日期" align="center" prop="shiftsDate" width="140" v-if="false">
        <template #default="scope">
          <span>{{ parseTime(scope.row.shiftsDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="班次id" align="center" prop="shiftsId" v-if="false"/>
      <el-table-column label="班次名称" align="center" prop="shiftsName" width="140"/>
      <el-table-column label="开始时间" align="center" prop="startTime" width="120">
        <!-- <template #default="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d}') }}</span>
        </template> -->
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="120">
        <!-- <template #default="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
        </template> -->
      </el-table-column>
      <el-table-column label="申请理由" align="center" prop="reason" />
      <el-table-column label="排班申请类别" align="center" prop="takeType" width="130px">
        <template #default="scope">
          <dict-tag :options="arr_holiday_type" :value="scope.row.takeType"/>
        </template>
      </el-table-column>
      <el-table-column label="状态(是否有效)" align="center" prop="status" width="100px" v-if="false">
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="拒绝日期" align="center" prop="rejectDate" width="140" v-if="form.rejectDate">
        <template #default="scope">
          <span>{{ parseTime(scope.row.rejectDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="取消日期" align="center" prop="cancelDate" width="140" v-if="form.cancelDate">
        <template #default="scope">
          <span>{{ parseTime(scope.row.cancelDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['arr:takeHoliday:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['arr:takeHoliday:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改排班申请休假对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="takeHolidayRef" :model="form" :rules="rules" label-width="130px" inline>
        <el-form-item label="用户id" prop="userId" v-if="false">
          <el-input v-model="form.userId" placeholder="请输入用户id" />
        </el-form-item>
        <el-form-item label="用户名称" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入用户名称" disabled/>
        </el-form-item>
        <el-form-item label="所属护理单元/科室" prop="deptId" style="width: 42.5%">
          <el-tree-select
            v-model="form.deptId"
            :data="deptOptions"
            :props="{ value: 'id', label: 'label', children: 'children' }"
            value-key="id"
            placeholder="请选择护理单元"
            check-strictly
            @node-click="handleClick"
          />
        </el-form-item>
        <el-form-item label="所属护理单元/科室名称" prop="deptName" v-if="false">
          <el-input v-model="form.deptName" placeholder="请输入所属护理单元/科室名称" />
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime" style="width: 42.5%">
          <el-date-picker clearable
            v-model="form.startTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime" style="width: 42.5%">
          <el-date-picker clearable
            v-model="form.endTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="班次日期" prop="shiftsDate" style="width:42.5%;" v-if="false">
          <el-input v-model="form.shiftsDate" placeholder="请输入班次日期" />
        </el-form-item>
        <el-form-item label="班次" prop="shiftsId" style="width: 42.5%;">
          <!-- <el-input v-model="form.shiftsId" placeholder="请输入班次id" /> -->
          <el-select v-model="form.shiftsId" placeholder="请选择班次" @change="changeShiftsValue(dict)">
            <el-option
              v-for="dict in workShiftsList"
              :key="dict.id"
              :label="dict.shiftsName"
              :value="dict.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="班次名称" prop="shiftsName" v-if="false">
          <el-input v-model="form.shiftsName" placeholder="请输入班次名称" />
          <!-- <el-select v-model="form.">

          </el-select> -->
        </el-form-item>
        <el-form-item label="排班申请类别" prop="takeType" style="width:42.5%;">
          <el-select v-model="form.takeType" placeholder="请选择排班申请类别">
            <el-option
              v-for="dict in arr_holiday_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态(是否有效)" prop="status" v-if="false">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in sys_yes_no"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="拒绝日期" prop="rejectDate" v-if="false">
          <el-date-picker clearable
            v-model="form.rejectDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择拒绝日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="取消日期" prop="cancelDate" v-if="false">
          <el-date-picker clearable
            v-model="form.cancelDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择取消日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="申请理由" prop="reason" style="width: 90%;">
          <el-input v-model="form.reason" type="textarea" placeholder="请输入申请理由" />
        </el-form-item>
        <el-form-item label="备注" prop="remark" style="width: 90%;">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TakeHoliday">
import Cookies from "js-cookie";
import { listTakeHoliday, getTakeHoliday, delTakeHoliday, addTakeHoliday, updateTakeHoliday } from "@/api/arr/takeHoliday";
import { deptTreeSelect } from "@/api/system/user";
import { listWorkShifts } from "@/api/arr/workShifts";

const { proxy } = getCurrentInstance();
const { arr_holiday_type, sys_yes_no } = proxy.useDict('arr_holiday_type', 'sys_yes_no');

const takeHolidayList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const userId = ref(localStorage.getItem("userId"));
const userName = ref(localStorage.getItem("userName"));
const userDeptId = ref(localStorage.getItem("deptId"));
const userDeptName = ref(localStorage.getItem("deptName")!="null"?localStorage.getItem("deptName"):null);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: userId.value,
    userName: userName.value,
    deptId: null,
    deptName: null,
    startTime: null,
    endTime: null,
    shiftsDate: null,
    shiftsId: null,
    shiftsName: null,
    reason: null,
    takeType: null,
    status: null,
    rejectDate: null,
    cancelDate: null,
  },
  rules: {
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 根据名称筛选部门树 */
const deptName = ref("");
watch(deptName, (val) => {
  proxy.$refs["deptTreeRef"].filter(val);
});
/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};
/** 查询部门下拉树结构 */
const deptOptions = ref(null);
function getDeptTree() {
  console.log(localStorage.getItem("userId"));
  deptTreeSelect().then((response) => {
    deptOptions.value = response.data;
  });
}

/** 获取护理单元选中节点信息 */
function handleClick(node) {
  // form.value.deptId = node.id;
  form.value.deptName = node.label;
  console.log(form.value.deptName)
  getShiftsList(node.id);

}
/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.value.deptId = data.id;
  handleQuery();
}

/** 查询排班申请休假列表 */
function getList() {
  loading.value = true;
  console.log(queryParams.value);
  listTakeHoliday(queryParams.value).then(response => {
    console.log(response);
    takeHolidayList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    userId: userId.value,
    userName: userName.value,
    deptId: "",
    deptName: "",
    startTime: null,
    endTime: null,
    shiftsDate: null,
    shiftsId: null,
    shiftsName: null,
    reason: null,
    takeType: null,
    status: null,
    rejectDate: null,
    cancelDate: null,
    createBy: null,
    createId: null,
    createTime: null,
    updateBy: null,
    updateId: null,
    updateTime: null,
    tenantId: null,
    delFlag: null,
    remark: null
  };
  proxy.resetForm("takeHolidayRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加排班申请休假";
  // getShiftsList();
  // form.value.deptId = null
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getTakeHoliday(_id).then(response => {
    console.log(response);
    form.value = response.data;
    form.value.userName = response.data.createBy
    open.value = true;
    title.value = "修改排班申请休假";
    // getShiftsList();
  });
}

/** 提交按钮 */
function submitForm() {
  if(compareDates(form.value.startTime,form.value.endTime) != -1){
    return proxy.$modal.msgWarning("结束时间不能大于开始时间！");
  } 
  form.value.shiftsName =  workShiftsList.value.filter(item=>item.id == form.value.shiftsId)[0].shiftsName
  proxy.$refs["takeHolidayRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateTakeHoliday(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addTakeHoliday(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
function compareDates(date1, date2) {
  const timestamp1 = new Date(date1).getTime();
  const timestamp2 = new Date(date2).getTime();
 
  if (timestamp1 > timestamp2) {
    return 1;
  } else if (timestamp1 === timestamp2) {
    return 0;
  } else {
    return -1;
  }
}
/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除排班申请休假编号为"' + _ids + '"的数据项？').then(function() {
    return delTakeHoliday(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('arr/takeHoliday/export', {
    ...queryParams.value
  }, `takeHoliday_${new Date().getTime()}.xlsx`)
}


/** 查询班次管理列表 */
const workShiftsList = ref([])
function getShiftsList(deptId) {
  loading.value = true;
  let data = {
    "deptId": deptId,
  }
  listWorkShifts(data).then((response) => {
    console.log(response);
    workShiftsList.value = response.rows;
    loading.value = false;
  });
}

/* 选择班次时的事件 */
function changeShiftsValue(e){
  console.log(e);
}


getDeptTree();
getList();
</script>
