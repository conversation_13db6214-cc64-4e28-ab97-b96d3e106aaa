<template>
  <div class="code-highlight-test">
    <h2>代码高亮测试</h2>
    
    <div class="input-section">
      <h3>输入Markdown内容</h3>
      <textarea v-model="markdownInput" class="markdown-input" rows="10"></textarea>
      <button @click="handleParseMarkdown" class="parse-button">解析Markdown</button>
    </div>
    
    <div v-if="showDebug" class="debug-section">
      <h3>处理后的Markdown (调试用)</h3>
      <pre class="debug-output">{{ processedDebugText }}</pre>
      <button @click="showDebug = false" class="close-debug-button">关闭调试信息</button>
    </div>
    
    <div class="output-section" v-html="parsedOutput"></div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import hljs from 'highlight.js/lib/common';
import 'highlight.js/styles/atom-one-dark.css';
import * as marked from 'marked';

const markdownInput = ref(`### 方案一：柱状图（推荐）

\`\`\`javascript
// 基础折线图配置
option = {
  title: {
    text: '案件分类统计'
  },
  xAxis: {
    data: [124808, 10725, 1, 802, 7]
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    type: 'bar',
    data: [1, 5, 3, 4, 6],
    color: '#99CCFF' // 柱状图颜色
  }]
};
\`\`\`

### 思考过程

<think>
我需要分析数据格式，确定最合适的图表类型。
根据数据特点，柱状图和饼图都是不错的选择。
</think>

### 单引号代码块测试

'''javascript
// 基础折线图配置
option = {
  title: {
    text: '案件分类统计'
  },
  xAxis: {
    data: [124808, 10725, 1, 802, 7]
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    type: 'bar',
    data: [1, 5, 3, 4, 6],
    color: '#99CCFF' // 柱状图颜色
  }]
};
'''

### 用户示例

ECharts图表生成方案： 可以使用ECharts的饼图或柱状图来展示这些数据。这里提供两种方案： 方案一：柱状图（推荐） '''javascript // 基础折线图配置 option = { title: { text: '案件分类统计' }, xAxis: { data: [124808, 10725, 1, 802, 7] }, yAxis: { type: 'value' }, series: [{ type: 'bar', data: [1, 5, 3, 4, 6], color: '#99CCFF' // 柱状图颜色 }] }; ''' 方案二：饼图（适合分类占比展示） '''javascript // 饼图配置 option = { title: { text: '案件分类统计' }, series: [{ type: 'pie', data: [ { name: '类型1', value: 124808 }, { name: '类型5', value: 10725 }, { name: '类型3', value: 1 }, { name: '类型4', value: 802 }, {name: '类型6', value: 7 } ], percentage: true, radius: ['50%'], label: { show: false } }] }; '''`);

const parsedOutput = ref('');
const processedDebugText = ref('');
const showDebug = ref(true);

// 配置marked解析器
const configureMarked = () => {
  // 创建自定义渲染器
  const renderer = new marked.Renderer();
  
  // 自定义代码块渲染
  renderer.code = function(code, language) {
    const validLanguage = hljs.getLanguage(language) ? language : 'plaintext';
    const highlightedCode = hljs.highlight(code, { language: validLanguage }).value;
    const langClass = language ? ` class="language-${language}"` : '';
    const codeBlockId = `code-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    const fileExtension = getFileExtension(language);
    const downloadFilename = `code-snippet${fileExtension}`;
    
    return `
      <div class="code-block">
        <div class="code-header">
          <span class="code-language">${language || 'Code'}</span>
          <div class="code-actions">
            <button class="copy-button" onclick="copyCodeToClipboard('${codeBlockId}')">
              <span class="copy-icon">📋</span> 复制
            </button>
            <button class="download-button" onclick="downloadCode('${codeBlockId}', '${downloadFilename}')">
              <span class="download-icon">💾</span> 下载
            </button>
          </div>
        </div>
        <pre><code id="${codeBlockId}" data-language="${language || 'plaintext'}"${langClass}>${highlightedCode}</code></pre>
      </div>
    `;
  };
  
  // 设置marked选项
  marked.setOptions({
    renderer: renderer,
    highlight: function(code, lang) {
      // 使用highlight.js进行代码高亮
      const language = hljs.getLanguage(lang) ? lang : 'plaintext';
      return hljs.highlight(code, { language }).value;
    },
    langPrefix: 'hljs language-', // 添加到<code>标签的类名前缀
    pedantic: false,
    gfm: true,
    breaks: true,
    sanitize: false,
    smartypants: false,
    xhtml: false
  });
};

// 获取文件扩展名
const getFileExtension = (language) => {
  const extensions = {
    'javascript': '.js',
    'js': '.js',
    'html': '.html',
    'css': '.css',
    'python': '.py',
    'py': '.py',
    'java': '.java',
    'c': '.c',
    'cpp': '.cpp',
    'csharp': '.cs',
    'php': '.php',
    'ruby': '.rb',
    'go': '.go',
    'rust': '.rs',
    'swift': '.swift',
    'kotlin': '.kt',
    'typescript': '.ts',
    'ts': '.ts',
    'json': '.json',
    'xml': '.xml',
    'yaml': '.yml',
    'markdown': '.md',
    'md': '.md',
    'sql': '.sql',
    'bash': '.sh',
    'powershell': '.ps1'
  };
  
  return extensions[language] || '.txt';
};

// 解析Markdown，处理思考部分
const parseMarkdown = () => {
  let processedText = markdownInput.value;
  let thinkingBlocks = [];
  
  // 检测是否包含思考部分
  const hasThinking = processedText.includes('<think>');
  
  // 如果包含思考部分，先提取出来，以便后续重新插入
  if (hasThinking) {
    console.log('检测到思考文本，进行处理');
    
    // 提取所有思考块
    const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
    let match;
    let index = 0;
    
    // 临时替换思考块为占位符
    while ((match = thinkRegex.exec(processedText)) !== null) {
      const fullMatch = match[0];
      const thinkContent = match[1];
      const placeholder = `__THINK_PLACEHOLDER_${index}__`;
      
      // 保存思考块内容和占位符的映射
      thinkingBlocks.push({
        placeholder,
        content: `<span style="display:block; background-color:#f5f5f5; color:#666; padding:8px 10px; margin:4px 0; border-radius:4px;">${thinkContent}</span>`
      });
      
      // 替换为占位符
      processedText = processedText.replace(fullMatch, placeholder);
      index++;
    }
  }
  
  // 预处理：在代码块前后添加换行，确保格式正确
  processedText = processedText.replace(/(方案[一二三四五]：[\w（）]+)\s+'''(\w*)/g, '$1\n\n```$2');
  
  // 修正各种代码块格式为标准的反引号代码块
  // 1. 处理单引号代码块 - 带语言标识
  processedText = processedText.replace(/'''(\w*)\s*([\s\S]*?)'''/g, '```$1\n$2```');
  
  // 2. 处理没有语言标识的单引号代码块
  processedText = processedText.replace(/'''\s*([\s\S]*?)'''/g, '```\n$1```');
  
  // 3. 处理可能的其他格式
  processedText = processedText.replace(/```(\w*)\s+/g, '```$1\n');
  
  // 4. 处理没有换行的代码块
  processedText = processedText.replace(/方案[一二三四五]：[\w（）]+\s+```(\w*)\s*/g, '方案$1：$2\n\n```$3\n');
  
  // 5. 添加额外的换行，确保代码块正确解析
  processedText = processedText.replace(/([^\n])\n```/g, '$1\n\n```');
  processedText = processedText.replace(/```\n([^\n])/g, '```\n\n$1');
  
  // 6. 修复可能被破坏的格式
  processedText = processedText.replace(/```\n\n\n/g, '```\n\n');
  
  // 7. 处理特殊情况：代码块后面紧跟文本
  processedText = processedText.replace(/```\s+(方案)/g, '```\n\n$1');
  
  // 更新调试文本
  processedDebugText.value = processedText;
  showDebug.value = true;
  
  console.log('处理后的文本:', processedText);
  
  try {
    console.log('开始使用marked解析Markdown');
    // 使用marked解析Markdown，包括代码块
    let parsedResult = marked(processedText);
    console.log('marked解析完成');
    
    // 恢复思考块
    if (hasThinking) {
      thinkingBlocks.forEach(block => {
        parsedResult = parsedResult.replace(block.placeholder, block.content);
      });
    }
    
    parsedOutput.value = parsedResult;
    
    // 添加复制和下载功能
    setTimeout(() => {
      setupCodeActions();
    }, 100);
  } catch (error) {
    console.error('解析Markdown时出错:', error);
    console.error('错误详情:', error.stack);
    parsedOutput.value = `<div class="error-message">解析Markdown时出错: ${error.message}</div>`;
  }
};

// 设置代码操作功能
const setupCodeActions = () => {
  window.copyCodeToClipboard = function (codeId) {
    const codeElement = document.getElementById(codeId);
    if (codeElement) {
      // 提取原始代码文本（不包含HTML标签）
      const codeText = codeElement.textContent;
      
      // 创建一个临时文本区域用于复制
      const textarea = document.createElement('textarea');
      textarea.value = codeText;
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      document.body.appendChild(textarea);
      textarea.select();
      
      try {
        // 尝试使用复制命令
        const successful = document.execCommand('copy');
        if (successful) {
          // 显示复制成功的视觉反馈
          const button = codeElement.closest('.code-block').querySelector('.copy-button');
          const originalText = button.innerHTML;
          button.innerHTML = '<span class="copy-icon">✓</span> 已复制';
          setTimeout(() => {
            button.innerHTML = originalText;
          }, 2000);
        } else {
          // 如果execCommand失败，尝试使用clipboard API
          navigator.clipboard.writeText(codeText)
            .then(() => {
              const button = codeElement.closest('.code-block').querySelector('.copy-button');
              const originalText = button.innerHTML;
              button.innerHTML = '<span class="copy-icon">✓</span> 已复制';
              setTimeout(() => {
                button.innerHTML = originalText;
              }, 2000);
            })
            .catch(err => console.error('复制失败:', err));
        }
      } catch (err) {
        console.error('复制过程中出错:', err);
      } finally {
        // 清理临时元素
        document.body.removeChild(textarea);
      }
    }
  };
  
  window.downloadCode = function (codeId, filename) {
    const codeElement = document.getElementById(codeId);
    if (codeElement) {
      // 提取原始代码文本（不包含HTML标签）
      const codeText = codeElement.textContent;
      const language = codeElement.getAttribute('data-language');
      
      // 创建Blob对象
      const blob = new Blob([codeText], { type: 'text/plain' });
      
      // 创建下载链接
      const downloadLink = document.createElement('a');
      downloadLink.href = URL.createObjectURL(blob);
      downloadLink.download = filename;
      
      // 添加到DOM并触发点击
      document.body.appendChild(downloadLink);
      downloadLink.click();
      
      // 清理
      document.body.removeChild(downloadLink);
      URL.revokeObjectURL(downloadLink.href);
      
      // 显示下载成功的视觉反馈
      const button = codeElement.closest('.code-block').querySelector('.download-button');
      const originalText = button.innerHTML;
      button.innerHTML = '<span class="download-icon">✓</span> 已下载';
      setTimeout(() => {
        button.innerHTML = originalText;
      }, 2000);
    }
  };
  
  // 创建一个专门用于复制和下载功能的全局脚本
  if (!document.getElementById('code-utils-script')) {
    const scriptElement = document.createElement('script');
    scriptElement.id = 'code-utils-script';
    scriptElement.textContent = `
      // 该脚本用于确保复制和下载函数在DOM中可访问
      if (typeof window.copyCodeToClipboard !== 'function') {
        window.copyCodeToClipboard = ${window.copyCodeToClipboard.toString()};
      }
      if (typeof window.downloadCode !== 'function') {
        window.downloadCode = ${window.downloadCode.toString()};
      }
    `;
    document.body.appendChild(scriptElement);
  }
};

// 处理按钮点击事件
const handleParseMarkdown = () => {
  console.log('解析按钮被点击');
  parseMarkdown();
};

onMounted(() => {
  console.log('组件已挂载，开始配置marked');
  try {
    configureMarked();
    console.log('marked配置完成，开始解析');
    parseMarkdown();
    console.log('初始化解析完成');
  } catch (error) {
    console.error('初始化过程出错:', error);
  }
});
</script>

<style scoped>
.code-highlight-test {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

h2 {
  margin-bottom: 20px;
  color: #333;
}

.input-section {
  margin-bottom: 20px;
}

.markdown-input {
  width: 100%;
  height: 300px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  resize: vertical;
}

.parse-button {
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  margin-top: 10px;
  transition: background 0.2s ease;
}

.parse-button:hover {
  background: #45a049;
}

.output-section {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 20px;
  background-color: #fff;
}

:deep(.code-block) {
  margin: 16px 0;
  border-radius: 6px;
  overflow: hidden;
  background-color: #282c34;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

:deep(.code-header) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: #21252b;
  border-bottom: 1px solid #181a1f;
}

:deep(.code-language) {
  color: #abb2bf;
  font-size: 12px;
  font-weight: 500;
}

:deep(.code-actions) {
  display: flex;
  gap: 8px;
}

:deep(.copy-button),
:deep(.download-button) {
  background: #4d78cc;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: background 0.2s ease;
}

:deep(.copy-button:hover),
:deep(.download-button:hover) {
  background: #5a8ae8;
}

:deep(.download-button) {
  background: #56b6c2;
}

:deep(.download-button:hover) {
  background: #66c6d2;
}

:deep(pre) {
  margin: 0;
  padding: 16px;
  overflow-x: auto;
}

:deep(code) {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.debug-section {
  margin-bottom: 20px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
}

.debug-output {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.close-debug-button {
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.close-debug-button:hover {
  background: #45a049;
}

.error-message {
  padding: 10px;
  background-color: #ffebee;
  color: #b71c1c;
  border: 1px solid #ef9a9a;
  border-radius: 4px;
  margin: 10px 0;
}
</style> 
