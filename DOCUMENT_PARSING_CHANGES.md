# 文档解析格式修改说明

## 修改概述

根据需求，文档解析格式已经修改，不再需要处理 `formatData` 字段，而是直接从文件详情接口获取以下字段：

## 新的字段映射

### 1. 文档摘要
- **原来**: 从 `res.data.formatData['文档摘要']` 获取
- **现在**: 直接从 `res.data.documentSummary` 获取

### 2. 关键词
- **原来**: 从 `res.data.formatData['关键词']` 获取
- **现在**: 直接从 `res.data.keywords` 获取
- **处理方式**: 支持字符串（按逗号或空格分割）和数组格式

### 3. 文档结构
- **原来**: 从 `res.data.formatData['文档结构']` 获取
- **现在**: 直接从 `res.data.documentStructure` 获取
- **处理方式**: 支持字符串（JSON解析）、数组和对象格式

### 4. 文档智能标签
- **原来**: 从 `res.data.formatData['文档智能标签']` 或 `res.data.formatData['智能标签']` 获取
- **现在**: 直接从 `res.data.documentTags` 获取
- **处理方式**: 支持字符串（按逗号或空格分割）和数组格式

## 主要修改文件

### 1. src/views/PdfPreview.vue
- 修改 `loadFileData` 函数，直接从新字段获取数据
- 修改 `saveSettings` 函数，保存到新的字段结构
- 删除不再使用的函数：`generateTextFormat`、`parseTextFormat`、`fixJsonFormat`

### 2. src/api/file/file.js
- 修改 `getFileDetail` 函数，支持 `remark` 参数

## 数据处理逻辑

### 关键词处理
```javascript
if (fileData.keywords) {
  if (typeof fileData.keywords === 'string') {
    keywords.value = fileData.keywords.split(/[,，\s]+/).filter(k => k.trim() !== '');
  } else if (Array.isArray(fileData.keywords)) {
    keywords.value = fileData.keywords.filter(k => k && k.trim() !== '');
  }
}
```

### 文档结构处理
```javascript
if (fileData.documentStructure) {
  let structureData;
  if (typeof fileData.documentStructure === 'string') {
    try {
      structureData = JSON.parse(fileData.documentStructure);
    } catch (e) {
      structureData = [{ label: fileData.documentStructure, children: [] }];
    }
  } else {
    structureData = fileData.documentStructure;
  }
  // 进一步处理数组或对象格式...
}
```

### 智能标签处理
```javascript
if (fileData.documentTags) {
  if (typeof fileData.documentTags === 'string') {
    smartTags.value = fileData.documentTags.split(/[,，\s]+/).filter(t => t.trim() !== '');
  } else if (Array.isArray(fileData.documentTags)) {
    smartTags.value = fileData.documentTags.filter(t => t && t.trim() !== '');
  }
}
```

## 保存数据格式

保存时直接使用新的字段结构：

```javascript
const params = {
  // ... 其他字段
  documentSummary: documentSummary.value === '暂无数据' ? '' : documentSummary.value,
  keywords: filteredKeywords.length > 0 ? filteredKeywords.join(',') : '',
  documentStructure: JSON.stringify(filteredStructure),
  documentTags: filteredSmartTags.length > 0 ? filteredSmartTags.join(',') : ''
};
```

## 兼容性说明

- 如果后端接口还没有返回新字段，会显示"暂无数据"
- 支持多种数据格式（字符串、数组、对象）的自动处理
- 保持了原有的编辑功能和用户界面

## 测试建议

1. 测试文件详情接口是否返回新字段
2. 测试不同格式数据的解析是否正确
3. 测试编辑和保存功能是否正常
4. 测试空数据的处理是否正确
