# ArchiMind - 智能建筑知识库系统

## 项目简介

ArchiMind 是一个基于 Vue 3 开发的智能建筑知识库前端系统，使用了 Vite 作为构建工具，整合了 Element Plus 和 Vuetify 两套 UI 框架，为用户提供直观、美观的交互界面。

## 系统要求

- Node.js 18.18 或更高版本
- npm 10.0 左右或更高版本

## 安装步骤
 - 本机需要安装node环境
### 1. 克隆项目

```bash
git clone <项目仓库URL>
cd knowLedge
```

### 2. 安装依赖

```bash
npm install
```

### 3. 运行开发服务器

```bash
npm run dev
```

开发服务器将会启动，通常在 http://localhost:5173 (Vite 默认端口)。

### 4. 构建生产版本

```bash
npm run build
```

生成的文件将位于 `dist` 目录中。

### 5. 预览生产构建

```bash
npm run preview
```

## 项目结构

```
knowLedge/
├── node_modules/         # 项目依赖
├── public/               # 静态资源
│   └── vite.svg          # 站点图标
├── src/                  # 源代码
│   ├── api/              # API 请求模块
│   │   ├── chat.js       # 聊天相关 API
│   │   ├── permission.js # 权限相关 API
│   │   └── user.js       # 用户相关 API
│   ├── assets/           # 资源文件（图片、字体等）
│   ├── components/       # 组件
│   │   ├── TheHeader.vue # 页面头部组件
│   │   └── TheFooter.vue # 页面底部组件
│   ├── router/           # 路由配置
│   │   └── index.js      # 路由定义
│   ├── store/            # Vuex 状态管理
│   ├── utils/            # 工具函数
│   │   ├── auth.js       # 认证相关工具
│   │   ├── mock.js       # 模拟数据
│   │   └── request.js    # Axios 请求封装
│   ├── views/            # 页面视图
│   │   ├── HomeView.vue  # 首页
│   │   ├── LoginView.vue # 登录页面
│   │   ├── AboutView.vue # 关于页面
│   │   ├── QAView.vue    # 问答页面
│   │   ├── Graph.vue     # 知识图谱页面
│   │   ├── Document.vue  # 文档页面
│   │   ├── operation.vue # 运维页面
│   │   └── PermissionView.vue # 权限管理页面
│   ├── App.vue           # 根组件
│   ├── main.js           # 入口文件
│   └── style.css         # 全局样式
├── index.html            # HTML 入口文件
├── vite.config.js        # Vite 配置
├── package.json          # 项目依赖和脚本
└── README.md             # 项目文档
```

## 主要文件说明

### 配置文件

- `package.json`: 定义项目依赖和命令脚本
- `vite.config.js`: Vite 构建工具配置文件
- `index.html`: HTML 入口文件

### 核心文件

- `src/main.js`: 项目入口文件，初始化 Vue 应用和导入全局插件
- `src/App.vue`: 根组件，定义全局布局结构
- `src/router/index.js`: 路由配置，定义应用的页面路由
- `src/utils/request.js`: Axios 请求封装，处理 API 请求

### API 模块

- `src/api/user.js`: 用户相关 API，包括登录、获取用户信息等
- `src/api/chat.js`: 聊天功能相关 API
- `src/api/permission.js`: 权限管理相关 API

### 工具函数

- `src/utils/auth.js`: 处理 Token 存储和获取
- `src/utils/mock.js`: 提供模拟数据，用于开发阶段测试

## 功能模块

### 用户认证

系统实现了基于 Token 的用户认证机制，包括登录、登出、自动登出等功能。

### 首页

通过 `/` 路由访问，提供系统总览和快速入口。

### 关于页面

通过 `/about` 路由访问，提供系统介绍和相关信息。

### 问答系统

通过 `/chat` 路由访问，提供智能问答功能。

### 知识图谱

通过 `/library` 路由访问，展示知识关联图谱。

### 文档管理

通过 `/documents` 路由访问，提供文档查看和管理功能。

### 运维管理

通过 `/operation` 路由访问，提供系统运维功能。

### 权限管理

通过 `/permission` 路由访问，管理用户权限。

## 技术栈

- **前端框架**: Vue 3
- **构建工具**: Vite
- **UI 框架**: Element Plus 和 Vuetify
- **状态管理**: Vuex
- **路由管理**: Vue Router
- **HTTP 客户端**: Axios
- **图表库**: ECharts

## 开发注意事项

1. 项目使用 Vue 3 的 Composition API，请熟悉相关语法
2. API 请求已封装在 `src/utils/request.js` 中
3. 项目同时使用了 Element Plus 和 Vuetify，注意避免样式冲突
4. 权限控制通过路由守卫实现，详见 `src/router/index.js`
5. 开发阶段使用 `mock.js` 提供模拟数据，生产环境需要配置真实 API 地址

## 部署说明

1. 执行 `npm run build` 生成静态文件
2. 将 `dist` 目录下的文件部署到 Web 服务器
3. 确保服务器配置了正确的 API 代理，以处理 `/api` 路径的请求

## 常见问题

- **Q: 如何修改 API 基础路径?**  
  A: 在 `src/utils/request.js` 文件中修改 `baseURL` 配置。

- **Q: 如何添加新页面?**  
  A: 在 `src/views` 目录创建新的 Vue 组件，然后在 `src/router/index.js` 中添加对应路由。

- **Q: 如何配置生产环境 API 地址?**  
  A: 可以通过环境变量或在构建时修改 `src/utils/request.js` 中的 `baseURL`。
