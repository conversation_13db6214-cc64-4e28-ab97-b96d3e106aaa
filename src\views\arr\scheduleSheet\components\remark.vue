<template>
  <el-dialog
    v-model="remarkVisible"
    title="排班备注"
    width="900px"
    align-center
    @close="getClose"
  >
    <div v-for="(item, index) in remarkList" :key="index">
      <div class="remark_item">
        <el-input
          v-model="item.value"
          class="remark_input"
          autosize
          type="textarea"
          placeholder="请填写排班备注"
        />
        <el-button
          class="button_item"
          type="primary"
          size="small"
          :icon="Plus"
          circle
          v-if="index == remarkList.length - 1"
          @click="addInput"
        ></el-button>
        <el-button
          class="button_item"
          type="primary"
          size="small"
          :icon="Minus"
          circle
          v-if="index != 0 && index == remarkList.length - 1"
          @click="reduceInput"
        ></el-button>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="getClose">取消</el-button>
        <el-button type="primary" @click="confirm">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { Minus, Plus } from "@element-plus/icons-vue";
const { proxy } = getCurrentInstance();
const props = defineProps({
  remarkVisible: {
    type: Boolean,
  },
});
const emit = defineEmits(["closeRemark", "confirmRemark"]);
// 定义备注列表
const remarkList = ref([{ key: 0, value: "" }]);
// 增加一个remark输入框
function addInput() {
  remarkList.value.push({ key: remarkList.value.length, value: "" });
}
// 删除一个remark输入框
function reduceInput() {
  remarkList.value.pop();
}

// 监听排版备注列表弹窗打开关闭
const remarkVisible = ref(false);
watch(
  () => [props.remarkVisible],
  (newValue, oldValue) => {
    console.log(newValue);
    if (newValue) {
      remarkVisible.value = newValue[0];
    }
  }
);
// 关闭弹窗组件
function getClose() {
  remarkVisible.value = false;
  emit("closeRemark");
}

// 保存
function confirm() {
  remarkVisible.value = false;
  emit("confirmRemark", remarkList.value);
}
</script>

<style lang="scss" scoped>
.remark_item {
  display: flex;
  align-items: center;
  margin-bottom: 14px;
  .remark_input {
    width: 80%;
  }
  .button_item {
    margin-left: 20px;
  }
}
</style>
