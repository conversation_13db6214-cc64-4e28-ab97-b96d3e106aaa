import { login, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import defAva from '@/assets/images/profile.jpg'
import { socialLogin } from '@/api/auth/loginSocial'

const useUserStore = defineStore(
  'user',
  {
    state: () => ({
      token: getToken(),
      id: '',
      name: '',
      avatar: '',
      roles: [],
      users: {},
      permissions: [],
      apiKey: '',
      apiUrl: '',
      publicKey: '',
      publicBucketName: '',
      decryptedApiKey: '',
      decryptedApiUrl: ''
    }),
    actions: {
      // 登录
      login(userInfo) {
        const username = userInfo.username.trim()
        const password = userInfo.password
        const code = userInfo.code
        const uuid = userInfo.uuid
        return new Promise((resolve, reject) => {
          login(username, password, code, uuid).then(res => {
            setToken(res.token)
            this.token = res.token
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 获取用户信息
      getInfo() {
        return new Promise((resolve, reject) => {
          getInfo().then(res => {
            // console.log('获取用户信息', res);

            const user = res.user
            const avatar = (user.avatar == "" || user.avatar == null) ? defAva : import.meta.env.VITE_APP_BASE_API + user.avatar;

            if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
              this.roles = res.roles
              this.permissions = res.permissions
            } else {
              this.roles = ['ROLE_DEFAULT']
            }
            this.users = res.user
            this.id = user.userId
            this.name = user.userName
            this.avatar = avatar
            resolve(res)
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 第三方平台登录
      SocialLogin(userInfo) {
        const code = userInfo.code
        const state = userInfo.state
        const source = userInfo.source
        return new Promise((resolve, reject) => {
          socialLogin(source, code, state).then(res => {
            setToken(res.token)
            // commit('SET_TOKEN', res.token)
            this.token = res.token
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 退出系统
      logOut() {
        return new Promise((resolve, reject) => {
          logout(this.token).then(() => {
            this.token = ''
            this.roles = []
            this.permissions = []
            this.apiKey = ''
            this.apiUrl = ''
            this.publicKey = ''
            this.publicBucketName = ''
            this.decryptedApiKey = ''
            this.decryptedApiUrl = ''
            removeToken()
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 设置权限
      setPermissions(permissions) {
        this.permissions = permissions
      },
      // 设置API密钥
      setApiKey(apiKey) {
        this.apiKey = apiKey
      },
      // 设置API地址
      setApiUrl(apiUrl) {
        this.apiUrl = apiUrl
      },
      // 设置公钥
      setPublicKey(publicKey) {
        this.publicKey = publicKey
      },
      // 设置公共存储桶名称
      setPublicBucketName(publicBucketName) {
        this.publicBucketName = publicBucketName
      },
      // 设置解密后的API密钥
      setDecryptedApiKey(decryptedApiKey) {
        this.decryptedApiKey = decryptedApiKey
      },
      // 设置解密后的API地址
      setDecryptedApiUrl(decryptedApiUrl) {
        this.decryptedApiUrl = decryptedApiUrl
      }
    }
  })

export default useUserStore
