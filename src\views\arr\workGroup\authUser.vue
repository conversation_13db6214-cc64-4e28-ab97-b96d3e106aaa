<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" v-show="showSearch" :inline="true">
      <el-form-item label="用户名称" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号码" prop="phonenumber">
        <el-input
          v-model="queryParams.phonenumber"
          placeholder="请输入手机号码"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="openSelectUser"
          v-hasPermi="['system:role:add']"
          >添加用户</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="CircleClose"
          :disabled="multiple"
          @click="cancelAuthUserAll"
          v-hasPermi="['system:role:remove']"
          >批量删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Close" @click="handleClose">关闭</el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="userList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="用户昵称" prop="nickName" :show-overflow-tooltip="true" />
      <el-table-column label="所属护理组" prop="deptName" :show-overflow-tooltip="true" />
      <el-table-column
        label="手机号码"
        prop="contactNumber"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="排序" prop="nurseGroupSort" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" @click="cancelAuthUser(scope.row)"
            >删除</el-button
          >
          <el-button link type="primary" @click="show(scope.row)">排序</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <select-user
      ref="selectRef"
      :nurseGroupId="queryParams.nurseGroupId"
      :nurseGroupName="queryParams.nurseGroupName"
      @ok="handleQuery"
    />
    <el-dialog title="排序" v-model="visible" width="300px" top="6vh" append-to-body>
      <el-form :model="data" ref="dataRef" :inline="true">
        <el-form-item label="排序" prop="nurseGroupSort">
          <el-input
            v-model="data.nurseGroupSort"
            oninput="value=value.replace(/[^\d]/g,'')"
            placeholder="请输入排序"
            clearable
            style="width: 100%"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSelectUser">确 定</el-button>
          <el-button @click="visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="WorkAuthUser">
import selectUser from "./selectUser";

import { listPermi, emptyGroup, addGroup } from "@/api/arr/workGroup";

const route = useRoute();
const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

const userList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const multiple = ref(true);
const total = ref(0);
const userIds = ref([]);

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  nurseGroupId: route.query.nurseGroupId,
  nurseGroupName: route.query.nurseGroupName,
});

/** 查询授权用户列表 */
function getList() {
  loading.value = true;
  listPermi(queryParams).then((response) => {
    userList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}
// 返回按钮
function handleClose() {
  const obj = { path: "/system/role" };
  proxy.$tab.closeOpenPage(obj);
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
// 多选框选中数据
function handleSelectionChange(selection) {
  userIds.value = selection.map((item) => item.userId);
  multiple.value = !selection.length;
}
/** 打开授权用户表弹窗 */
function openSelectUser() {
  proxy.$refs["selectRef"].show();
}
/** 删除按钮操作 */
function cancelAuthUser(row) {
  const userIds = [row.userId];
  proxy.$modal
    .confirm('确认要删除该护理组"' + row.userName + '"用户吗？')
    .then(function () {
      return emptyGroup(userIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
/** 批量删除按钮操作 */
function cancelAuthUserAll(row) {
  proxy.$modal
    .confirm("是否删除选中用户数据项?")
    .then(function () {
      return emptyGroup(userIds.value);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

const visible = ref(false);
const _userId = ref();
const data = ref({
  nurseGroupSort: null,
});
// 显示弹框
function show(row) {
  console.log(row);
  _userId.value = row.userId;
  visible.value = true;
}
function handleSelectUser() {
  let obj = {
    userId: _userId.value,
    nurseGroupId: queryParams.nurseGroupId,
    nurseGroupName: queryParams.nurseGroupName,
    nurseGroupSort: data.value.nurseGroupSort,
  };

  addGroup([obj]).then((res) => {
    proxy.$modal.msgSuccess(res.msg);
    if (res.code === 200) {
      proxy.resetForm("dataRef");
      visible.value = false;
      getList();
    }
  });
}
getList();
</script>
