import request from '@/utils/request'

// 登录方法
export function login(username, password, code, uuid) {
  const data = {
    username,
    password,
    code,
    uuid
  }
  return request({
    url: '/login',
    headers: {
      isToken: false,
      repeatSubmit: false
    },
    method: 'post',
    data: data
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}

// 获取key
export function getPublicKey() {
  return request({
    url: '/publicKey',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}

// 获取用户手机号
export function getPhone() {
  return request({
    url: '/getPhone',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}


// 发送验证码-未登录
export function sendSmsByPhone(phoneNumber) {
  return request({
    url: '/sendSmsByPhone/'+phoneNumber,
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}

// 发送验证码
export function sendSms(phoneNumber) {
  return request({
    url: '/sendSms/'+phoneNumber,
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}

export function checkSms(phoneNumber, code) {
  return request({
    // url: `/checkSms/${phoneNumber}/${code}`,
    url: '/checkSms/'+phoneNumber+'/' + code,
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}
