import request from '@/utils/request'

// 查询排班模板列表
export function listArrTemplate(query) {
  return request({
    url: '/arr/arrTemplate/list',
    method: 'get',
    params: query
  })
}

// 查询排班模板详细
export function getArrTemplate(id) {
  return request({
    url: '/arr/arrTemplate/' + id,
    method: 'get'
  })
}

// 新增排班模板
export function addArrTemplate(data) {
  return request({
    url: '/arr/arrTemplate',
    method: 'post',
    data: data
  })
}

// 修改排班模板
export function updateArrTemplate(data) {
  return request({
    url: '/arr/arrTemplate/edit',
    method: 'post',
    data: data
  })
}

// 删除排班模板
export function delArrTemplate(id) {
  return request({
    url: '/arr/arrTemplate/del/' + id,
    method: 'post'
  })
}
