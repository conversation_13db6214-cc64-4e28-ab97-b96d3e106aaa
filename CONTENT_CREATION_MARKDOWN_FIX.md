# 内容创作模块Markdown格式处理修复

## 问题描述

用户反馈在内容创作模块中，连续问答时有些markdown格式内容没有正确解析，特别是在历史记录显示时。

## 问题分析

通过代码分析发现以下问题：

1. **流式响应处理不一致**: 在`sendMessageToAPI`函数中，消息的`content`字段被设置为原始内容，没有经过`processContent`函数的markdown处理
2. **连续问答处理缺失**: 连续问答时，机器人回复的显示内容没有经过markdown处理
3. **错误消息未处理**: 一些硬编码的错误消息没有经过`processContent`处理
4. **历史记录显示**: 虽然历史记录加载时会调用`processContent`，但实时显示时可能存在问题

## 修复方案

### 1. 优化Markdown检测逻辑

修改`isMarkdownContent`函数，使其对内容创作模式更加友好：

```javascript
// 强特征：明确的Markdown语法
const strongMarkdownPatterns = [
  /^#{1,6}\s+.+$/m,        // 标题必须有内容 # 标题
  /```[\w]*\n[\s\S]*?\n```/,  // 完整的代码块
  /^\s*>\s+.+$/m,          // 引用必须有内容
  /\[.+\]\(https?:\/\/.+\)/,  // 完整的链接
  /!\[.*\]\(.+\)/,         // 图片链接
  /^\s*\|.+\|.+\|/m,       // 表格（至少两列）
  /^---+$/m,               // 分隔线
];

// 弱特征：可能的Markdown语法
const weakMarkdownPatterns = [
  /\*\*[^*]+\*\*/,         // 粗体（内容不能为空）
  /\*[^*]+\*/,             // 斜体（内容不能为空）
  /`[^`]+`/,               // 行内代码（内容不能为空）
  /^\s*[-*+]\s+.+$/m,      // 无序列表
  /^\s*\d+\.\s+.+$/m,      // 有序列表
];

// 1个强特征 或 2个弱特征就认为是markdown
const isMarkdown = strongMatches >= 1 || weakMatches >= 2;
```

### 2. 修复流式响应处理

在`sendMessageToAPI`函数中，确保所有消息内容都经过`processContent`处理：

#### 修复位置1: 主要消息更新
```javascript
// 创建更新后的消息对象，保留所有现有属性
const rawContentForDisplay = hasThinkTag ? mainContent : cleanedContent;

// 对显示内容进行markdown处理
const processedContentForDisplay = processContent(rawContentForDisplay);
console.log('sendMessageToAPI: 对显示内容进行markdown处理完成');

const updatedMessage = {
  ...currentMessage,
  // 使用经过markdown处理的内容用于显示
  content: processedContentForDisplay,
  originalContent: latestAnswer, // 保存API返回的原始内容，用于历史记录
  // ... 其他属性
};
```

#### 修复位置2: 流式响应中的思考完成处理
```javascript
// 对清理后的内容进行markdown处理
const processedCleanedContent = processContent(cleanedContent);
console.log('流式响应: 对清理后的内容进行markdown处理完成');

const updatedMessage = {
  ...currentMessage,
  // 使用经过markdown处理的内容用于显示
  content: processedCleanedContent,
  // ... 其他属性
};
```

#### 修复位置3: 其他消息内容设置
```javascript
const rawContentForDisplay2 = contentWithoutThink || currentAnswer;
const processedContentForDisplay2 = processContent(rawContentForDisplay2);

messages.value[thinkingMessageIndex] = {
  type: 'system',
  content: processedContentForDisplay2, // 使用经过markdown处理的内容
  // ... 其他属性
};
```

### 3. 修复错误消息处理

确保所有错误消息都经过`processContent`处理：

```javascript
// 错误消息1
const errorMessage = "未能获取到有效回答。单位知识库模式下可能需要刷新页面或选择其他知识库后再试。";
messages.value[thinkingMessageIndex] = {
  type: 'system',
  content: processContent(errorMessage),
  // ... 其他属性
};

// 错误消息2
const errorMessage2 = "已接收到响应数据，但未能提取有效回答。单位知识库模式下可能需要刷新页面或选择其他知识库后再试。";
// ... 类似处理

// 错误消息3-5
// ... 类似处理
```

### 4. 添加调试日志

为了更好地跟踪markdown处理过程，添加了详细的调试日志：

```javascript
const processContent = (text) => {
  console.log('processContent开始处理内容:', {
    textLength: text.length,
    textPreview: text.substring(0, 200),
    currentMode: selectedMode.value?.value
  });

  const isMarkdown = isMarkdownContent(text);
  
  console.log('processContent检测结果:', {
    isMarkdown,
    willUseMarkdownProcessor: isMarkdown
  });
  
  // ... 处理逻辑
};
```

## 修复效果

经过以上修复，内容创作模块现在能够：

1. **正确检测markdown内容**: 更准确地识别markdown格式的文本
2. **一致的markdown处理**: 无论是实时回复还是历史记录，都使用相同的处理逻辑
3. **完整的格式支持**: 支持标题、列表、代码块、表格、链接等markdown语法
4. **思考块兼容**: 保持原有的思考块（`<think>`标签）处理逻辑
5. **错误消息格式化**: 连错误消息也能正确显示markdown格式

## 测试建议

建议测试以下场景：

1. **基本markdown**: 标题、粗体、斜体、列表
2. **代码块**: 带语言标识的代码块
3. **表格**: 复杂的markdown表格
4. **混合内容**: markdown + 思考块的混合内容
5. **连续问答**: 多轮对话中的markdown格式保持
6. **历史记录**: 点击历史记录时的格式显示
7. **错误场景**: 网络错误等异常情况下的消息显示

## 注意事项

1. **向后兼容**: 所有修改都保持了向后兼容性
2. **性能影响**: markdown处理会增加一些计算开销，但影响很小
3. **调试日志**: 生产环境可能需要移除或减少调试日志
4. **内容创作特化**: 针对内容创作模式优化了markdown检测逻辑
