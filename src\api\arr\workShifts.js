import request from '@/utils/request'

// 查询班次管理列表
export function listWorkShifts(query) {
  return request({
    url: '/arr/workShifts/list',
    method: 'get',
    params: query
  })
}

// 查询班次管理详细
export function getWorkShifts(id) {
  return request({
    url: '/arr/workShifts/' + id,
    method: 'get'
  })
}

// 新增班次管理
export function addWorkShifts(data) {
  return request({
    url: '/arr/workShifts',
    method: 'post',
    data: data
  })
}

// 修改班次管理
export function updateWorkShifts(data) {
  return request({
    url: '/arr/workShifts/edit',
    method: 'post',
    data: data
  })
}

// 删除班次管理
export function delWorkShifts(id) {
  return request({
    url: '/arr/workShifts/del/' + id,
    method: 'post'
  })
}
