import request from '@/utils/request'

// 查询员工基本信息列表
export function listStaffInfo(query) {
  return request({
    url: '/hr/staffInfo/list',
    method: 'get',
    params: query
  })
}

// 查询员工基本信息详细
export function getStaffInfo(userId) {
  return request({
    url: '/hr/staffInfo/' + userId,
    method: 'get'
  })
}

// 新增员工基本信息
export function addStaffInfo(data) {
  return request({
    url: '/hr/staffInfo',
    method: 'post',
    data: data
  })
}

// 修改员工基本信息
export function updateStaffInfo(data) {
  return request({
    url: '/hr/staffInfo/edit',
    method: 'post',
    data: data
  })
}

// 删除员工基本信息
export function delStaffInfo(userId) {
  return request({
    url: '/hr/staffInfo/del/' + userId,
    method: 'post'
  })
}
