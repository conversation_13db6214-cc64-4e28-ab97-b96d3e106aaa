import request from '@/utils/request'

// 查询用户部门角色列表
export function listSysUserDeptRole(query) {
  return request({
    url: '/plus/sysUserDeptRole/list',
    method: 'get',
    params: query
  })
}


// 查询用户部门角色列表
export function listUserDeptRole(query) {
  return request({
    url: '/plus/sysUserDeptRole/listUserDeptRole',
    method: 'get',
    params: query
  })
}

// 新增用户部门角色
export function addUserDeptRole(data) {
  return request({
    url: '/plus/sysUserDeptRole/add',
    method: 'post',
    data: data
  })
}

// 修改用户部门角色
export function updateUserDeptRole(data) {
  return request({
    url: '/plus/sysUserDeptRole/update',
    method: 'post',
    data: data
  })
}

// 给用户授权时添加用户部门角色关系
export function authUserDeptRole(data) {
  return request({
    url: '/plus/sysUserDeptRole/auth/',
    method: 'post',
    data: data
  })
}

// 删除用户部门角色
export function cancelUserDeptRole(data) {
  return request({
    url: '/plus/sysUserDeptRole/cancelAuth/',
    method: 'post',
    data: data
  })
}

//切换自己所在的部门
export function changeMyDept(data) {
  return request({
    url: '/plus/sysUserDeptRole/changeMyDept/',
    method: 'post',
    data: data
  })
}
