<script setup>
import TheHeader from './components/TheHeader.vue';
import TheFooter from './components/TheFooter.vue';
import { useRoute } from 'vue-router';
import { computed } from 'vue';

const route = useRoute();
const isLoginPage = computed(() => route.path == '/login');
</script>

<template>
  <v-app>
    <TheHeader v-if="!isLoginPage" />
    <v-main :class="{ 'login-main': isLoginPage, 'app-main': !isLoginPage }">
      <div :class="{ 'main-content': !isLoginPage, 'login-container': isLoginPage }">
        <router-view />
      </div>
    </v-main>
    <!-- <TheFooter v-if="!isLoginPage" /> -->
  </v-app>
</template>

<style>
html,
body {
  margin: 0;
  padding: 0;
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

* {
  box-sizing: border-box;
}

:root {
  /* 默认亮色主题变量 */
  --background-color: #F5F5F5;
  --card-background: #FFFFFF;
  --text-primary: #333333;
  --text-secondary: #757575;
  --border-color: #E0E0E0;
  --accent-color: #1976D2;
  --shadow-color: rgba(0, 0, 0, 0.05);
  --hover-shadow: rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] {
  --background-color: #121212;
  --card-background: #1E1E1E;
  --text-primary: #FFFFFF;
  --text-secondary: #AAAAAA;
  --border-color: #333333;
  --accent-color: #64B5F6;
  --shadow-color: rgba(0, 0, 0, 0.2);
  --hover-shadow: rgba(0, 0, 0, 0.3);
}

.main-content {
  min-height: calc(100vh - 140px);
  width: 100%;
  /* max-width: 100%; */
  margin: 0;
  padding: 0px;
  background-color: var(--card-background);
  border-radius: 0;
  box-shadow: 0 1px 3px var(--shadow-color);
  position: relative;
  z-index: 1;
  color: var(--text-primary);
}

/* 确保内容区域有足够的上边距，不被header遮挡 */
.app-main {
  padding-top: 24px !important;
  /* 只保留顶部导航栏的实际高度 */
  padding-bottom: 0 !important;
  /* 不再需要底部内边距 */
  background-color: var(--background-color);
  height: 93vh !important;
  transition: background-color 0.3s ease;
  width: 100%;
}

/* 登录页面专用样式 */
.login-main {
  padding: 0 !important;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.login-container {
  padding: 0;
  margin: 0;
  min-height: 100vh;
  width: 100%;
  max-width: none;
  background-color: transparent;
  box-shadow: none;
  border-radius: 0;
}

.theme--dark {
  --v-theme-background: #121212;
  --v-theme-surface: #1E1E1E;
  --v-theme-primary: #64B5F6;
  --v-theme-secondary: #5C6BC0;
  --v-theme-accent: #4FC3F7;
}

.theme--light {
  --v-theme-background: #FFFFFF;
  --v-theme-surface: #F5F5F5;
  --v-theme-primary: #1976D2;
  --v-theme-secondary: #3F51B5;
  --v-theme-accent: #03A9F4;
}

/* 全局过渡效果 */
.v-card,
.v-btn,
.v-icon,
.v-text-field,
.v-list-item {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease !important;
}

/* 隐藏显示"首页"的标签样式 */
.v-btn--active-class, .v-breadcrumbs, .breadcrumb-item, .el-breadcrumb-item, .el-tabs {
  display: none !important;
}
</style>
