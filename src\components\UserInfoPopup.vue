<template>
  <v-dialog v-model="visible" max-width="420" transition="dialog-bottom-transition">
    <v-card class="user-info-popup">
      <div class="user-info-content">
        <!-- 头像和基本信息 -->
        <div class="user-header">
          <div class="avatar-container" :style="!userInfo.avatar ? { background: getAvatarBackground } : {}">
            <img v-if="userInfo.avatar" :src="userInfo.avatar" alt="avatar" class="user-avatar" />
            <span v-else class="avatar-text">{{ userInfo.nickName ? userInfo.nickName.charAt(0) : '' }}</span>
          </div>
          <div class="user-name">{{ userInfo.nickName }}</div>
          <div class="user-id">{{ userInfo.phonenumber }}</div>
        </div>

        <!-- 用户部门信息 -->
        <div class="info-section">
          <div class="info-label">所属部门:</div>
          <div class="info-value">{{ userInfo.dept.deptName }}</div>
        </div>

        <!-- 用户角色 -->
        <div class="info-section">
          <div class="info-label">用户角色</div>
          <div class="role-tag">{{ userInfo.roles[0].roleName }}</div>
        </div>

        <!-- 修改密码按钮 -->
        <div class="password-section">
          <v-btn block variant="outlined" color="#1976D2" class="password-btn" @click="showPasswordDialog" elevation="0">
            <v-icon start>mdi-lock-outline</v-icon>
            修改密码
          </v-btn>
        </div>

        <!-- 退出登录按钮 -->
        <div class="logout-section">
          <v-btn block variant="outlined" color="#333" class="logout-btn" @click="showLogoutConfirm" elevation="0">
            <v-icon start>mdi-logout</v-icon>
            退出登录
          </v-btn>
        </div>
      </div>
    </v-card>

    <!-- 退出确认对话框 -->
    <v-dialog v-model="logoutConfirmVisible" max-width="380" transition="dialog-bottom-transition">
      <v-card class="logout-confirm-dialog">
        <v-card-text class="text-center">
          <v-icon color="error" size="64" class="mb-4">mdi-logout-variant</v-icon>
          <div class="text-h5 font-weight-bold mb-3">确认退出登录？</div>
          <div class="text-body-1 text-grey">退出后需要重新登录才能使用系统</div>
        </v-card-text>
        <v-card-actions class="pb-6 px-6">
          <v-spacer></v-spacer>
          <v-btn color="grey-darken-1" variant="tonal" class="action-btn" @click="logoutConfirmVisible = false">
            取消
          </v-btn>
          <v-btn color="error" variant="elevated" class="action-btn" @click="confirmLogout">
            确认退出
          </v-btn>
          <v-spacer></v-spacer>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 修改密码对话框 -->
    <v-dialog v-model="passwordDialogVisible" max-width="450" transition="dialog-bottom-transition">
      <v-card class="password-dialog">
        <v-card-title class="text-center pt-6 pb-0">
          <div class="password-header">
            <div class="password-icon-wrapper">
              <v-icon color="#fff" size="24">mdi-lock-reset</v-icon>
            </div>
            <h2 class="text-h6 font-weight-bold mt-2 mb-0">修改密码</h2>
          </div>
        </v-card-title>
        <v-card-text class="pt-2 px-6">
          <v-form ref="passwordFormRef" v-model="passwordFormValid" @submit.prevent="submitPasswordChange">
            <!-- 旧密码 -->
            <v-text-field
              v-model="passwordForm.oldPassword"
              :rules="passwordRules.oldPassword"
              label="当前密码"
              variant="outlined"
              :type="showOldPassword ? 'text' : 'password'"
              density="comfortable"
              placeholder="请输入当前密码"
              :append-inner-icon="showOldPassword ? 'mdi-eye-off' : 'mdi-eye'"
              @click:append-inner="showOldPassword = !showOldPassword"
              class="mb-3 rounded-lg password-field"
              prepend-inner-icon="mdi-lock-outline"
              hide-details
            ></v-text-field>
            
            <!-- 新密码 -->
            <v-text-field
              v-model="passwordForm.newPassword"
              :rules="passwordRules.newPassword"
              label="新密码"
              variant="outlined"
              :type="showNewPassword ? 'text' : 'password'"
              density="comfortable"
              placeholder="请输入新密码"
              :append-inner-icon="showNewPassword ? 'mdi-eye-off' : 'mdi-eye'"
              @click:append-inner="showNewPassword = !showNewPassword"
              class="mb-3 rounded-lg password-field"
              prepend-inner-icon="mdi-lock-plus-outline"
              hide-details
            ></v-text-field>
            
            <!-- 确认新密码 -->
            <v-text-field
              v-model="passwordForm.confirmPassword"
              :rules="passwordRules.confirmPassword"
              label="确认新密码"
              variant="outlined"
              :type="showConfirmPassword ? 'text' : 'password'"
              density="comfortable"
              placeholder="请再次输入新密码"
              :append-inner-icon="showConfirmPassword ? 'mdi-eye-off' : 'mdi-eye'"
              @click:append-inner="showConfirmPassword = !showConfirmPassword"
              class="mb-3 rounded-lg password-field"
              prepend-inner-icon="mdi-lock-check-outline"
              hide-details
            ></v-text-field>

            <!-- 密码规则提示 -->
            <div class="password-compact-rules mb-4">
              <div class="password-rule-row">
                <div class="rule-item" :class="{ active: passwordStrength.length }">
                  <v-icon size="14" :color="passwordStrength.length ? 'success' : 'grey-lighten-1'" class="mr-1">
                    {{ passwordStrength.length ? 'mdi-check-circle' : 'mdi-circle-outline' }}
                  </v-icon>
                  <span>长度≥8位</span>
                </div>
                <div class="rule-item" :class="{ active: passwordStrength.number }">
                  <v-icon size="14" :color="passwordStrength.number ? 'success' : 'grey-lighten-1'" class="mr-1">
                    {{ passwordStrength.number ? 'mdi-check-circle' : 'mdi-circle-outline' }}
                  </v-icon>
                  <span>包含数字</span>
                </div>
              </div>
              <div class="password-rule-row">
                <div class="rule-item" :class="{ active: passwordStrength.lowercase }">
                  <v-icon size="14" :color="passwordStrength.lowercase ? 'success' : 'grey-lighten-1'" class="mr-1">
                    {{ passwordStrength.lowercase ? 'mdi-check-circle' : 'mdi-circle-outline' }}
                  </v-icon>
                  <span>小写字母</span>
                </div>
                <div class="rule-item" :class="{ active: passwordStrength.uppercase }">
                  <v-icon size="14" :color="passwordStrength.uppercase ? 'success' : 'grey-lighten-1'" class="mr-1">
                    {{ passwordStrength.uppercase ? 'mdi-check-circle' : 'mdi-circle-outline' }}
                  </v-icon>
                  <span>大写字母</span>
                </div>
              </div>
              <v-progress-linear
                :model-value="passwordStrengthScore"
                :color="passwordStrengthLevel.color"
                height="4"
                rounded
                class="mt-1"
              ></v-progress-linear>
            </div>
          </v-form>
        </v-card-text>
        
        <v-card-actions class="pb-4 px-6">
          <v-spacer></v-spacer>
          <v-btn 
            color="grey-darken-1" 
            variant="text" 
            @click="passwordDialogVisible = false"
            size="small"
            density="comfortable"
          >
            取消
          </v-btn>
          <v-btn 
            :color="passwordStrengthLevel.color" 
            variant="elevated" 
            :disabled="!passwordFormValid"
            @click="submitPasswordChange"
            :loading="passwordSubmitting"
            size="small"
            density="comfortable"
          >
            确认
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-dialog>
</template>

<script setup>
import { userUpdate } from '@/api/user';
import { ref, computed, onMounted, reactive, watch, onUnmounted } from 'vue';
import useUserStore from '@/store/modules/user';
import { useRouter } from 'vue-router';
import { getAvatarColor } from '@/utils/avatarColor';
import { ElMessage } from 'element-plus';

const router = useRouter();
const userStore = useUserStore();
const visible = ref(false);
const logoutConfirmVisible = ref(false);
const userInfo = ref({});
const passwordDialogVisible = ref(false);
const passwordFormRef = ref(null);
const passwordFormValid = ref(false);
const passwordSubmitting = ref(false);

// 显示/隐藏密码状态
const showOldPassword = ref(false);
const showNewPassword = ref(false);
const showConfirmPassword = ref(false);

// 更新用户信息
const updateUserInfo = async () => {
  console.log('UserInfoPopup: 开始更新用户信息');
  try {
    // 如果对话框可见，从store重新获取最新的用户信息
    if (visible.value) {
      userInfo.value = userStore.users || {};
      console.log('UserInfoPopup: 用户信息已更新:', userInfo.value);
    }
  } catch (error) {
    console.error('UserInfoPopup: 更新用户信息失败:', error);
  }
};

// 当组件挂载时设置事件监听器
onMounted(() => {
  // 监听用户信息更新事件
  window.addEventListener('userInfoUpdated', updateUserInfo);
});

// 组件卸载时移除事件监听器
onUnmounted(() => {
  window.removeEventListener('userInfoUpdated', updateUserInfo);
});

// 密码表单数据
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 密码强度监测
const passwordStrength = computed(() => {
  const password = passwordForm.newPassword;
  return {
    length: password.length >= 8,
    number: /\d/.test(password),
    lowercase: /[a-z]/.test(password),
    uppercase: /[A-Z]/.test(password)
  };
});

// 计算密码强度分数和级别
const passwordStrengthScore = computed(() => {
  const strength = passwordStrength.value;
  let score = 0;
  
  // 计算满足条件的数量
  if (strength.length) score += 25;
  if (strength.number) score += 25;
  if (strength.lowercase) score += 25;
  if (strength.uppercase) score += 25;
  
  return score;
});

// 密码强度级别
const passwordStrengthLevel = computed(() => {
  const score = passwordStrengthScore.value;
  
  if (score === 0) {
    return { text: '未输入', color: 'grey', class: 'text-grey' };
  } else if (score < 50) {
    return { text: '弱', color: 'error', class: 'text-error' };
  } else if (score < 100) {
    return { text: '中等', color: 'warning', class: 'text-warning' };
  } else {
    return { text: '强', color: 'success', class: 'text-success' };
  }
});

// 密码表单验证规则
const passwordRules = {
  oldPassword: [
    v => !!v || '请输入当前密码',
  ],
  newPassword: [
    v => !!v || '请输入新密码',
    v => v.length >= 8 || '密码长度至少为8位',
    v => /\d/.test(v) || '密码必须包含数字',
    v => /[a-z]/.test(v) || '密码必须包含小写字母',
    v => /[A-Z]/.test(v) || '密码必须包含大写字母',
    v => v !== passwordForm.oldPassword || '新密码不能与旧密码相同'
  ],
  confirmPassword: [
    v => !!v || '请再次输入新密码',
    v => v === passwordForm.newPassword || '两次输入的密码不一致'
  ]
};

// 计算属性获取用户部门
const userDept = computed(() => {
  return '中心/综合部'; // 这里需要根据实际数据来源修改
});

// 计算属性获取用户角色
const userRole = computed(() => {
  return '管理员'; // 这里需要根据实际数据来源修改
});

// 获取用户头像背景色
const getAvatarBackground = computed(() => {
  return userInfo.value && userInfo.value.userId ? 
    getAvatarColor(userInfo.value.userId) : 
    getAvatarColor('default');
});

// 打印用户登录信息
const printUserInfo = () => {
  console.log('用户登录信息:', userStore.users);
  userInfo.value = userStore.users;
};

// 组件挂载时打印用户信息
onMounted(() => {
  printUserInfo();
});

// 打开弹窗方法
const open = () => {
  // 每次打开时重新获取最新的用户信息
  userInfo.value = userStore.users || {};
  visible.value = true;
};

// 关闭弹窗方法
const close = () => {
  visible.value = false;
};

// 显示退出确认框
const showLogoutConfirm = () => {
  logoutConfirmVisible.value = true;
};

// 确认退出
const confirmLogout = () => {
  logoutConfirmVisible.value = false;
  userStore.logOut().then(() => {
    router.push('/login');
  });
};

// 显示修改密码对话框
const showPasswordDialog = () => {
  // 重置表单
  if (passwordFormRef.value) {
    passwordFormRef.value.reset();
  }
  passwordDialogVisible.value = true;
};

// 提交密码修改
const submitPasswordChange = async () => {
  if (!passwordFormValid.value) return;
  
  try {
    passwordSubmitting.value = true;
    
    // 调用修改密码API，使用userUpdate接口
    const params = {
      oldPassword: passwordForm.oldPassword,
      newPassword: passwordForm.newPassword
    };
    
    await userUpdate(params);
    
    // 提示成功
    ElMessage.success('密码修改成功，请重新登录');
    
    // 关闭对话框
    passwordDialogVisible.value = false;
    
    // 调用退出登录接口并跳转到登录页
    await userStore.logOut();
    router.push('/login');
    
  } catch (error) {
    console.error('修改密码失败:', error);
    ElMessage.error(error.message || '修改密码失败，请重试');
  } finally {
    passwordSubmitting.value = false;
  }
};

// 暴露方法供父组件调用
defineExpose({
  open
});
</script>

<style scoped>
.user-info-popup {
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.user-info-content {
  padding: 32px;
}

.user-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32px;
  position: relative;
}

.avatar-container {
  width: 88px;
  height: 88px;
  border-radius: 50%;
  background: linear-gradient(145deg, #f5f5f5, #ffffff);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  border: 2px solid #fff;
}

.avatar-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.user-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 6px;
  letter-spacing: 0.5px;
}

.user-id {
  font-size: 14px;
  color: #666;
  background: #f5f7fa;
  padding: 4px 12px;
  border-radius: 12px;
}

.info-section {
  background: #f8fafc;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.info-section:hover {
  background: #f0f3f7;
  transform: translateY(-1px);
}

.info-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.info-value {
  font-size: 15px;
  color: #1a1a1a;
  font-weight: 500;
}

.role-tag {
  display: inline-block;
  padding: 4px 16px;
  background: linear-gradient(135deg, #FFB800, #FF9500);
  color: #fff;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(255, 184, 0, 0.2);
}

.password-section {
  margin-bottom: 20px;
}

.password-btn {
  height: 44px !important;
  font-size: 15px !important;
  font-weight: 500 !important;
  letter-spacing: 1px;
  border: none !important;
  background: linear-gradient(to right, #64B5F6, #1976D2) !important;
  color: white !important;
  transition: all 0.3s ease !important;
  border-radius: 12px !important;
}

.password-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(25, 118, 210, 0.2) !important;
  opacity: 0.95;
}

.password-btn:active {
  transform: translateY(0);
}

.logout-section {
  margin-top: 12px;
}

.logout-btn {
  height: 44px !important;
  font-size: 15px !important;
  font-weight: 500 !important;
  letter-spacing: 1px;
  border: none !important;
  background: linear-gradient(to right, #ff4d4d, #f23030) !important;
  color: white !important;
  transition: all 0.3s ease !important;
  border-radius: 12px !important;
}

.logout-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(242, 48, 48, 0.2) !important;
  opacity: 0.95;
}

.logout-btn:active {
  transform: translateY(0);
}

.logout-confirm-dialog {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
}

.logout-confirm-dialog .v-card-text {
  padding: 32px 24px 24px !important;
}

.password-dialog {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
}

.password-header {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.password-icon-wrapper {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4A90E2, #6AB7FF);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.25);
  margin-bottom: 8px;
}

.password-field {
  border-radius: 8px;
  transition: all 0.2s ease;
}

.password-field:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.password-compact-rules {
  background-color: #f9fafc;
  padding: 12px;
  border-radius: 8px;
}

.password-rule-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
}

.rule-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #757575;
  transition: all 0.2s ease;
}

.rule-item.active {
  color: #333;
  font-weight: 500;
}

.dialog-buttons {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 12px;
}

.change-btn {
  height: 50px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px !important;
  width: 100%;
  transition: all 0.3s ease !important;
  text-transform: none !important;
}

.change-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15) !important;
}

.cancel-btn {
  height: 50px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  width: 100%;
  letter-spacing: 0.5px !important;
  transition: all 0.3s ease !important;
  text-transform: none !important;
}

.avatar-text {
  font-size: 32px;
  font-weight: 600;
  color: #fff;
  text-transform: uppercase;
}

/* 对话框动画效果 */
.v-dialog-enter-active,
.v-dialog-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.v-dialog-enter-from,
.v-dialog-leave-to {
  opacity: 0;
  transform: scale(0.9) translateY(20px);
}
</style> 