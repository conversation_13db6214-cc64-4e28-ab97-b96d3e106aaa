import { createWebHistory, createRouter } from 'vue-router'
/* Layout */
import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: '/chat-file',
    children: [
      {
        path: '/chat-file',
        component: () => import('@/views/HomeView.vue'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true, permissions: ['ai:knowledge:cockpit'] }
      }
    ]
  },
  {
    path: '/manage/user',
    component: Layout,
    children: [
      {
        path: '/manage/user',
        component: () => import('@/views/manage/user/index.vue'),
        name: 'ManageUser',
        meta: { title: '人员管理', icon: 'mdi-cube', affix: true, permissions: ['ai:knowledge:userManage'] }
      }
    ]
  },
  {
    path: '/chat',
    component: Layout,
    children: [
      {
        path: '/chat',
        component: () => import('@/views/chatRoom.vue'),
        name: 'QAView',
        meta: { title: '智能问答', icon: 'dashboard', affix: true, permissions: ['ai:knowledge:cockpit'] }
      }
    ]
  },
  {
    path: '/documents',
    component: Layout,
    children: [
      {
        path: '/documents',
        component: () => import('@/views/Document.vue'),
        name: 'Document',
        meta: { title: '智能文档', icon: 'dashboard', affix: true, permissions: ['ai:knowledge:file'] }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  },
  {
    path: '/scheduleSheet',
    component: Layout,
    // name: "scheduleSheet",
    hidden: true,
    component: () => import('@/views/arr/scheduleSheet/index.vue'),
  },
  // {
  //   path: '/system/user',
  //   component: Layout,
  //   children: [
  //     {
  //       path: '/system/user',
  //       component: () => import('@/views/system/user/index'),
  //       name: 'User',
  //       meta: { title: '用户管理', icon: 'user', permissions: ['system:user:list'] }
  //     }
  //   ]
  // },
  // {
  //   path: '/system/role',
  //   component: Layout,
  //   children: [
  //     {
  //       path: '/system/role',
  //       component: () => import('@/views/system/role/index'),
  //       name: 'Role',
  //       meta: { title: '角色管理', icon: 'peoples', permissions: ['system:role:list'] }
  //     }
  //   ]
  // },
  // {
  //   path: '/system/dept',
  //   component: Layout,
  //   children: [
  //     {
  //       path: '/system/dept',
  //       component: () => import('@/views/system/dept/index'),
  //       name: 'Dept',
  //       meta: { title: '部门管理', icon: 'tree', permissions: ['system:dept:list'] }
  //     }
  //   ]
  // },
  // {
  //   path: '/system/menu',
  //   component: Layout,
  //   children: [
  //     {
  //       path: '/system/menu',
  //       component: () => import('@/views/system/menu/index'),
  //       name: 'Menu',
  //       meta: { title: '菜单管理', icon: 'tree-table', permissions: ['system:menu:list'] }
  //     }
  //   ]
  // },
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' }
      }
    ]
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    permissions: ['monitor:job:list'],
    children: [
      {
        path: 'index/:jobId(\\d+)',
        component: () => import('@/views/monitor/job/log'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' }
      }
    ]
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'index/:tableId(\\d+)',
        component: () => import('@/views/tool/gen/editTable'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }
      }
    ]
  },
  {
    path: '/arr/workGroup-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user',
        component: () => import('@/views/arr/workGroup/authUser'),
        name: 'WorkAuthUser',
        meta: { title: '分配用户', activeMenu: '/arr/workGroup' }
      }
    ]
  },
]

const routes = [
  ...constantRoutes,
  ...dynamicRoutes,
  {
    path: '/pdf-preview',
    name: 'PdfPreview',
    component: () => import('@/views/PdfPreview.vue'),
    meta: {
      title: 'PDF预览'
    }
  }
]

import routerPlus from './routerPlus'
import useUploadStore from '@/store/modules/upload'
import { ElMessageBox } from 'element-plus'

const router = createRouter({
  history: createWebHistory(),
  routes: [...routes, ...routerPlus],
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  },
});

// 添加导航守卫 - 已移除上传检查
router.beforeEach(async (to, from, next) => {
  // 移除了文件上传中切换页面的限制，允许用户在文件上传过程中自由切换页面
  // 不再检查上传状态，也不再显示确认对话框
  next();
})

export default router;
