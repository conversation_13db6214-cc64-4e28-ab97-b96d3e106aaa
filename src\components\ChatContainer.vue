<template>
  <!-- 聊天容器 -->
  <v-card id="chatSection" class="chat-container" :class="{ 'v2-mode': selectedMode.value == 'v2' }">
    <v-card-title class="py-3 py-sm-4 px-4 px-sm-6 d-flex justify-space-between align-center">
      <div class="d-flex align-center">
        <v-icon left class="mr-2 d-none d-sm-inline">mdi-message-text</v-icon>
        智能问答
      </div>
      <div class="d-flex">
        <v-btn variant="text" class="ml-2" :icon="$vuetify.display.xs"
          @click="!isProcessing && createNewSession(true)"
          :disabled="isProcessing" :class="{ 'unselectable-btn': isProcessing }">
          <v-icon>mdi-plus</v-icon>
          <span class="d-none d-sm-inline ml-1">开启新对话</span>
        </v-btn>
      </div>
    </v-card-title>

    <div class="chat-messages" ref="chatContainer">
      <div v-for="(msg, index) in messages" :key="index" :class="['message', msg.type]">
        <div class="message-avatar" v-if="msg.type == 'system'">
          <v-avatar color="primary" :size="$vuetify.display.xs ? 32 : 40">
            <v-icon color="white" :size="$vuetify.display.xs ? 16 : 24">mdi-robot</v-icon>
          </v-avatar>
        </div>
        <div class="message-avatar" v-if="msg.type == 'user'">
          <v-avatar color="blue-lighten-4" :size="$vuetify.display.xs ? 32 : 40">
            <v-icon color="blue-darken-1" :size="$vuetify.display.xs ? 16 : 24">mdi-account</v-icon>
          </v-avatar>
        </div>
        <div class="message-content" :class="{ 'table-container': msg.table }">
          <!-- 思考过程直接放到上面 -->
          <template v-if="msg.thinking">
            <div v-html="msg.content"></div>
          </template>

          <!-- 非思考中状态显示内容 -->
          <template v-else>
            <!-- 用户消息直接显示内容 -->
            <div v-if="msg.type === 'user'" v-html="msg.content" class="formal-content" style="max-width: 100%; word-wrap: break-word; overflow-wrap: break-word;"></div>
            <!-- 系统消息直接显示当前内容，支持流式输出 -->
            <template v-else>
              <div v-html="msg.content" class="formal-content" style="max-width: 100%; word-wrap: break-word; overflow-wrap: break-word;"></div>
            </template>
          </template>

          <!-- 参考文档列表 -->
          <div v-if="msg.type === 'system' && msg.docAggs && msg.docAggs.length > 0" class="reference-docs-container">
            <div class="reference-docs-header">
              <span>参考文档</span>
            </div>
            <div class="reference-docs-list">
              <div v-for="(doc, docIndex) in msg.docAggs" :key="docIndex" class="reference-doc-item" @click="viewFileInline(doc)">
                <v-icon size="small" color="primary" class="mr-2">mdi-file-document-outline</v-icon>
                <span class="reference-doc-name">{{ doc.doc_name }}</span>
              </div>
            </div>
          </div>

          <!-- 建议问题 - 只在i暖城问数模式下显示 -->
          <div v-if="msg.welcome && msg.suggestedQuestions && msg.suggestedQuestions.length > 0 && selectedMode.value === 'v2'"
            class="suggested-questions">
            <div v-for="(question, qIndex) in msg.suggestedQuestions" :key="qIndex" class="suggested-question"
              @click="askSuggestedQuestion(question)">
              {{ question }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 返回顶部按钮 -->
      <div class="back-to-top-btn" v-show="showBackToTopBtn" @click="scrollToTop">
        <v-icon color="white">mdi-arrow-up</v-icon>
      </div>
    </div>

    <div class="chat-input">
      <div class="chat-input-container">
        <v-text-field v-model="userMessage"
          :placeholder="selectedMode.value == 'v2' ? '请输入深度检索内容...' : '请输入您的问题...'"
          @keyup.enter="!isProcessing && userMessage.trim() && sendMessage()" hide-details variant="outlined"
          density="comfortable" bg-color="white" :disabled="false" class="input-field"
          :class="{ 'v2-input': selectedMode.value == 'v2' }">
        </v-text-field>
      </div>
      <div class="bottom-input-row">
        <div class="mode-selector">
          <v-select v-model="selectedMode" @update:model-value="handleModeChange" :items="modeOptions" variant="plain" density="compact" hide-details
            class="mode-select" :disabled="isProcessing" bg-color="transparent" return-object item-title="text"
            item-value="value" item-value-key="value">
            <template v-slot:selection="{ item }">
              <div class="d-flex align-center mode-label">
                <v-icon size="x-small" color="primary" class="mr-1">
                  {{ item.raw.icon }}
                </v-icon>
                {{ item.raw.text }}
              </div>
            </template>
            <template v-slot:item="{ props, item }">
              <v-list-item v-bind="props">
                <template v-slot:prepend>
                  <v-icon size="x-small" color="primary">
                    {{ item.raw.icon }}
                  </v-icon>
                </template>
              </v-list-item>
            </template>
          </v-select>
        </div>

        <!-- 知识库选择器 - 只在日常问答模式下显示 -->
        <div class="kb-selector" v-if="selectedMode.value === 'r1'">
          <v-select v-model="selectedKnowledgeBase" :items="knowledgeBaseOptions" variant="plain"
            density="compact" hide-details class="kb-select" :disabled="isProcessing" bg-color="transparent"
            return-object item-title="text" item-value="value" @update:model-value="handleKnowledgeBaseChange">
            <template v-slot:selection="{ item }">
              <div class="d-flex align-center kb-label">
                <v-icon size="x-small" color="primary" class="mr-1">
                  mdi-database
                </v-icon>
                <span class="kb-label-text">{{ item.raw.text }}</span>
              </div>
            </template>
            <template v-slot:item="{ props }">
              <v-list-item v-bind="props">
                <template v-slot:prepend>
                  <v-icon size="small" color="primary">
                    mdi-database
                  </v-icon>
                </template>
              </v-list-item>
            </template>
          </v-select>
        </div>

        <div class="input-disclaimer" :class="{ 'v2-disclaimer': selectedMode.value == 'v2' }">
          <v-icon size="small" :color="selectedMode.value == 'v2' ? 'green-darken-1' : 'grey'">
            {{ selectedMode.value == 'v2' ? 'mdi-chart-box-outline' : 'mdi-information-outline' }}
          </v-icon>
          <span>{{ selectedMode.value == 'v2' ? '提示: 您可以询问实地问题分析，实践站所查询，文明 实践活动，诚信建设，文明培育等问题' : '提示: 您可以根据知识库内容进行提问' }}</span>
        </div>

        <button class="send-button" :class="{ 'processing': isProcessing }" @click="handleButtonClick"
          :disabled="!userMessage.trim() && !isProcessing">
          {{ isProcessing ? '停止' : '发送' }}
        </button>
      </div>
    </div>
  </v-card>
</template>

<script setup>
import { modeOptions } from '@/utils/chatUtils'

// Props
const props = defineProps({
  selectedMode: {
    type: Object,
    default: () => ({ value: 'r1', text: '日常问答', icon: 'mdi-brain' })
  },
  isProcessing: {
    type: Boolean,
    default: false
  },
  messages: {
    type: Array,
    default: () => []
  },
  showBackToTopBtn: {
    type: Boolean,
    default: false
  },
  userMessage: {
    type: String,
    default: ''
  },
  selectedKnowledgeBase: {
    type: Object,
    default: null
  },
  knowledgeBaseOptions: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits([
  'createNewSession',
  'viewFileInline',
  'askSuggestedQuestion',
  'scrollToTop',
  'sendMessage',
  'handleModeChange',
  'handleKnowledgeBaseChange',
  'handleButtonClick',
  'update:userMessage'
])

// Computed
const userMessage = computed({
  get: () => props.userMessage,
  set: (value) => emit('update:userMessage', value)
})

// Methods
const createNewSession = (showToast) => {
  emit('createNewSession', showToast)
}

const viewFileInline = (doc) => {
  emit('viewFileInline', doc)
}

const askSuggestedQuestion = (question) => {
  emit('askSuggestedQuestion', question)
}

const scrollToTop = () => {
  emit('scrollToTop')
}

const sendMessage = () => {
  emit('sendMessage')
}

const handleModeChange = (mode) => {
  emit('handleModeChange', mode)
}

const handleKnowledgeBaseChange = (kb) => {
  emit('handleKnowledgeBaseChange', kb)
}

const handleButtonClick = () => {
  emit('handleButtonClick')
}
</script>

<style scoped>
/* 聊天容器样式 */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.chat-container.v2-mode {
  border: 2px solid #4caf50;
}

/* 聊天消息区域 */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background-color: #f8f9fa;
  position: relative;
}

.message {
  display: flex;
  margin-bottom: 16px;
  gap: 12px;
}

.message.user {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  min-width: 0;
  background-color: #fff;
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message.user .message-content {
  background-color: #e3f2fd;
  margin-left: 20%;
}

.message.system .message-content {
  margin-right: 20%;
}

.formal-content {
  line-height: 1.6;
  color: #333;
}

/* 参考文档样式 */
.reference-docs-container {
  margin-top: 16px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.reference-docs-header {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.reference-docs-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.reference-doc-item {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  background-color: #fff;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.reference-doc-item:hover {
  background-color: #e3f2fd;
}

.reference-doc-name {
  font-size: 13px;
  color: #1976d2;
  text-decoration: none;
}

.reference-doc-name:hover {
  text-decoration: underline;
}

/* 建议问题样式 */
.suggested-questions {
  margin-top: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggested-question {
  background-color: #e8f5e9;
  color: #2e7d32;
  padding: 8px 12px;
  border-radius: 16px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid #c8e6c9;
}

.suggested-question:hover {
  background-color: #c8e6c9;
  transform: translateY(-1px);
}

/* 返回顶部按钮 */
.back-to-top-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #1677ff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 100;
  transition: all 0.3s ease;
}

.back-to-top-btn:hover {
  background-color: #0d5ecf;
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 聊天输入区域 */
.chat-input {
  background-color: #fff;
  border-top: 1px solid #e0e0e0;
  padding: 16px;
}

.chat-input-container {
  margin-bottom: 12px;
}

.input-field {
  border-radius: 8px;
}

.input-field.v2-input {
  border: 2px solid #4caf50;
}

.bottom-input-row {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.mode-selector {
  flex-shrink: 0;
}

.mode-select {
  min-width: 120px;
}

.mode-label {
  font-size: 13px;
  font-weight: 500;
}

.kb-selector {
  flex-shrink: 0;
}

.kb-select {
  min-width: 150px;
}

.kb-label {
  font-size: 13px;
}

.kb-label-text {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.input-disclaimer {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
  min-width: 200px;
}

.input-disclaimer.v2-disclaimer {
  color: #2e7d32;
}

.send-button {
  background-color: #1677ff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 60px;
}

.send-button:hover:not(:disabled) {
  background-color: #0d5ecf;
}

.send-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.send-button.processing {
  background-color: #f44336;
}

.send-button.processing:hover {
  background-color: #d32f2f;
}

.unselectable-btn {
  opacity: 0.4;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-messages {
    padding: 12px;
  }

  .message.user .message-content {
    margin-left: 10%;
  }

  .message.system .message-content {
    margin-right: 10%;
  }

  .bottom-input-row {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .mode-selector,
  .kb-selector {
    width: 100%;
  }

  .input-disclaimer {
    min-width: auto;
    text-align: center;
  }

  .send-button {
    width: 100%;
    padding: 12px;
  }

  .back-to-top-btn {
    width: 40px;
    height: 40px;
    bottom: 20px;
    right: 20px;
  }

  .suggested-questions {
    justify-content: center;
  }

  .suggested-question {
    font-size: 12px;
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .chat-input {
    padding: 12px;
  }

  .message-content {
    padding: 10px 12px;
  }

  .reference-docs-container {
    padding: 10px;
  }
}
</style>
