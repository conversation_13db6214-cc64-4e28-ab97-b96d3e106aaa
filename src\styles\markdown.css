/* Markdown内容样式 */
.markdown-content {
  line-height: 1.5; /* 减少整体行高，从1.6改为1.5 */
  color: #333;
  font-size: 16px;
}

/* 处理换行符，避免过大间距 */
.markdown-content br {
  line-height: 1.2; /* 单独的换行符使用更小的行高 */
}

/* 优化文本块的间距 */
.markdown-content > *:first-child {
  margin-top: 0; /* 第一个元素不要上边距 */
}

.markdown-content > *:last-child {
  margin-bottom: 0; /* 最后一个元素不要下边距 */
}

/* 紧凑的文本显示 */
.markdown-content {
  word-spacing: normal;
  letter-spacing: normal;
}

/* 优化内联元素的间距 */
.markdown-content strong,
.markdown-content em,
.markdown-content code {
  line-height: inherit; /* 继承父元素的行高 */
}

/* 减少不必要的垂直间距 */
.markdown-content * {
  margin-block-start: 0;
  margin-block-end: 0;
}

/* 重新设置需要间距的元素 */
.markdown-content h1, .markdown-content h2, .markdown-content h3,
.markdown-content h4, .markdown-content h5, .markdown-content h6 {
  margin-block-start: 1em;
  margin-block-end: 0.5em;
}

.markdown-content p {
  margin-block-start: 0.5em;
  margin-block-end: 0.5em;
}

.markdown-content ul, .markdown-content ol {
  margin-block-start: 0.5em;
  margin-block-end: 0.5em;
}

/* 标题样式 */
.markdown-content h1 {
  font-size: 2em;
  font-weight: bold;
  margin: 24px 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e1e4e8;
  color: #24292e;
}

.markdown-content h2 {
  font-size: 1.5em;
  font-weight: bold;
  margin: 20px 0 12px 0;
  padding-bottom: 6px;
  border-bottom: 1px solid #e1e4e8;
  color: #24292e;
}

.markdown-content h3 {
  font-size: 1.25em;
  font-weight: bold;
  margin: 16px 0 8px 0;
  color: #24292e;
}

.markdown-content h4 {
  font-size: 1em;
  font-weight: bold;
  margin: 12px 0 6px 0;
  color: #24292e;
}

.markdown-content h5, .markdown-content h6 {
  font-size: 0.875em;
  font-weight: bold;
  margin: 10px 0 5px 0;
  color: #24292e;
}

/* 段落样式 */
.markdown-content p {
  margin: 8px 0; /* 减少段落间距，从12px改为8px */
  line-height: 1.5; /* 稍微减少行高，从1.6改为1.5 */
}

/* 连续段落间距优化 */
.markdown-content p + p {
  margin-top: 6px; /* 连续段落间距更小 */
}

/* 列表样式 */
.markdown-content ul, .markdown-content ol {
  margin: 8px 0; /* 减少列表外边距，从12px改为8px */
  padding-left: 24px;
}

.markdown-content li {
  margin: 2px 0; /* 减少列表项间距，从4px改为2px */
  line-height: 1.4; /* 稍微减少列表项行高 */
}

.markdown-content ul li {
  list-style-type: disc;
}

.markdown-content ol li {
  list-style-type: decimal;
}

/* 嵌套列表 */
.markdown-content ul ul, .markdown-content ol ol, .markdown-content ul ol, .markdown-content ol ul {
  margin: 4px 0;
}

.markdown-content ul ul li {
  list-style-type: circle;
}

.markdown-content ul ul ul li {
  list-style-type: square;
}

/* 引用样式 */
.markdown-content blockquote {
  margin: 16px 0;
  padding: 0 16px;
  border-left: 4px solid #dfe2e5;
  background-color: #f6f8fa;
  color: #6a737d;
  font-style: italic;
}

.markdown-content blockquote p {
  margin: 8px 0;
}

/* 代码样式 */
.markdown-content code {
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
  font-size: 85%;
  margin: 0;
  padding: 0.2em 0.4em;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
}

.markdown-content pre {
  background-color: #f6f8fa;
  border-radius: 6px;
  font-size: 85%;
  line-height: 1.45;
  overflow: auto;
  padding: 16px;
  margin: 16px 0;
}

.markdown-content pre code {
  background-color: transparent;
  border: 0;
  display: inline;
  line-height: inherit;
  margin: 0;
  max-width: auto;
  overflow: visible;
  padding: 0;
  word-wrap: normal;
}

/* 表格样式 */
.markdown-content table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
  margin: 16px 0;
  overflow: auto;
  display: block;
  white-space: nowrap;
}

.markdown-content table th,
.markdown-content table td {
  border: 1px solid #dfe2e5;
  padding: 6px 13px;
  text-align: left;
}

.markdown-content table th {
  background-color: #f6f8fa;
  font-weight: 600;
}

.markdown-content table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

/* 链接样式 */
.markdown-content a {
  color: #0366d6;
  text-decoration: none;
  transition: color 0.2s ease; /* 添加颜色过渡效果 */
}

.markdown-content a:hover {
  text-decoration: underline;
  color: #0256cc; /* 悬停时颜色稍深 */
}

.markdown-content a:visited {
  color: #6f42c1;
}

/* 外部链接样式 - 添加小图标提示会在新标签页打开 */
.markdown-content a[target="_blank"]::after {
  content: " ↗";
  font-size: 0.8em;
  color: #586069;
  margin-left: 2px;
  opacity: 0.7;
}

.markdown-content a[target="_blank"]:hover::after {
  opacity: 1;
}

/* 图片样式 */
.markdown-content img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 8px 0;
}

/* 分割线样式 */
.markdown-content hr {
  border: none;
  border-top: 1px solid #e1e4e8;
  margin: 24px 0;
}

/* 强调样式 */
.markdown-content strong {
  font-weight: 600;
  color: #24292e;
}

.markdown-content em {
  font-style: italic;
}

/* 删除线 */
.markdown-content del {
  text-decoration: line-through;
  color: #6a737d;
}

/* 思考块样式 */
.markdown-content .thinking-block {
  background-color: #f5f5f5;
  color: #888;
  padding: 12px 16px;
  margin: 8px 0;
  border-radius: 6px;
  font-style: italic;
  border-left: 3px solid #ddd;
  position: relative;
}

.markdown-content .thinking-block::before {
  content: "💭 思考过程";
  font-size: 12px;
  color: #666;
  font-weight: bold;
  display: block;
  margin-bottom: 8px;
  font-style: normal;
}

/* 任务列表样式 */
.markdown-content .task-list-item {
  list-style-type: none;
  margin-left: -20px;
}

.markdown-content .task-list-item input[type="checkbox"] {
  margin-right: 8px;
}

/* 键盘按键样式 */
.markdown-content kbd {
  background-color: #fafbfc;
  border: 1px solid #c6cbd1;
  border-bottom-color: #959da5;
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 #959da5;
  color: #444d56;
  display: inline-block;
  font-size: 11px;
  line-height: 10px;
  padding: 3px 5px;
  vertical-align: middle;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
}

/* 高亮文本 */
.markdown-content mark {
  background-color: #fff3cd;
  padding: 2px 4px;
  border-radius: 2px;
}

/* 脚注样式 */
.markdown-content .footnote {
  font-size: 0.875em;
  color: #6a737d;
}

.markdown-content .footnote-ref {
  color: #0366d6;
  text-decoration: none;
  font-weight: bold;
}

.markdown-content .footnote-ref:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .markdown-content {
    font-size: 14px;
  }
  
  .markdown-content h1 {
    font-size: 1.75em;
  }
  
  .markdown-content h2 {
    font-size: 1.5em;
  }
  
  .markdown-content h3 {
    font-size: 1.25em;
  }
  
  .markdown-content table {
    font-size: 12px;
  }
  
  .markdown-content pre {
    padding: 12px;
    font-size: 12px;
  }
  
  .markdown-content ul, .markdown-content ol {
    padding-left: 20px;
  }
}

/* 打印样式 */
@media print {
  .markdown-content {
    color: #000;
    background: #fff;
  }
  
  .markdown-content a {
    color: #000;
    text-decoration: underline;
  }
  
  .markdown-content .thinking-block {
    border: 1px solid #ccc;
    background: #f9f9f9;
  }
}
