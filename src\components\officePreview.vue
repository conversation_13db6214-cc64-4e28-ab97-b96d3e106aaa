<template>
	<div class="wrap" v-loading="loading">
	
	  <vue-office-docx v-if="fileInfo.fileType === 'docx'" 
		:src="fileInfo.filePath" style="height: 100%"
        @rendered="rendered" @error="HandlError">
      </vue-office-docx>
      
      <vue-office-pdf v-if="fileInfo.fileType === 'pdf'" 
	      :src="fileInfo.filePath" style="height: 100%" 
	      @rendered="rendered"  @error="HandlError">
      </vue-office-pdf>
      
      <vue-office-excel v-if="fileInfo.fileType === 'xlsx'" 
      	:src="fileInfo.filePath" style="height: 78vh"
        @rendered="rendered" @error="HandlError">
       </vue-office-excel>
       
       <vue-office-pptx v-if="fileInfo.fileType === 'pptx' || fileInfo.fileType === 'ppt'" 
      	:src="fileInfo.filePath" style="height: 100%"
        @rendered="rendered" @error="HandlError">
       </vue-office-pptx>
       
       <el-image v-if="fileInfo.fileType === 'image'" 
	       :src="fileInfo.filePath" alt="Preview"
	        @load="rendered" @error="HandlError" />
	        
	   <video v-if="fileInfo.fileType === 'video' && fileInfo.filePath"
   			 width="100%" controls controlslist="nodownload">
   			 /**  controlslist="nodownload" 隐藏下载按钮 */
	        <source :src="fileInfo.filePath"/>
	        Download the
	      	<a :href="fileInfo.filePath">MP4</a>
      </video>
      
	  <audio v-if="fileInfo.fileType === 'audio' && fileInfo.filePath"
          controls controlsList="nodownload" style="width: 100%;">
          <source :src="fileInfo.filePath" type="audio/mp3">
          您的浏览器不支持audio标签。
      </audio>
       
	</div>
</template>
<script setup>
import { ref } from 'vue';
import VueOfficeDocx from "@vue-office/docx";
import VueOfficeExcel from "@vue-office/excel";
import VueOfficePdf from "@vue-office/pdf";
import VueOfficePptx from "@vue-office/pptx";
//引入相关样式
import "@vue-office/docx/lib/index.css";
import '@vue-office/excel/lib/index.css'
// @vue-office/pptx 包中没有 CSS 文件，所以不需要导入

const props = defineProps({
 fileInfo: {
    type: Object,
    required: true
  }
});

const loading = ref(true);
/** rendered：渲染完成后调用 */
const rendered = () => {
  loading.value = false;
};
/** HandlError ：渲染失败后调用 */
const HandlError = (errorInfo) => {
  // 假设你已经配置了全局的 toast
  alert("该文件暂不支持在线预览");
  loading.value = false;
};

</script>
