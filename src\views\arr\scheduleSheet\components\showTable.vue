<template>
  <div style="position: relative">
    <el-table
      class="table_box"
      :data="tableData"
      style="width: 100%; padding-bottom: 4px"
      :max-height="tableHeight"
      border
      :header-row-style="{ height: '60px' }"
      :row-style="{ height: '50' }"
      :row-class-name="'rowStyle'"
      :cell-style="cellStyle"
      :span-method="spanMethod"
      @cell-click="cellClick"
      @cell-dblclick.native="cellContextmenu"
      @cell-contextmenu.native="rightClick"
    >
      <el-table-column
        :prop="item.prop"
        :label="item.label"
        v-for="(item, index) in tableHeader"
        :key="index"
        :width="index == 0 ? 100 : 80"
        fixed
        align="center"
      >
      </el-table-column>
      <el-table-column
        :prop="item.prop"
        :label="item.label"
        v-for="(item, index) in dateHeader"
        :key="index"
        width="130"
        align="center"
      >
        <template #default="scope">
          <div
            class="editable-row-span"
            v-for="(citem, cindex) in scope.row[item.prop]"
            :key="cindex"
            v-show="citem.flag"
          >
            <span>
              {{ citem.shiftsName }}
              <span v-show="citem.postId && citem.postId.length != 0">(</span>
              <span
                class="post_item"
                v-show="citem.postId && citem.postId.length != 0"
                v-for="(sitem, sindex) in citem.postName"
                :key="sindex"
              >
                {{ sitem }}
                <span
                  class="post_line"
                  v-show="citem.postId && sindex != citem.postId.length - 1"
                ></span>
              </span>
              <span v-show="citem.postId && citem.postId.length != 0">)</span>
            </span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <largeMenu
      style="position: absolute"
      :style="{ top: menuTop, left: menuLeft }"
      :classesList="classesList"
      :postList="postList"
      :combinationsList="combinationsList"
      :menuTabs="menuTabs"
      @selButtonItem="selButtonItem"
      @tabClick="tabClick"
      @handleTabsEdit="handleTabsEdit"
      @changeDuration="changeDuration"
      @changeRemark="changeRemark"
      v-show="showMenu"
    ></largeMenu>
    <mini-menu
      style="position: absolute"
      :style="{ top: menuTop, left: menuLeft }"
      @miniMenuFun="miniMenuFun"
      v-show="showMiniMenu"
    ></mini-menu>
    <right-menu
      style="position: absolute"
      :style="{ top: menuTop, left: menuLeft }"
      @rightMenuFun="rightMenuFun"
      :showPaste="showPaste"
      :showPasteAll="showPasteAll"
      :classesList="classesList"
      v-show="showRightMenu"
    ></right-menu>
    <!-- 设置排序 -->
    <SortAndGroup
      :visibleSG="visibleSG"
      :titleSG="titleSG"
      :nurseGroupSort="rowValueMini.nurseGroupSort"
      :groupList="groupList"
      @closeSG="closeSG"
      @confirmChange="confirmChange"
    />

    <!-- :groupId="rowValueMini.nurseGroupId" -->
  </div>
</template>

<script setup>
import { ref, toRaw } from "vue";
import largeMenu from "./largeMenu.vue";
import miniMenu from "./miniMenu.vue";
import rightMenu from "./rightMenu.vue";
import { arrWorkList } from "@/api/arr/scheduleSheet";
import { listWorkShifts } from "@/api/arr/workShifts";
import { listWorkPost } from "@/api/arr/workPost";
import { listWorkShiftsPost } from "@/api/arr/workShiftsPost";
import SortAndGroup from "./sortAndGroup.vue";
import { useRouter } from "vue-router";
const router = useRouter();

const { proxy } = getCurrentInstance();
// const { hr_staff_level, } = proxy.useDict('hr_staff_level',);

const props = defineProps({
  workData: {
    type: Array,
  },
  expData: {
    type: Array,
  },
  dates: {
    type: Array,
  },
  appTop: {
    type: Number,
  },
  tableHeight: {
    type: Number,
  },
});

const data = reactive({
  form: {},
  queryParams: {
    deptId: router.currentRoute.value.query.deptId,
    deptName: router.currentRoute.value.query.deptName,
    status: "", // 1-临时保存；2-发布；""-全部；
  },
  rules: {},
});
const { queryParams, form, rules } = toRefs(data);

/* 默认数据 */
const tableHeader = ref([
  {
    prop: "nurseGroupName",
    label: "护理分组",
    flag: true,
  },
  {
    prop: "nickName",
    label: "姓名",
    flag: true,
  },
  {
    prop: "userCode",
    label: "工号",
    flag: true,
  },
  {
    prop: "levelName",
    label: "能级",
    flag: true,
  },
  // {
  //   prop: "rest",
  //   label: "积休",
  //   flag: true,
  // },
  // {
  //   prop: "weekRest",
  //   label: "本周积休",
  //   flag: true,
  // },
  {
    prop: "allAmassRest",
    label: "累计积休",
    flag: true,
  },
  {
    prop: "weekDuration",
    label: "本周时长",
    flag: true,
  },
  {
    prop: "monthDuration",
    label: "本月时长",
    flag: true,
  },
  // {
  //   prop: "monthRest",
  //   label: "本月积休",
  //   flag: true,
  // },
]);
const tableData = ref([]);
console.log(tableData.value);
const dateHeader = ref([]);
const dateRang = ref([]);

// 监听时间范围(dates)的变化，有变化则计算遍历始末日期间的所有日期
watch(
  () => [props.dates, props.workData, props.expData],
  (newValue, oldValue) => {
    if (newValue[0] != oldValue[0]) {
      let newDates = JSON.parse(JSON.stringify(newValue[0]));
      let newList = [];

      dateRang.value = [];
      for (let index in newDates) {
        let obj = {
          prop: newDates[index],
          // prop: "recordMap",
          label: getWeekday(newDates[index]),
          flag: true,
        };
        newList.push(obj);
        if (index == 0) {
          dateRang.value.push(newDates[index]);
        } else if (index == newDates.length - 1) {
          dateRang.value.push(newDates[index]);
        }
      }
      dateHeader.value = newList;
      getList();
    }
    if (newValue[1] != oldValue[1]) {
      tableData.value = JSON.parse(JSON.stringify(newValue[1]));
    }
    if (newValue[2] && newValue[2].length != 0 && newValue[2] != oldValue[2]) {
      newValue[2].map((item) => {
        if (item.recordMap && item.recordMap.length != 0) {
          tableData.value.map((child) => {
            if (child.userId == item.userId) {
              Object.keys(item.recordMap).map((son) => {
                item.recordMap[son].map((gson) => {
                  gson.name = "0";
                  gson.flag = true;
                  if (!gson.postId) {
                    gson.postId = [];
                    gson.postName = [];
                  }
                });
                if (son in child.recordMap) {
                  child.recordMap[son].map((zson) => {
                    removeIds.value.push(zson.id);
                    child.weekDuration -= zson.duration - zson.extraDuration;
                    child.monthDuration -= zson.duration - zson.extraDuration;
                    child.allAmassRest -= zson.duration - zson.extraDuration;
                  });
                }
                child.recordMap[son] = item.recordMap[son];
                child[son] = item.recordMap[son];
                item.recordMap[son].map((sson) => {
                  console.log(sson);
                  child.weekDuration += sson.duration;
                  child.monthDuration += sson.duration;
                  child.allAmassRest += sson.duration;
                });
              });
            }
          });
        }
      });
    }
  }
);

/* 默认一周日期范围计算 start */
// 计算距离今天七天后的日期
const nextday = (e) => {
  let date2 = new Date(new Date());
  date2.setDate(new Date().getDate() + 6);
  return date2;
};

/* 计算当前日期的星期数 */
const getWeekday = (dateStr) => {
  const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
  const dateObj = new Date(dateStr);
  return dateStr + " " + weekdays[dateObj.getDay()];
};

/* 计算默认一周的日期 */
const ComputeInterval = () => {
  let list = [];
  let currentDate = new Date();
  while (currentDate <= nextday()) {
    list.push(
      `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(
        2,
        "0"
      )}-${String(currentDate.getDate()).padStart(2, "0")}`
    );
    currentDate.setDate(currentDate.getDate() + 1);
  }
  let newList = [];
  for (let index in list) {
    let obj = {
      prop: list[index],
      // prop: "recordMap",
      label: getWeekday(list[index]),
      flag: true,
    };
    newList.push(obj);
    if (index == 0) {
      dateRang.value.push(list[index]);
    } else if (index == list.length - 1) {
      dateRang.value.push(list[index]);
    }
  }
  dateHeader.value = newList;
  getList();
};

ComputeInterval();
/* 默认一周日期范围计算 end */

/* 合并单元格事件 start */
// 将获取到的数据根据需要合并的字段进行排序分组
// 函数groupBy有两个形参，data 为 表格数据 ， params 为需要合并的字段
const groupBy = (data, params) => {
  const groups = {};
  data.forEach((item) => {
    // 根据模拟数据 是通过group字段来分组，获取data中的传入的params属性对应的属性值 ，放入数组中：["2222"]，再将属性值转换为json字符串：'["2222"]'
    const group = JSON.stringify(item[params]);
    //  把group作为groups的key,初始化value,循环时找到相同的item[params]时不变
    groups[group] = groups[group] || [];
    // 将对应找到的值作为value放入数组中
    groups[group].push(item);
  });
  // 返回处理好的二维数组 （如果想返回groupBy形式的数据只返回groups即可）
  return Object.values(groups);
};

// 构造控制合并数组spanArr(),构造一个SpanArr数组赋予rowspan，即控制行合并
const spanArr = ref([]);
const getSpanArr = (data, params) => {
  // 接收重构数组
  let arr = [];
  // 设置索引
  let pos = 0;

  // 控制合并的数组
  spanArr.value = [];
  // arr 处理,就是进行深拷贝，改变存储地址，避免影响原数据(tableData)
  groupBy(data, params).map((v) => (arr = arr.concat(v)));
  arr.map((res) => {
    data.shift();
    data.push(res);
  });
  // spanArr 处理
  const redata = arr.map((v) => v[params]);
  redata.reduce((prev, cur, index, arr) => {
    if (index === 0) {
      spanArr.value.push(1);
    } else {
      if (cur === prev) {
        spanArr.value[pos] += 1;
        spanArr.value.push(0);
      } else {
        spanArr.value.push(1);
        pos = index;
      }
    }
    return cur;
  }, {});
  tableData.value.map((item, index) => {
    item["id"] = index;
  });
};

// 合并单元格
const spanMethod = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex === 0) {
    const _row = spanArr.value[rowIndex];
    const _col = _row > 0 ? 1 : 0;
    return {
      rowspan: _row,
      colspan: _col,
    };
  }
};
/* 合并单元格事件 end */

/* 点击单元格相关事件 start*/
const currentValue = ref("");
const selRowIndex = ref("");
const selColIndex = ref("");
// 单元格点击事件
const cellClick = (row, column, cell, columnIndex) => {
  selRowIndex.value = row.id;
  selColIndex.value = column.rawColumnKey;
  currentValue.value = `${row.id}_${column.property}`;
  showMenu.value = false;
  showMiniMenu.value = false;
  showRightMenu.value = false;
};

//单元格的 style 的回调方法
const cellStyle = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex < 7) {
    return { backgroundColor: "#f8f8f9" };
  } else if (`${row.id}_${column.property}` === currentValue.value) {
    return { border: "1px solid #597EF7" };
  }
};

/* 点击单元格相关事件 end*/

/* 点击单元格事件 start */
// 双击事件
const showMiniMenu = ref(false);
const showMenu = ref(false);
const menuTop = ref("0px");
const menuLeft = ref("0px");
const menuTabs = ref([]);
const rowValueMini = ref({ nurseGroupSort: 0 }); // 双击人员信息时，临时保存当前人员数据，用于进行排序、分组等操作
const cellContextmenu = (row, column, cell, event) => {
  event.preventDefault();
  showMenu.value = false;
  showMiniMenu.value = false;
  selRowIndex.value = row.id;
  selColIndex.value = column.rawColumnKey;
  currentValue.value = `${row.id}_${column.property}`;
  if (!tableHeader.value.some((item) => item["label"] == column.label)) {
    let overHeight = event.clientY + 500 - window.innerHeight;
    let overWidth = event.clientX + 520 - window.innerWidth;
    menuTop.value =
      event.clientY - props.appTop - (overHeight > 0 ? overHeight : 0) + "px";
    menuLeft.value = event.clientX - (overWidth > 0 ? 520 : -10) + "px";
    if (
      !tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop] ||
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop]
        .length == 0
    ) {
      let data = {
        endTime: null,
        extraDuration: null,
        id: null,
        postColor: null,
        postId: [],
        postName: [],
        remark: null,
        shiftsColor: null,
        shiftsDate: dateHeader.value[selColIndex.value].prop,
        shiftsId: null,
        shiftsName: null,
        startTime: null,
        userId: tableData.value[selRowIndex.value].userId,
        flag: true,
        name: "0",
      };
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop] = [];
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop].push(
        data
      );
      menuTabs.value =
        tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop];
      tableData.value[selRowIndex.value].recordMap[
        dateHeader.value[selColIndex.value].prop
      ] = JSON.parse(
        JSON.stringify(
          tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop]
        )
      );
    }
    menuIndex.value =
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop]
        .length - 1;
    menuTabs.value = tableData.value[selRowIndex.value][
      dateHeader.value[selColIndex.value].prop
    ]
      ? JSON.parse(
          JSON.stringify(
            tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop]
          )
        )
      : [];
    showMenu.value = true;
  } else if (
    tableHeader.value.some(
      (item) => item["label"] == column.label && item["label"] != "护理分组"
    )
  ) {
    let overHeight = event.clientY + 190 - window.innerHeight;
    menuTop.value =
      event.clientY - props.appTop - (overHeight > 0 ? overHeight : 0) + "px";
    menuLeft.value = event.clientX + 10 + "px";
    showMiniMenu.value = true;
    rowValueMini.value = JSON.parse(JSON.stringify(row));
  }
};

// 右击单元格事件
const showRightMenu = ref(false);
const rightClick = (row, column, cell, event) => {
  event.preventDefault();
  showMenu.value = false;
  showMiniMenu.value = false;
  selRowIndex.value = row.id;
  selColIndex.value = column.rawColumnKey;
  currentValue.value = `${row.id}_${column.property}`;
  if (!tableHeader.value.some((item) => item["label"] == column.label)) {
    let overHeight = event.clientY + 500 - window.innerHeight;
    let overWidth = event.clientX + 120 - window.innerWidth;
    menuTop.value =
      event.clientY - props.appTop - (overHeight > 0 ? overHeight : 0) + "px";
    menuLeft.value = event.clientX - (overWidth > 0 ? 120 : -10) + "px";
    showRightMenu.value = true;
  }
};

// 保存复制的值
const copyValue = ref();
const copyAllValue = ref();
const showPaste = ref(false);
const showPasteAll = ref(false);
// 选择右击相关按钮
const rightMenuFun = (item) => {
  if (item.key == "copy") {
    if (
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop] &&
      (tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][0]
        .postId.length != 0 ||
        tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][0]
          .shiftsId)
    ) {
      copyValue.value = JSON.parse(
        JSON.stringify(
          tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop]
        )
      );
      showPaste.value = true;
    } else {
    }
  } else if (item.key == "copyAll") {
    if (tableData.value[selRowIndex.value].recordMap) {
      copyAllValue.value = JSON.parse(
        JSON.stringify(tableData.value[selRowIndex.value].recordMap)
      );
      showPasteAll.value = true;
    } else {
      console.log("复制无内容");
    }
  } else if (item.key == "paste") {
    tableData.value[selRowIndex.value][
      dateHeader.value[selColIndex.value].prop
    ] = JSON.parse(JSON.stringify(copyValue.value));
    tableData.value[selRowIndex.value].recordMap[
      dateHeader.value[selColIndex.value].prop
    ] = JSON.parse(
      JSON.stringify(
        tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop]
      )
    );
  } else if (item.key == "pasteAll") {
    Object.keys(copyAllValue.value).map((item, index) => {
      copyAllValue.value[item][0].userId = tableData.value[selRowIndex.value].userId;
      tableData.value[selRowIndex.value][item] = JSON.parse(
        JSON.stringify(copyAllValue.value[item])
      );
    });
    tableData.value[selRowIndex.value].recordMap = JSON.parse(
      JSON.stringify(copyAllValue.value)
    );
  } else if (item.key == "rest" || item.key == "holiday") {
    if (
      tableData.value[selRowIndex.value].recordMap[
        dateHeader.value[selColIndex.value].prop
      ] &&
      tableData.value[selRowIndex.value].recordMap[
        dateHeader.value[selColIndex.value].prop
      ].length != 0
    ) {
      tableData.value[selRowIndex.value].recordMap[
        dateHeader.value[selColIndex.value].prop
      ].map((item) => {
        if (item.id) {
          removeIds.value.push(item.id);
        }
      });
    }
    let data = {
      endTime: item.endTime,
      duration: item.duration,
      extraDuration: null,
      id: null,
      postColor: null,
      postId: [],
      postName: [],
      remark: item.remark,
      shiftsColor: item.color,
      shiftsDate: dateHeader.value[selColIndex.value].prop,
      shiftsId: item.shiftsId,
      shiftsName: item.shiftsName,
      startTime: item.startTime,
      userId: tableData.value[selRowIndex.value].userId,
      flag: true,
      name: "0",
    };
    tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop] = [];
    tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop].push(
      data
    );
    tableData.value[selRowIndex.value].recordMap[
      dateHeader.value[selColIndex.value].prop
    ] = JSON.parse(
      JSON.stringify(
        tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop]
      )
    );
  }
  showRightMenu.value = false;
};

// 双击姓名等基础信息事件
const visibleSG = ref(false); // 控制人员排序和分组弹窗展开/收起的标识，
const titleSG = ref("");
const groupList = ref([]);
function miniMenuFun(key) {
  showMiniMenu.value = false;
  if (key == "sort") {
    visibleSG.value = true;
    titleSG.value = "sort";
  } else if (key == "group") {
    visibleSG.value = true;
    titleSG.value = "group";
    let resultObj = {};
    tableData.value.forEach((item) => {
      if (!resultObj[item.nurseGroupId]) {
        resultObj[item.nurseGroupId] = item;
      }
    });
    groupList.value = Object.values(resultObj);
  } else if (key == "positive") {
    // 正序轮岗
    let length = tableData.value.filter(
      (item) => item.nurseGroupId == rowValueMini.value.nurseGroupId
    ).length;
    let temObj = JSON.parse(JSON.stringify(tableData.value[length - 1]));
    if (length > 1) {
      for (let i in tableData.value) {
        if (tableData.value[i].nurseGroupId == rowValueMini.value.nurseGroupId) {
          // 1. 确认选中的信息的原始位置和要修改的位置
          // 2. 把选中的信息的后一位到要修改位置的所有数据向前提一位（可通过自定义字段去赋值）
          // 3. 再把选中信息赋值到要修改位置即可;
          if (i < length - 1) {
            tableData.value[+i + 1].nickName = tableData.value[i].nickName;
            tableData.value[+i + 1].userCode = tableData.value[i].userCode;
            tableData.value[+i + 1].userId = tableData.value[i].userId;
            if (tableData.value[+i + 1].recordMap) {
              Object.keys(tableData.value[+i + 1].recordMap).map((item) => {
                tableData.value[+i + 1].recordMap[item].map((child) => {
                  child.userId = tableData.value[+i + 1].userId;
                });
              });
            }
            Object.keys(tableData.value[+i + 1]).map((item) => {
              dateHeader.value.map((child) => {
                if (item == child.prop) {
                  tableData.value[+i + 1][item].map((son) => {
                    son.userId = tableData.value[+i + 1].userId;
                  });
                }
              });
            });
          }
        }
      }
      tableData.value[0].nickName = temObj.nickName;
      tableData.value[0].userCode = temObj.userCode;
      tableData.value[0].userId = temObj.userId;
      if (tableData.value[0].recordMap) {
        Object.keys(tableData.value[0].recordMap).map((item) => {
          tableData.value[0].recordMap[item].map((child) => {
            child.userId = temObj.userId;
          });
        });
      }
      Object.keys(tableData.value[0]).map((item) => {
        dateHeader.value.map((child) => {
          if (item == child.prop) {
            tableData.value[0][item].map((son) => {
              son.userId = temObj.userId;
            });
          }
        });
      });
    }
  } else if (key == "negative") {
    // 倒序轮岗
    let length = tableData.value.filter(
      (item) => item.nurseGroupId == rowValueMini.value.nurseGroupId
    ).length;
    let temObj = JSON.parse(JSON.stringify(tableData.value[0]));
    if (length > 1) {
      for (let i in tableData.value) {
        if (tableData.value[i].nurseGroupId == rowValueMini.value.nurseGroupId) {
          // 1. 确认选中的信息的原始位置和要修改的位置
          // 2. 把选中的信息的后一位到要修改位置的所有数据向前提一位（可通过自定义字段去赋值）
          // 3. 再把选中信息赋值到要修改位置即可;
          if (i > 0) {
            tableData.value[+i - 1].nickName = tableData.value[i].nickName;
            tableData.value[+i - 1].userCode = tableData.value[i].userCode;
            tableData.value[+i - 1].userId = tableData.value[i].userId;
            if (tableData.value[+i - 1].recordMap) {
              Object.keys(tableData.value[+i - 1].recordMap).map((item) => {
                tableData.value[+i - 1].recordMap[item].map((child) => {
                  child.userId = tableData.value[+i - 1].userId;
                });
              });
            }
            Object.keys(tableData.value[+i - 1]).map((item) => {
              dateHeader.value.map((child) => {
                if (item == child.prop) {
                  tableData.value[+i - 1][item].map((son) => {
                    son.userId = tableData.value[+i - 1].userId;
                  });
                }
              });
            });
          }
        }
      }
      tableData.value[length - 1].nickName = temObj.nickName;
      tableData.value[length - 1].userCode = temObj.userCode;
      tableData.value[length - 1].userId = temObj.userId;
      if (tableData.value[length - 1].recordMap) {
        Object.keys(tableData.value[length - 1].recordMap).map((item) => {
          tableData.value[length - 1].recordMap[item].map((child) => {
            child.userId = temObj.userId;
          });
        });
      }
      Object.keys(tableData.value[length - 1]).map((item) => {
        dateHeader.value.map((child) => {
          if (item == child.prop) {
            tableData.value[length - 1][item].map((son) => {
              son.userId = temObj.userId;
            });
          }
        });
      });
    }
  }
}
// 关闭人员排序和分组弹窗
function closeSG() {
  visibleSG.value = false;
}

// 确认修改
function confirmChange(type, value, groupId, groupName) {
  if (type == "sort") {
    for (let i in tableData.value) {
      if (tableData.value[i].nurseGroupId == rowValueMini.value.nurseGroupId) {
        // 1. 确认选中的信息的原始位置和要修改的位置
        // 2. 把选中的信息的后一位到要修改位置的所有数据向前提一位（可通过自定义字段去赋值）
        // 3. 再把选中信息赋值到要修改位置即可;
        if (i > rowValueMini.value.nurseGroupSort - 1 && i <= value - 1) {
          tableData.value[i - 1].nickName = tableData.value[i].nickName;
          tableData.value[i - 1].userCode = tableData.value[i].userCode;
          tableData.value[i - 1].userId = tableData.value[i].userId;
          if (tableData.value[i - 1].recordMap) {
            Object.keys(tableData.value[i - 1].recordMap).map((item) => {
              tableData.value[i - 1].recordMap[item].map((child) => {
                child.userId = tableData.value[i - 1].userId;
              });
            });
          }
          Object.keys(tableData.value[i - 1]).map((item) => {
            dateHeader.value.map((child) => {
              if (item == child.prop) {
                tableData.value[i - 1][item].map((son) => {
                  son.userId = tableData.value[i - 1].userId;
                });
              }
            });
          });
        }
      }
    }
    tableData.value[value - 1].nickName = rowValueMini.value.nickName;
    tableData.value[value - 1].userCode = rowValueMini.value.userCode;
    tableData.value[value - 1].userId = rowValueMini.value.userId;
    if (tableData.value[value - 1].recordMap) {
      Object.keys(tableData.value[value - 1].recordMap).map((item) => {
        tableData.value[value - 1].recordMap[item].map((child) => {
          child.userId = rowValueMini.value.userId;
        });
      });
    }
    Object.keys(tableData.value[value - 1]).map((item) => {
      dateHeader.value.map((child) => {
        if (item == child.prop) {
          tableData.value[value - 1][item].map((son) => {
            son.userId = rowValueMini.value.userId;
          });
        }
      });
    });
  } else if (type == "group") {
    let length = tableData.value.filter((item) => item.nurseGroupId == groupId).length;
    tableData.value.forEach((item) => {
      if (item.userId == rowValueMini.value.userId) {
        item.nurseGroupId = groupId;
        item.nurseGroupName = groupName;
        item.nurseGroupSort = length + 1;
      }
    });
    getSpanArr(tableData.value, "nurseGroupId");
  }
}

/* 点击单元格事件 end */

/* 子组件想父组件暴露事件或传值 start */
const menuIndex = ref(0);
const tabClick = (index) => {
  menuIndex.value = index;
};

// 修改tab_item
const removeIds = ref([]);
const handleTabsEdit = (action, targetName) => {
  if (action == "add") {
    const newTabName = JSON.stringify(
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop].length
    );
    let data = {
      endTime: null,
      extraDuration: null,
      duration: null,
      id: null,
      postColor: null,
      postId: [],
      postName: [],
      remark: null,
      shiftsColor: null,
      shiftsDate: dateHeader.value[selColIndex.value].prop,
      shiftsId: null,
      shiftsName: null,
      startTime: null,
      userId: tableData.value[selRowIndex.value].userId,
      flag: true,
      name: newTabName,
    };
    tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop].push(
      data
    );
    menuTabs.value =
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop];
    menuIndex.value =
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop]
        .length - 1;
    tableData.value[selRowIndex.value].recordMap[
      dateHeader.value[selColIndex.value].prop
    ] = JSON.parse(
      JSON.stringify(
        tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop]
      )
    );
  } else if (action == "remove") {
    if (menuTabs.value.length == 1) {
      return proxy.$message.warning("当前标签不可删除！");
    }
    if (
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][
        targetName
      ].id
    ) {
      removeIds.value.push(
        tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][
          targetName
        ].id
      );
    }
    tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop].splice(
      targetName,
      1
    );
    for (let index in tableData.value[selRowIndex.value][
      dateHeader.value[selColIndex.value].prop
    ]) {
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][
        index
      ].name = index;
    }
    menuTabs.value =
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop];
    menuIndex.value = menuTabs.value.length - 1;
    tableData.value[selRowIndex.value].recordMap[
      dateHeader.value[selColIndex.value].prop
    ] = JSON.parse(
      JSON.stringify(
        tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop]
      )
    );
  }
};

// 双击后的菜单选择班次岗位事件
const selButtonItem = (
  boolean,
  shifts_name,
  shifts_id,
  post_name,
  post_id,
  duration,
  type,
  startTime,
  endTime
) => {
  if (type == 1) {
    if (
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop].some(
        (item) => item.shiftsId == shifts_id
      )
    ) {
      return proxy.$message.warning("当前班次已选择！");
    }
    if (Object.keys(tableData.value[selRowIndex.value].recordMap).length > 0) {
      for (let item of tableData.value[selRowIndex.value][
        dateHeader.value[selColIndex.value].prop
      ]) {
        const freg = `'${item.startTime}'<'${startTime}' && '${item.endTime}' > '${startTime}'`;
        const sreg = `'${item.startTime}'<'${endTime}' && '${item.endTime}' > '${endTime}'`;
        const first = eval(freg);
        const second = eval(sreg);
        if (first || second) {
          return proxy.$message.warning(
            startTime + "-" + endTime + "时间段已经有班次了！"
          );
        }
      }
    }
    if (duration >= 0) {
      if(tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][menuIndex.value].duration){
        tableData.value[selRowIndex.value].weekDuration -= tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][menuIndex.value].duration
        tableData.value[selRowIndex.value].monthDuration -= tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][menuIndex.value].duration
        tableData.value[selRowIndex.value].allAmassRest -= tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][menuIndex.value].duration
      }
      tableData.value[selRowIndex.value].weekDuration += duration;
      tableData.value[selRowIndex.value].monthDuration += duration;
      tableData.value[selRowIndex.value].allAmassRest += duration;
    }
    tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][
      menuIndex.value
    ].shiftsId = shifts_id;
    tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][
      menuIndex.value
    ].shiftsName = shifts_name;
    tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][
      menuIndex.value
    ].duration = duration;
    tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][
      menuIndex.value
    ].startTime = startTime;
    tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][
      menuIndex.value
    ].endTime = endTime;
    tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][
      menuIndex.value
    ].shiftsDate = dateHeader.value[selColIndex.value].prop;
    menuTabs.value =
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop];
    tableData.value[selRowIndex.value].recordMap[
      dateHeader.value[selColIndex.value].prop
    ] = JSON.parse(
      JSON.stringify(
        tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop]
      )
    );
    // if(){

    // }
  } else if (type == 2) {
    if (
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][
        menuIndex.value
      ].postId.some((item) => item == post_id)
    ) {
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][
        menuIndex.value
      ].postName = tableData.value[selRowIndex.value][
        dateHeader.value[selColIndex.value].prop
      ][menuIndex.value].postName.reduce((pre, cur) => {
        if (cur != post_name) {
          pre.push(cur);
        }
        return pre;
      }, []);
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][
        menuIndex.value
      ].postId = tableData.value[selRowIndex.value][
        dateHeader.value[selColIndex.value].prop
      ][menuIndex.value].postId.reduce((prev, curr) => {
        if (curr != post_id) {
          prev.push(curr);
        }
        return prev;
      }, []);
    } else {
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][
        menuIndex.value
      ].postName.push(post_name);
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][
        menuIndex.value
      ].postId.push(post_id);
    }
    menuTabs.value =
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop];
    tableData.value[selRowIndex.value].recordMap[
      dateHeader.value[selColIndex.value].prop
    ] = JSON.parse(
      JSON.stringify(
        tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop]
      )
    );
  } else if (type == 3) {
    if (
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop].some(
        (item) => item.shiftsId == shifts_id
      )
    ) {
      return proxy.$message.warning("当前班次已选择！");
    }
    tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][
      menuIndex.value
    ].shiftsId = shifts_id;
    tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][
      menuIndex.value
    ].shiftsName = shifts_name;
    tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][
      menuIndex.value
    ].shiftsDate = dateHeader.value[selColIndex.value].prop;
    menuTabs.value =
      tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop];
    tableData.value[selRowIndex.value].recordMap[
      dateHeader.value[selColIndex.value].prop
    ] = JSON.parse(
      JSON.stringify(
        tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop]
      )
    );
  }
  console.log(tableData.value[selRowIndex.value]);
};

// 修改额外时长
const changeDuration = (number) => {
  tableData.value[selRowIndex.value].allAmassRest = tableData.value[selRowIndex.value].allAmassRest -tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][menuIndex.value].extraDuration+number
  tableData.value[selRowIndex.value].weekDuration = tableData.value[selRowIndex.value].weekDuration -tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][menuIndex.value].extraDuration+number
  tableData.value[selRowIndex.value].monthDuration= tableData.value[selRowIndex.value].monthDuration-tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][menuIndex.value].extraDuration+number
  tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][menuIndex.value].extraDuration = number;
};
// 修改备注
const changeRemark = (text) => {
  tableData.value[selRowIndex.value][dateHeader.value[selColIndex.value].prop][
    menuIndex.value
  ].remark = text;
};
/* 子组件想父组件暴露事件或传值 end */

/* 页面相关内容接口 start */

/** 查询排班申请休假列表 */
function getList() {
  // loading.value = true;
  arrWorkList(proxy.addDateRange(queryParams.value, dateRang.value)).then((response) => {
    console.log("排班列表", response);
    response.data.map((item, index) => {
      item["id"] = index;
      item.allAmassRestTem = JSON.parse(JSON.stringify(item.allAmassRest))
      item.weekDurationTem = JSON.parse(JSON.stringify(item.weekDuration))
      item.monthDurationTem = JSON.parse(JSON.stringify(item.monthDuration))
      if (item.recordMap) {
        Object.keys(item.recordMap).map((child) => {
          item.recordMap[child].forEach((son, sindex) => {
            son.flag = true;
            son.name = JSON.stringify(sindex);
            if (typeof son.postId == "string") {
              son.postId = son.postId.split(",");
            }
            if (typeof son.postName == "string") {
              son.postName = son.postName.split(",");
            }
          });
          item[child] = item.recordMap[child];
        });
      } else {
        item.recordMap = {};
      }
    });
    tableData.value = JSON.parse(JSON.stringify(response.data));
    getSpanArr(tableData.value, "nurseGroupId");
  });
}
const tableParams = {
  pageNum: 1,
  pageSize: 100,
};
/** 查询班次管理列表 */
const classesList = ref([]);
function getClasses() {
  listWorkShifts(tableParams.value).then((response) => {
    console.log("班次列表", response);
    if (response.code == 200) {
      classesList.value = response.rows;
    }
  });
}

/** 查询岗位列表 */
const postList = ref([]);
function getPostList() {
  listWorkPost(tableParams.value).then((response) => {
    // console.log("岗位：", response);
    if (response.code == 200) {
      postList.value = response.rows;
    }
  });
}

/** 查询排班班次岗位组合列表 */
const combinationsList = ref([]);
function getCombinations() {
  listWorkShiftsPost(tableParams.value).then((response) => {
    combinationsList.value = response.rows;
  });
}

getClasses();
getPostList();
// getCombinations();
/* 页面相关内容接口 end */

defineExpose({
  tableData,
  removeIds,
  selRowIndex,
  dateHeader,
  selColIndex,
});
</script>

<style lang="scss">
.table_box {
  // .rowStyle{
  //   height: 50px;
  //   line-height: 50px;
  // }
  th {
    padding: 0 !important;
    height: 50px;
    line-height: 50px;
  }
  td {
    padding: 0 !important;
    height: 50px;
    line-height: 50px;
    overflow: hidden;
  }
  // .cell {
  //   padding: 0 !important;
  //   height: 50px;
  //   // line-height: 50px;
  //   text-align: center;
  // }
  .post_item {
    // width: auto;
    // white-space: nowrap;
    // overflow: hidden;
    // text-overflow: ellipsis;
    .post_line {
      border-right: 1px solid #000;
    }
  }
}
.table_box:hover {
  cursor: default;
}
</style>
