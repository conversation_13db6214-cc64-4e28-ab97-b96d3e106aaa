<template>
    <div class="app-container">
        <!-- 左侧菜单 -->
        <div class="left-sidebar">
            <!-- 知识库导航菜单 -->
            <div class="sidebar-menu">
                <div class="menu-item active">
                    <div class="menu-header">
                        <v-icon size="small" color="#1A53E1" class="mr-2">mdi-folder</v-icon>
                        <span class="orange-text">精神文明建设服务中心</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧内容 -->
        <div class="right-content">
            <div class="content-header">
                <div class="header-title">{{ activeSubItem }}</div>
            </div>

            <div class="search-bar">
                <el-input v-model="searchQuery" placeholder="请输入姓名、账号..." class="search-input" clearable
                    prefix-icon="el-icon-search" @keyup.enter="handleSearch" />
                <!-- <el-select v-model="selectedStatus" placeholder="账号状态" class="filter-select" @change="handleSearch">
                    <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select> -->
                <div class="search-buttons">
                    <el-button type="primary" @click="handleSearch">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </div>
                <div class="button-group">
                    <el-button class="invite-button" @click="showAddUserDialog">添加人员</el-button>
                    <el-button type="warning" class="delete-button" @click="batchDeleteUsers">删除成员</el-button>
                </div>
            </div>

            <div class="content-body">
                <el-table :data="userList" style="width: 100%" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55">
                    </el-table-column>
                    <el-table-column prop="name" label="姓名" min-width="120">
                        <template #default="scope">
                            <div class="user-info">
                                <el-avatar :size="32" :src="scope.row.avatar" class="user-avatar"
                                    :style="{ backgroundColor: getAvatarColor(scope.row.userId) }">
                                    {{ scope.row.nickName.substring(0, 1) }}
                                </el-avatar>
                                <span>{{ scope.row.nickName }}</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="账号状态" min-width="120">
                        <template #default="scope">
                            <span class="status-text">{{ scope.row.status == '0' ? '正常' : '停用' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="phonenumber" label="手机号码" min-width="120">
                        <template #default="scope">
                            <span>{{ scope.row.phonenumber || '暂无手机号' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="department" label="加入的知识库及角色" min-width="150">
                        <template #default="scope">
                            <div v-if="scope.row.knowledgeBaseNames && scope.row.operationPermissions">
                                <div v-for="(kb, index) in scope.row.knowledgeBaseNames.split(',')" :key="index" class="role-text">
                                    {{ kb }}{{ getPermissionLabel(scope.row.operationPermissions.split(',')[index]) }}
                                </div>
                            </div>
                            <span v-else class="role-text">暂无加入的知识库</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="roleName" label="角色" min-width="100">
                        <template #default="scope">
                            <span class="role-text">{{ scope.row.roleName || '暂无角色' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="createTime" label="注册时间" min-width="120">
                        <template #default="scope">
                            <span class="role-text">{{ scope.row.createTime }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" min-width="120" align="center">
                        <template #default="scope">
                            <el-button type="text" size="small" @click="editUser(scope.row)">编辑</el-button>
                            <el-button type="text" size="small" class="text-danger"
                                @click="deleteUser(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <div class="pagination-container">
                    <div class="page-size">
                        <div style="width: 110px;">每页显示</div>
                        <el-select v-model="pageSize" size="small" @change="handleSizeChange">
                            <el-option v-for="size in [10, 20, 50, 100]" :key="size" :label="size"
                                :value="size"></el-option>
                        </el-select>
                        条
                    </div>
                    <el-pagination @current-change="handleCurrentChange" :current-page="currentPage"
                        :page-size="pageSize" layout="prev, pager, next, jumper" :total="total" background>
                    </el-pagination>
                </div>
            </div>
        </div>

        <!-- 添加/编辑人员对话框 -->
        <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" custom-class="user-dialog"
            :show-close="true" :modal-append-to-body="true" :append-to-body="true" :close-on-click-modal="true" center
            destroy-on-close>
            <!-- <div class="dialog-header">
                <img src="@/assets/images/lines.png" style="width: 100%;" alt="" srcset="">
            </div> -->
            <div class="user-form-container">
                <el-form :model="userForm" ref="userFormRef" :rules="userFormRules" label-width="80px"
                    label-position="left">
                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item label="用户昵称" required prop="account">
                                <el-input v-model="userForm.account" placeholder="请输入用户昵称"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item label="手机号码" :required="!userForm.id" prop="phone">
                                <el-input v-model="userForm.phone" placeholder="请输入手机号码"
                                    :disabled="!!userForm.id"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item label="状态">
                                <div class="radio-group">
                                    <el-radio v-model="userForm.status" type="primary" label="正常" :disabled="isEditingSelf">正常</el-radio>
                                    <el-radio v-model="userForm.status" type="danger" label="停用" :disabled="isEditingSelf">停用</el-radio>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item label="角色" prop="role">
                                <el-select v-model="userForm.role" placeholder="请选择角色" class="full-width" :disabled="isEditingSelf">
                                    <el-option label="普通角色" value="common"></el-option>
                                    <el-option label="单位管理员" value="kb-admin"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-form-item label="备注" prop="remark">
                        <el-input v-model="userForm.remark" type="textarea" placeholder="请输入内容" :rows="4">
                        </el-input>
                    </el-form-item>
                </el-form>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button plain @click="dialogVisible = false">取消</el-button>
                    <el-button type="warning" @click="submitUserForm" class="confirm-btn">完成</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { userAdd, deptTree, userListApi, userUpdate, UserDelete } from '@/api/user';
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
// 引入 Vuetify 组件
import { VIcon } from 'vuetify/components';
// 引入 Element Plus 图标
import { ArrowRight, ArrowDown } from '@element-plus/icons-vue';
// 导入用户store
import useUserStore from '@/store/modules/user';

// 获取当前登录用户信息
const userStore = useUserStore();

// 页面标题
const activeSubItem = "人员管理";

// 用户列表相关
const searchQuery = ref('');
const selectedStatus = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const userList = ref([]);
const multipleSelection = ref([]);

// 添加是否编辑自己的标志
const isEditingSelf = ref(false);

// 部门选项
const departments = [
    { value: '', label: '所有部门' },
    { value: '文明办', label: '文明办' },
    { value: '综合科', label: '综合科' },
    { value: '创建科', label: '创建科' }
];

// 账号状态选项
const statusOptions = [
    { value: '', label: '全部状态' },
    { value: '0', label: '正常' },
    { value: '1', label: '停用' }
];

// 用户表单
const dialogVisible = ref(false);
const dialogTitle = ref('添加人员');
const userFormRef = ref(null);
const userForm = reactive({
    id: '',
    account: '',
    phone: '',
    status: '正常',
    role: 'common', // 默认为普通角色
    remark: '',
    deptId: 100
});

// 表单验证规则
const userFormRules = {
    account: [
        { required: true, message: '请输入用户账号', trigger: 'blur' }
    ],
    phone: [
        {
            required: true, message: '请输入手机号码', trigger: 'blur', validator: (rule, value, callback) => {
                if (userForm.id) {
                    // 编辑模式下不验证必填
                    callback();
                } else if (!value) {
                    // 新增模式下验证必填
                    callback(new Error('请输入手机号码'));
                } else {
                    callback();
                }
            }
        },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码格式', trigger: 'blur' }
    ],
    role: [
        { required: true, message: '请选择用户角色', trigger: 'change' }
    ]
};

// 部门数据
const deptOptions = ref([
    {
        id: 100,
        label: "精神文明服务中心",
        disabled: false,
        treeEntity: null
    }
]);

// 表格选择事件
const handleSelectionChange = (val) => {
    multipleSelection.value = val;
};

// 分页处理
const handleSizeChange = (size) => {
    pageSize.value = size;
    fetchUserList();
};

const handleCurrentChange = (page) => {
    currentPage.value = page;
    fetchUserList();
};

// 显示添加用户对话框
const showAddUserDialog = () => {
    dialogTitle.value = '添加人员';
    resetUserForm();
    userForm.deptId = 100; // 默认设置部门ID为100
    isEditingSelf.value = false; // 重置编辑自己的标志
    dialogVisible.value = true;
};

// 编辑用户
const editUser = (row) => {
    dialogTitle.value = '编辑人员';
    // 打印row对象，查看是否包含id属性
    console.log('编辑的用户数据:', row);

    // 获取角色信息
    let role = 'common'; // 默认为普通角色
    
    // 简单判断：是单位管理员就是kb-admin，不是就是common
    role = (row.roleName == '单位管理员') ? 'kb-admin' : 'common';

    // 判断是否在编辑当前登录用户自己
    isEditingSelf.value = row.userId == userStore.id;
    console.log('是否编辑自己:', isEditingSelf.value, 'userId:', row.userId, 'currentUserId:', userStore.id);

    // 复制用户数据到表单
    Object.assign(userForm, {
        id: row.userId,
        account: row.nickName || row.userName,
        phone: row.phonenumber,
        status: row.status === '0' ? '正常' : '停用',
        role: role,
        remark: row.remark
    });
    dialogVisible.value = true;
};

// 删除用户
const deleteUser = (row) => {
    ElMessageBox.confirm(`确定删除人员 ${row.nickName} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        try {
            const res = await UserDelete(row.userId.toString());
            if (res.code == 200) {
                ElMessage.success('删除成功');
                fetchUserList();
            } else {
                ElMessage.error(res.msg || '删除失败');
            }
        } catch (error) {
            console.error('删除失败:', error);
            ElMessage.error('删除失败，请稍后重试');
        }
    }).catch(() => { });
};

// 批量删除用户
const batchDeleteUsers = () => {
    if (multipleSelection.value.length == 0) {
        ElMessage.warning('请先选择要删除的用户');
        return;
    }

    const userNames = multipleSelection.value.map(item => item.nickName).join('、');
    const userIds = multipleSelection.value.map(item => item.userId).join(',');

    ElMessageBox.confirm(`确定删除以下人员: ${userNames}？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        try {
            const res = await UserDelete(userIds);
            if (res.code == 0) {
                ElMessage.success(`已删除 ${multipleSelection.value.length} 名人员`);
                fetchUserList();
            } else {
                ElMessage.error(res.msg || '批量删除失败');
            }
        } catch (error) {
            console.error('批量删除失败:', error);
            ElMessage.error('批量删除失败，请稍后重试');
        }
    }).catch(() => { });
};

// 搜索按钮点击事件
const handleSearch = () => {
    currentPage.value = 1; // 重置为第一页
    fetchUserList();
};

// 重置搜索条件
const resetSearch = () => {
    searchQuery.value = '';
    selectedStatus.value = '';
    currentPage.value = 1;
    fetchUserList();
};

// 获取用户列表
const fetchUserList = async () => {
    userList.value = [];
    const params = {
        nickName: searchQuery.value || null,
        pageNum: currentPage.value,
        pageSize: pageSize.value,
        phonenumber: null,
        userName: null,
        status: selectedStatus.value || null,
        deptId: 100 // 默认传100
    };

    const res = await userListApi(params);
    // console.log('awdawdaw', res);

    if (res.code == 0) {
        ElMessage.success('获取用户列表成功');
        userList.value = res.data.list || [];
        total.value = res.data.total || 0;
    }

    if (res.code == -1) {
        userList.value = [];
        total.value = 0;
        ElMessage.success('暂无更多数据');
    }
};

// 获取头像颜色
const getAvatarColor = (id) => {
    const colors = ['#9c27b0', '#2196f3', '#ff9800', '#4caf50', '#e91e63', '#00bcd4', '#ff5722', '#3f51b5'];
    const index = parseInt(id) % colors.length;
    return colors[index];
};

// 根据权限代码获取权限显示名称
const getPermissionLabel = (permission) => {
    if (!permission) return '';
    
    const permissionMap = {
        'EDIT': '(管理员)',
        'READ': '(普通用户)',
        'WRITE': '(编辑者)'
    };
    
    return permissionMap[permission] || '';
};

// 处理头像上传
const handleAvatarChange = (file) => {
    // 这里可以添加文件类型和大小的验证
    if (file.raw.type.indexOf('image/') !== 0) {
        ElMessage.error('请上传图片文件!');
        return;
    }

    // 创建一个临时的URL来预览头像
    userForm.avatar = URL.createObjectURL(file.raw);

    // 实际项目中，这里应该调用上传API
    ElMessage.success('头像上传成功!');
};

// 处理部门变化
const handleDeptChange = () => {
    // 这里可以添加部门变化后的逻辑
};

// 提交用户表单
const submitUserForm = () => {
    userFormRef.value.validate(async (valid) => {
        if (valid) {
            try {
                // 打印userForm.id的值，诊断问题
                console.log('当前userForm.id的值:', userForm.id);
                console.log('当前用户角色:', userForm.role);

                // 构造符合接口要求的参数
                const params = {
                    deptId: 100, // 默认设置部门ID为100
                    nickName: userForm.account,
                    phonenumber: userForm.phone,
                    remark: userForm.remark,
                    sex: '2', // 默认未知
                    status: userForm.status == '正常' ? '0' : '1',
                    operationPermission: userForm.role === 'kb-admin' ? 'EDIT' : 'READ', // 根据角色设置权限
                    roleKey: userForm.role // 添加roleKey字段
                };

                // 如果有ID则是编辑用户
                if (userForm.id) {
                    params.userId = userForm.id;
                    const res = await userUpdate(params);
                    if (res.code == 0) {
                        ElMessage.success('更新成功');

                        // 如果是编辑自己的信息，则更新store中的用户信息
                        if (isEditingSelf.value) {
                            // 更新store中的用户信息
                            userStore.name = userForm.account; // 更新用户名
                            // 如果有需要，可以在这里更新其他信息
                            
                            // 如果需要完整更新用户信息，可以重新调用getInfo方法
                            try {
                                await userStore.getInfo();
                                console.log('已更新当前用户信息');
                                
                                // 发布自定义事件，通知其他组件（如TheHeader）更新用户信息
                                const userInfoUpdatedEvent = new CustomEvent('userInfoUpdated', {
                                    detail: { userId: userStore.id }
                                });
                                window.dispatchEvent(userInfoUpdatedEvent);
                                
                            } catch (error) {
                                console.error('更新用户信息失败:', error);
                            }
                        }

                        dialogVisible.value = false;
                        fetchUserList();
                    } else {
                        ElMessage.error(res.msg || '更新失败');
                    }
                } else {
                    // 添加用户
                    const res = await userAdd(params);
                    if (res.code == 0) {
                        ElMessage.success('添加成功');
                        dialogVisible.value = false;
                        fetchUserList();
                    } else {
                        ElMessage.error(res.msg || '添加失败');
                    }
                }
            } catch (error) {
                console.error('操作失败:', error);
                ElMessage.error('操作失败，请稍后重试');
            }
        }
    });
};

// 重置表单
const resetUserForm = () => {
    if (userFormRef.value) {
        userFormRef.value.resetFields();
    }
    userForm.id = '';
    userForm.account = '';
    userForm.phone = '';
    userForm.status = '正常';
    userForm.role = 'common'; // 默认为普通角色
    userForm.remark = '';
    userForm.deptId = 100; // 默认设置部门ID为100
};

onMounted(() => {
    fetchUserList();
});
</script>

<style lang="scss" scoped>
.app-container {
    display: flex;
    width: 100%;
    height: 100%;
    position: relative;
}

/* 左侧菜单样式 */
.left-sidebar {
    position: relative;
    width: 240px;
    display: flex;
    flex-direction: column;
    z-index: 100;
    background-color: #fff;
    border-right: 1px solid rgba(0, 0, 0, 0.08);
    height: 100%;
    min-height: 100vh;
}

.dialog-header {
    width: 100%;
    box-sizing: border-box;
}

.sidebar-menu {
    width: 100%;
    overflow-y: auto;
    padding: 0px;
    background-color: #fff;
    flex: 0 0 auto;
    height: 70vh;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.menu-item {
    margin: 0px;
}

.menu-header {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    color: #555;
}

.menu-header:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.menu-item.active .menu-header {
    // background-color: rgba(255, 152, 0, 0.05);
    background-color: rgba(26, 83, 225, 0.12);
}

.orange-text {
    color: #1A53E1;
    font-weight: 500;
}

.submenu {
    padding: 4px 0 8px 32px;
}

.submenu-item {
    padding: 8px 16px;
    font-size: 13px;
    color: #666;
    cursor: pointer;
    border-radius: 6px;
    margin: 2px 0;
    transition: all 0.3s ease;
}

.submenu-item:hover {
    background-color: rgba(0, 0, 0, 0.03);
    color: #333;
}

.submenu-item.active {
    background-color: rgba(26, 115, 232, 0.08);
    color: #1a73e8;
    font-weight: 500;
}

.collapse-icon {
    transition: transform 0.3s ease;
}

.collapse-icon.collapsed {
    transform: rotate(-90deg);
}

/* 右侧内容样式 */
.right-content {
    flex: 1;
    padding: 16px;
    background-color: #fff;
    overflow-y: auto;
}

.content-header {
    margin-bottom: 16px;
    padding: 0;
    border-bottom: 1px solid #ebeef5;
}

.header-title {
    font-size: 17px;
    font-weight: 600;
    color: #303133;
    padding: 0 0 16px 0;
}

.search-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    padding: 0;
}

.search-input {
    width: 280px;
}

.search-input1 {
    width: 170px;
}

.filter-select {
    width: 120px;
}

.search-buttons {
    display: flex;
    gap: 8px;
}

.button-group {
    margin-left: auto;
    display: flex;
    gap: 10px;
}

.invite-button {
    background-color: #fff;
    border-color: #dcdfe6;
    color: #606266;
}

.delete-button {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #fff;
}

.content-body {
    background-color: #fff;
    padding: 0;
    width: 100%;
}

.el-table {
    width: 100%;
    table-layout: fixed;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-avatar {
    color: #fff;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-text {
    color: #606266;
    background-color: #F5F5F4;
    padding: 1px 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 46px;
    // height: 22px;
    font-size: 12px;
    font-weight: 500;
}

.role-text {
    color: #606266;
    margin-bottom: 4px;
    line-height: 1.4;
}

.operation-text {
    color: #409EFF;
    cursor: pointer;
}

.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-size {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #606266;
    font-size: 14px;
}

.text-danger {
    color: #F56C6C;
}

/* 邀请成员对话框样式 */
.invite-container {
    padding: 10px 0;
}

.invite-tabs {
    display: flex;
    width: 100%;
    gap: 20px;
}

.invite-tab {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    border-radius: 4px;
}

.tab-title {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 8px;
}

.tab-desc {
    color: #a1a1a1;
    font-size: 14px;
    margin-bottom: 16px;
}

.invite-link-box {
    width: 100%;
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 16px;
    word-break: break-all;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.link-text {
    font-size: 12px;
    color: #606266;
}

.copy-link-btn {
    width: 100%;
    background-color: #ffd200;
    border-color: #ffd200;
    color: #333;
}

.qrcode-container {
    width: 150px;
    height: 150px;
    margin-bottom: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.qrcode-img {
    max-width: 100%;
    max-height: 100%;
}

.download-qr-btn {
    width: 100%;
    background-color: #ffd200;
    border-color: #ffd200;
    color: #333;
}

/* 自定义对话框样式 */
:deep(.invite-dialog .el-dialog__header) {
    text-align: center;
    padding: 15px;
    margin-right: 0;
    border-bottom: 1px solid #ebeef5;
}

:deep(.invite-dialog .el-dialog__title) {
    font-size: 16px;
    font-weight: 500;
}

:deep(.invite-dialog .el-dialog__body) {
    padding: 20px 30px;
}

:deep(.invite-dialog .el-dialog__headerbtn) {
    top: 15px;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
    .left-sidebar {
        position: fixed;
        left: 0;
        top: 0;
        bottom: 0;
        width: 280px;
        transform: translateX(-280px);
        transition: transform 0.3s ease;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        z-index: 100;
    }

    .left-sidebar.open {
        transform: translateX(0);
    }

    .invite-tabs {
        flex-direction: column;
    }
}

/* 全局弹窗样式 */
:deep(.el-overlay) {
    --el-dialog-margin-top: 25vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: auto;
    padding: 0;
}

:deep(.invite-dialog.el-dialog) {
    margin-top: 30vh !important;
}

:deep(.el-dialog) {
    margin-top: 25vh !important;
}

/* 用户管理弹窗样式 */
.user-form-container {
    padding: 20px 20px 0;
}

.full-width {
    width: 100%;
}

.radio-group {
    display: flex;
    gap: 20px;
    line-height: 36px;
}

/* 确保表单项标签垂直居中对齐 */
:deep(.el-form-item__label) {
    display: flex;
    align-items: center;
    height: 36px;
    line-height: 36px;
}

/* 自定义对话框样式 */
:deep(.user-dialog .el-dialog__header) {
    text-align: left;
    padding: 15px 20px;
    margin-right: 0;
    border-bottom: 1px solid #f0f0f0;
}

:deep(.user-dialog .el-dialog__title) {
    font-size: 16px;
    font-weight: 500;
}

:deep(.user-dialog .el-dialog__body) {
    padding: 0;
}

:deep(.user-dialog .el-dialog__footer) {
    padding: 10px 20px 20px;
    text-align: right;
    border-top: none;
}

:deep(.user-dialog .el-form-item__label) {
    font-weight: normal;
    color: #606266;
}

:deep(.user-dialog .el-input__inner) {
    border-radius: 4px;
    height: 36px;
}

:deep(.user-dialog .el-form-item.is-required > .el-form-item__label::before) {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
    display: flex;
    align-items: center;
}

:deep(.user-dialog .el-button) {
    padding: 9px 20px;
    border-radius: 4px;
    font-size: 14px;
}

:deep(.user-dialog .el-button--plain) {
    background: transparent;
    border-color: #dcdfe6;
    color: #606266;
}

.confirm-btn {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
}
</style>