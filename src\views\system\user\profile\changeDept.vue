<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px"
      @submit.native.prevent>
      <el-form-item label="部门" prop="deptId">
        <el-input v-model="queryParams.deptName" placeholder="请输入部门" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="sysUserDeptRoleList">
      <el-table-column label="部门ID" width="60" align="center" prop="deptId" />
      <el-table-column label="部门名称" align="center" prop="deptName" />
      <el-table-column label="角色列表" align="left" prop="roleNames" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-tag v-if="scope.row.currentFlag == '1' ">当前部门</el-tag>
          <el-button v-if="scope.row.currentFlag === '0' " size="mini" type="text" icon="el-icon-edit"
            @click="handleChangeDept(scope.row)">切换部门</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script setup>
  import {
    listSysUserDeptRole,
    changeMyDept
  } from "@/api/plus/sysUserDeptRole";
  import useUserStore from "@/store/modules/user";
  const userStore = useUserStore();
  const props = defineProps({
    user: {
      type: Object
    }
  });
  const {
    proxy
  } = getCurrentInstance();
  const sysUserDeptRoleList = ref([]);
  const total = ref(0);
  const showSearch = ref(true);
  const loading = ref(true);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    deptId: null,
  });
  /** 查询用户部门角色列表 */
  function getList() {
    loading.value = true;
    queryParams.value.userId = userStore.id;
    listSysUserDeptRole(queryParams.value).then(response => {
      sysUserDeptRoleList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  };
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryForm");
    handleQuery();
  };
  /** 切换部门 */
  function handleChangeDept(row) {
    const deptId = row.deptId;
    const userId = userStore.id;
    proxy.$modal.confirm('是否确认切换部门？').then(function () {
      changeMyDept({
        userId: userId,
        deptId: deptId
      });
    }).then(() => {
      getList();
      proxy.$modal.loading("部门切换成功，正在清除缓存并刷新，请稍候...");
      setTimeout("window.location.reload()", 2000)
    }).catch(() => {});
  };

  getList();
</script>