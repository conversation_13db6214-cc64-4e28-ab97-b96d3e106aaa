{"name": "knowledge", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@mdi/font": "^7.4.47", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "@vue-office/pptx": "^1.0.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.6.1", "axios": "^1.9.0", "buffer": "^6.0.3", "echarts": "^5.6.0", "element-china-area-data": "^6.1.0", "element-plus": "^2.9.9", "file-saver": "2.0.5", "fuse.js": "6.6.2", "highlight.js": "^11.11.1", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "marked": "^15.0.12", "nprogress": "0.2.0", "pdfjs-dist": "^2.9.359", "pinia": "2.1.7", "qrcodejs2-fix": "^0.0.1", "vite-plugin-remove-console": "^2.2.0", "vue": "^3.5.13", "vue-amazing-ui": "^2.4.14", "vue-cropper": "1.1.1", "vue-demi": "^0.14.10", "vue-pdf-embed": "^1.2.1", "vue-router": "^4.5.1", "vue-wxlogin": "^1.0.5", "vue3-pdfjs": "^0.1.6", "vuetify": "^3.8.0-beta.0", "vuex": "^4.0.2"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "@vue/compiler-sfc": "3.3.9", "sass": "1.69.5", "unplugin-auto-import": "0.17.1", "unplugin-vue-setup-extend-plus": "1.0.0", "vite": "^6.3.5", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}}