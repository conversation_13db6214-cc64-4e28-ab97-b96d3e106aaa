import useUserStore from '@/store/modules/user'

// 获取API基础URL
export function getApiBaseUrl() {
  const userStore = useUserStore();
  return `https://ragflow-${userStore.decryptedApiUrl || ''}`;
}

// 获取API令牌
export function getApiToken() {
  const userStore = useUserStore();
  return `Bearer ${userStore.decryptedApiKey || ''}`;
}

// 固定的ID值
export const CHAT_ID = '767dd04647f711f08b220242ac120006';
export const ENV = 'dev'
export const ENV_BG = 'prod/dev'
// 正式环境：env：prod 
// 测试环境：env: dev
export const DATASET_ID = '85f15b2447f711f0ae280242ac120006'; // 测试知识库id
export const AGENT_ID = '3f7419c4462711f08c3b0242ac120006';  //问数原值：51183ec0347611f0835e0242ac120004/e178376e360a11f08e120242ac120002/f65f81e23a9611f0a2790242ac120005 
export const AGENT_ID_NR = 'e0ab86546c9411f0a5b90242ac120006';  //这个是内容创作的AGENT_ID
export const AGENT_ID_ZC = '5a16d23a50ae11f08a2a0242ac120006';  //这个是内容创作的AGENT_ID_ZC
export const AGENT_ID_BG = 'fc4d18c4530711f0aa190242ac120006';  //这个是创城报告的AGENT_ID_BG
